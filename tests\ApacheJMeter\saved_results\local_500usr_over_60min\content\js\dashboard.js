/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
var showControllersOnly = false;
var seriesFilter = "";
var filtersOnlySampleSeries = true;

/*
 * Add header in statistics table to group metrics by category
 * format
 *
 */
function summaryTableHeader(header) {
    var newRow = header.insertRow(-1);
    newRow.className = "tablesorter-no-sort";
    var cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 1;
    cell.innerHTML = "Requests";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 3;
    cell.innerHTML = "Executions";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 7;
    cell.innerHTML = "Response Times (ms)";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 1;
    cell.innerHTML = "Throughput";
    newRow.appendChild(cell);

    cell = document.createElement('th');
    cell.setAttribute("data-sorter", false);
    cell.colSpan = 2;
    cell.innerHTML = "Network (KB/sec)";
    newRow.appendChild(cell);
}

/*
 * Populates the table identified by id parameter with the specified data and
 * format
 *
 */
function createTable(table, info, formatter, defaultSorts, seriesIndex, headerCreator) {
    var tableRef = table[0];

    // Create header and populate it with data.titles array
    var header = tableRef.createTHead();

    // Call callback is available
    if(headerCreator) {
        headerCreator(header);
    }

    var newRow = header.insertRow(-1);
    for (var index = 0; index < info.titles.length; index++) {
        var cell = document.createElement('th');
        cell.innerHTML = info.titles[index];
        newRow.appendChild(cell);
    }

    var tBody;

    // Create overall body if defined
    if(info.overall){
        tBody = document.createElement('tbody');
        tBody.className = "tablesorter-no-sort";
        tableRef.appendChild(tBody);
        var newRow = tBody.insertRow(-1);
        var data = info.overall.data;
        for(var index=0;index < data.length; index++){
            var cell = newRow.insertCell(-1);
            cell.innerHTML = formatter ? formatter(index, data[index]): data[index];
        }
    }

    // Create regular body
    tBody = document.createElement('tbody');
    tableRef.appendChild(tBody);

    var regexp;
    if(seriesFilter) {
        regexp = new RegExp(seriesFilter, 'i');
    }
    // Populate body with data.items array
    for(var index=0; index < info.items.length; index++){
        var item = info.items[index];
        if((!regexp || filtersOnlySampleSeries && !info.supportsControllersDiscrimination || regexp.test(item.data[seriesIndex]))
                &&
                (!showControllersOnly || !info.supportsControllersDiscrimination || item.isController)){
            if(item.data.length > 0) {
                var newRow = tBody.insertRow(-1);
                for(var col=0; col < item.data.length; col++){
                    var cell = newRow.insertCell(-1);
                    cell.innerHTML = formatter ? formatter(col, item.data[col]) : item.data[col];
                }
            }
        }
    }

    // Add support of columns sort
    table.tablesorter({sortList : defaultSorts});
}

$(document).ready(function() {

    // Customize table sorter default options
    $.extend( $.tablesorter.defaults, {
        theme: 'blue',
        cssInfoBlock: "tablesorter-no-sort",
        widthFixed: true,
        widgets: ['zebra']
    });

    var data = {"OkPercent": 100.0, "KoPercent": 0.0};
    var dataset = [
        {
            "label" : "FAIL",
            "data" : data.KoPercent,
            "color" : "#FF6347"
        },
        {
            "label" : "PASS",
            "data" : data.OkPercent,
            "color" : "#9ACD32"
        }];
    $.plot($("#flot-requests-summary"), dataset, {
        series : {
            pie : {
                show : true,
                radius : 1,
                label : {
                    show : true,
                    radius : 3 / 4,
                    formatter : function(label, series) {
                        return '<div style="font-size:8pt;text-align:center;padding:2px;color:white;">'
                            + label
                            + '<br/>'
                            + Math.round10(series.percent, -2)
                            + '%</div>';
                    },
                    background : {
                        opacity : 0.5,
                        color : '#000'
                    }
                }
            }
        },
        legend : {
            show : true
        }
    });

    // Creates APDEX table
    createTable($("#apdexTable"), {"supportsControllersDiscrimination": true, "overall": {"data": [1.0, 500, 2000, "Total"], "isController": false}, "titles": ["Apdex", "T (Toleration threshold)", "F (Frustration threshold)", "Label"], "items": [{"data": [1.0, 500, 2000, "Get all albums"], "isController": false}, {"data": [1.0, 500, 2000, "Home page"], "isController": true}, {"data": [1.0, 500, 2000, "Play songs from home page"], "isController": false}, {"data": [1.0, 500, 2000, "Trending playlist"], "isController": false}, {"data": [1.0, 500, 2000, "Albums page"], "isController": true}, {"data": [1.0, 500, 2000, "Cover 2"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 1"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 4"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 3"], "isController": false}, {"data": [1.0, 500, 2000, "Play songs from albums"], "isController": false}, {"data": [1.0, 500, 2000, "Cover 5"], "isController": false}, {"data": [1.0, 500, 2000, "Search"], "isController": false}, {"data": [1.0, 500, 2000, "Initial song"], "isController": false}, {"data": [1.0, 500, 2000, "Get paths to songs from each album"], "isController": false}]}, function(index, item){
        switch(index){
            case 0:
                item = item.toFixed(3);
                break;
            case 1:
            case 2:
                item = formatDuration(item);
                break;
        }
        return item;
    }, [[0, 0]], 3);

    // Create statistics table
    createTable($("#statisticsTable"), {"supportsControllersDiscrimination": true, "overall": {"data": ["Total", 10500, 0, 0.0, 14.989428571428588, 2, 180, 4.0, 42.0, 47.0, 58.0, 2.64782424499183, 5294.061796819443, 0.39015783830052553], "isController": false}, "titles": ["Label", "#Samples", "FAIL", "Error %", "Average", "Min", "Max", "Median", "90th pct", "95th pct", "99th pct", "Transactions/s", "Received", "Sent"], "items": [{"data": ["Get all albums", 500, 0, 0.0, 3.906, 2, 16, 4.0, 5.0, 5.0, 7.0, 0.1347578132580127, 0.13515261153904204, 0.01592353066818314], "isController": false}, {"data": ["Home page", 500, 0, 0.0, 65.658, 57, 139, 64.0, 71.0, 81.94999999999999, 97.96000000000004, 0.1391648992724459, 1217.4203826739003, 0.1360391251676937], "isController": true}, {"data": ["Play songs from home page", 1500, 0, 0.0, 44.35133333333333, 31, 180, 43.0, 53.0, 57.0, 75.99000000000001, 0.3985948203931739, 2757.016683383429, 0.06627105992341666], "isController": false}, {"data": ["Trending playlist", 500, 0, 0.0, 6.600000000000001, 4, 77, 6.0, 9.0, 10.0, 12.990000000000009, 0.13917078704976857, 0.9466059880875373, 0.01821180221159081], "isController": false}, {"data": ["Albums page", 500, 0, 0.0, 20.02600000000001, 14, 43, 19.0, 23.900000000000034, 25.0, 37.99000000000001, 0.1347572684701681, 1.974904617120911, 0.11988659333625308], "isController": true}, {"data": ["Cover 2", 500, 0, 0.0, 3.9740000000000006, 2, 11, 4.0, 6.0, 6.0, 8.990000000000009, 0.13919825701510496, 63.51042818532244, 0.018895075903417567], "isController": false}, {"data": ["Cover 1", 500, 0, 0.0, 3.7000000000000006, 2, 8, 3.0, 5.0, 6.0, 7.990000000000009, 0.13919825701510496, 52.65432349612291, 0.018895075903417567], "isController": false}, {"data": ["Cover 4", 500, 0, 0.0, 3.607999999999999, 2, 13, 3.0, 5.0, 6.0, 7.990000000000009, 0.13919837327213058, 61.14274952387196, 0.01889509168440054], "isController": false}, {"data": ["Cover 3", 500, 0, 0.0, 2.8899999999999983, 2, 9, 3.0, 4.0, 5.0, 6.0, 0.13919829576742526, 43.55927919503296, 0.018895081163742295], "isController": false}, {"data": ["Play songs from albums", 1000, 0, 0.0, 40.244, 33, 77, 39.0, 43.0, 51.0, 67.99000000000001, 0.26593686499262953, 1657.0001081698197, 0.0442621266711805], "isController": false}, {"data": ["Cover 5", 500, 0, 0.0, 3.454000000000002, 2, 9, 3.0, 5.0, 6.0, 8.0, 0.1391987995495527, 49.15729481748949, 0.018895149548230294], "isController": false}, {"data": ["Search", 1500, 0, 0.0, 5.18400000000001, 2, 22, 5.0, 7.0, 8.0, 10.0, 0.41736227045075125, 1.7519161710489706, 0.05502334620200334], "isController": false}, {"data": ["Initial song", 500, 0, 0.0, 41.43199999999999, 35, 72, 40.0, 44.0, 49.0, 67.99000000000001, 0.13919659068574367, 946.7302509878086, 0.023380677341746006], "isController": false}, {"data": ["Get paths to songs from each album", 2500, 0, 0.0, 3.223999999999997, 2, 16, 3.0, 4.0, 5.0, 7.0, 0.6737872503281345, 1.8397550311693982, 0.10396326714047387], "isController": false}]}, function(index, item){
        switch(index){
            // Errors pct
            case 3:
                item = item.toFixed(2) + '%';
                break;
            // Mean
            case 4:
            // Mean
            case 7:
            // Median
            case 8:
            // Percentile 1
            case 9:
            // Percentile 2
            case 10:
            // Percentile 3
            case 11:
            // Throughput
            case 12:
            // Kbytes/s
            case 13:
            // Sent Kbytes/s
                item = item.toFixed(2);
                break;
        }
        return item;
    }, [[0, 0]], 0, summaryTableHeader);

    // Create error table
    createTable($("#errorsTable"), {"supportsControllersDiscrimination": false, "titles": ["Type of error", "Number of errors", "% in errors", "% in all samples"], "items": []}, function(index, item){
        switch(index){
            case 2:
            case 3:
                item = item.toFixed(2) + '%';
                break;
        }
        return item;
    }, [[1, 1]]);

        // Create top5 errors by sampler
    createTable($("#top5ErrorsBySamplerTable"), {"supportsControllersDiscrimination": false, "overall": {"data": ["Total", 10500, 0, "", "", "", "", "", "", "", "", "", ""], "isController": false}, "titles": ["Sample", "#Samples", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors", "Error", "#Errors"], "items": [{"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}, {"data": [], "isController": false}]}, function(index, item){
        return item;
    }, [[0, 0]], 0);

});
