import { usePricesGet } from "@/hooks/usePrices";

export function useTotalPrice(
  startDate: Date,
  endDate: Date,
  people: number,
  children: number,
  dog: number,
  tent: number,
  car: number,
  moto: number,
  caravan: number,
  electricity: boolean
) {
  const { data: price } = usePricesGet();
  if (!price) {
    return 0;
  }

  const days = Math.ceil(
    (new Date(endDate).getTime() - new Date(startDate).getTime()) /
      (1000 * 60 * 60 * 24)
  );

  return (
    (price.people * people +
      price.children * children +
      price.dog * dog +
      price.tent * tent +
      price.car * car +
      price.moto * moto +
      price.caravan * caravan +
      (electricity ? price.electricity : 0)) *
    days
  );
}
