import { useTranslation } from "react-i18next";
import { z } from "zod";
import { useForm, type ControllerRenderProps } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./ui/form";
import { Input } from "./ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "./ui/button";
import { DatePicker } from "./ui/datepicker";
import { Checkbox } from "./ui/checkbox";
import { Textarea } from "./ui/textarea";
import { useReservationUpdate } from "@/hooks/useReservation";
import { toast } from "sonner";
import ReservationFormSchema from "@/validation/schemas/ReservationFormSchema";
import { useTotalPrice } from "@/counting/total-price";
import type { Reservation } from "@/types";

interface AdminReservationFormProps {
  reservation?: Reservation;
}

function AdminReservationForm({ reservation }: AdminReservationFormProps) {
  const { t } = useTranslation();
  const updateMutation = useReservationUpdate();
  const FormSchema = ReservationFormSchema()();
  type FormSchemaType = z.infer<typeof FormSchema>;

  // TODO calendar should allow to choose any date for admin

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    try {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { gdpr, terms_and_conditions, ...reservationData } = data;

      if (reservation) {
        const token = localStorage.getItem("adminToken");
        if (!token) {
          toast.error("Chyba autentifikace");
          return;
        }
        await updateMutation.mutateAsync({
          id: reservation.id.toString(),
          token,
          reservation: reservationData,
        });
        toast.success("Rezervace byla úspěšně aktualizována");
      }
    } catch (error) {
      toast.error("Chyba při aktualizaci rezervace");
      console.error("Error with reservation:", error);
    }
  };

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      startDate: reservation?.startDate
        ? new Date(reservation.startDate)
        : new Date(),
      endDate: reservation?.endDate
        ? new Date(reservation.endDate)
        : (() => {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            return tomorrow;
          })(),

      name: reservation?.name || "",
      surname: reservation?.surname || "",
      email: reservation?.email || "",
      phone: reservation?.phone || "",

      car: reservation?.car || 0,
      moto: reservation?.moto || 0,
      caravan: reservation?.caravan || 0,

      people: reservation?.people || 0,
      children: reservation?.children || 0,
      dog: reservation?.dog || 0,
      tent: reservation?.tent || 0,
      electricity: reservation?.electricity || false,

      street: reservation?.street || "",
      zip: reservation?.zip || "",
      city: reservation?.city || "",
      country: reservation?.country || "",

      note: reservation?.note || "",

      // Admin forms don't show these fields, but schema requires them
      gdpr: true,
      terms_and_conditions: true,
    },
  });

  // Handle start date change with automatic end date adjustment
  const handleStartDateChange = (date: Date | undefined) => {
    // Update the start date field
    form.setValue("startDate", date || new Date());

    if (date) {
      const currentEndDate = form.getValues("endDate");
      const requiredEndDate = new Date(date);
      requiredEndDate.setDate(requiredEndDate.getDate() + 1);

      // If current end date is not at least 1 day after the new start date, update it
      if (!currentEndDate || currentEndDate < requiredEndDate) {
        form.setValue("endDate", requiredEndDate);
      }
    }
  };

  const FormItemString = (
    label: string,
    placeholder: string,
    field: ControllerRenderProps<
      FormSchemaType,
      | "name"
      | "surname"
      | "email"
      | "phone"
      | "street"
      | "zip"
      | "city"
      | "country"
      | "note"
    >
  ) => {
    return (
      <FormItem className="flex-1">
        <FormLabel>{label}*</FormLabel>
        <FormControl>
          <Input placeholder={placeholder} {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    );
  };

  const FormItemNumber = (
    label: string,
    placeholder: string,
    field: ControllerRenderProps<
      FormSchemaType,
      "car" | "moto" | "caravan" | "people" | "children" | "dog" | "tent"
    >
  ) => {
    return (
      <FormItem className="flex-1">
        <FormLabel>{label}</FormLabel>
        <FormControl>
          <Input type="number" placeholder={placeholder} {...field} min={0} />
        </FormControl>
        <FormMessage />
      </FormItem>
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-4 mx-auto p-6 border-2"
      >
        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Start Date */}
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>{t("start_date")}*</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={handleStartDateChange}
                    placeholder={t("select_start_date")}
                    minDate={new Date()} // Prevent selecting past dates
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* End Date */}
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>{t("end_date")}*</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t("select_end_date")}
                    minDate={(() => {
                      const startDate = form.watch("startDate");
                      if (startDate) {
                        const minEndDate = new Date(startDate);
                        minEndDate.setDate(minEndDate.getDate() + 1);
                        return minEndDate;
                      }
                      const tomorrow = new Date();
                      tomorrow.setDate(tomorrow.getDate() + 1);
                      return tomorrow;
                    })()} // End date must be at least 1 day after start date
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* People */}
          <FormField
            control={form.control}
            name="people"
            render={({ field }) =>
              FormItemNumber(
                t("people_older_than_6_years"),
                t("enter_number_of_people"),
                field
              )
            }
          />

          {/* Children */}
          <FormField
            control={form.control}
            name="children"
            render={({ field }) =>
              FormItemNumber(
                t("children_under_6_years"),
                t("enter_number_of_children"),
                field
              )
            }
          />
        </div>

        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) =>
              FormItemString(t("name"), t("enter_name"), field)
            }
          />

          {/* Surname */}
          <FormField
            control={form.control}
            name="surname"
            render={({ field }) =>
              FormItemString(t("surname"), t("enter_surname"), field)
            }
          />

          {/* Email */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) =>
              FormItemString(t("Email"), t("enter_email"), field)
            }
          />

          {/* Phone */}
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) =>
              FormItemString(t("phone"), t("enter_phone"), field)
            }
          />
        </div>

        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Street */}
          <FormField
            control={form.control}
            name="street"
            render={({ field }) =>
              FormItemString(t("street"), t("enter_street"), field)
            }
          />

          {/* Zip */}
          <FormField
            control={form.control}
            name="zip"
            render={({ field }) =>
              FormItemString(t("zip"), t("enter_zip"), field)
            }
          />

          {/* City */}
          <FormField
            control={form.control}
            name="city"
            render={({ field }) =>
              FormItemString(t("city"), t("enter_city"), field)
            }
          />

          {/* Country */}
          <FormField
            control={form.control}
            name="country"
            render={({ field }) =>
              FormItemString(t("country"), t("enter_country"), field)
            }
          />
        </div>

        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Dog */}
          <FormField
            control={form.control}
            name="dog"
            render={({ field }) =>
              FormItemNumber(t("dog"), t("enter_number_of_dogs"), field)
            }
          />

          {/* Car */}
          <FormField
            control={form.control}
            name="car"
            render={({ field }) =>
              FormItemNumber(t("car"), t("enter_number_of_cars"), field)
            }
          />

          {/* Motorcycle */}
          <FormField
            control={form.control}
            name="moto"
            render={({ field }) =>
              FormItemNumber(t("moto"), t("enter_number_of_motos"), field)
            }
          />

          {/* Tent */}
          <FormField
            control={form.control}
            name="tent"
            render={({ field }) =>
              FormItemNumber(t("tent"), t("enter_number_of_tents"), field)
            }
          />

          {/* Caravan */}
          <FormField
            control={form.control}
            name="caravan"
            render={({ field }) =>
              FormItemNumber(t("caravan"), t("enter_number_of_caravans"), field)
            }
          />

          {/* Electricity */}
          <FormField
            control={form.control}
            name="electricity"
            render={({ field }) => (
              <FormItem className="flex flex-col items-start gap-3 h-full">
                <FormLabel>{t("electricity_connection")}</FormLabel>
                <FormControl className="h-6 w-6 flex justify-center items-center">
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Note */}
        <FormField
          control={form.control}
          name="note"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("note")}</FormLabel>
              <FormControl>
                <Textarea placeholder={t("enter_note")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex flex-col md:flex-row gap-2 relative">
          <Button type="submit" className="w-1/2 mx-auto h-10">
            Uložit{" "}
          </Button>
          <p className="md:absolute top-2 right-3">
            {t("total_price")}:{" "}
            {useTotalPrice(
              form.watch("startDate"),
              form.watch("endDate"),
              form.watch("people"),
              form.watch("children"),
              form.watch("dog"),
              form.watch("tent"),
              form.watch("car"),
              form.watch("moto"),
              form.watch("caravan"),
              form.watch("electricity")
            )}
            {" CZK"}
          </p>
        </div>
      </form>
    </Form>
  );
}

export default AdminReservationForm;
