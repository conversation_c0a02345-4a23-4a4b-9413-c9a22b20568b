import { useTranslation } from "react-i18next";
import { z } from "zod";
import { useForm, type ControllerRenderProps } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "./ui/form";
import { Input } from "./ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "./ui/button";
import { DatePicker } from "./ui/datepicker";
import { Checkbox } from "./ui/checkbox";
import { Textarea } from "./ui/textarea";
import { useReservationPost } from "@/hooks/useReservation";
import { toast } from "sonner";
import ReservationFormSchema from "@/validation/schemas/ReservationFormSchema";
import { useTotalPrice } from "@/counting/total-price";

function ReservationForm() {
  const { t } = useTranslation();
  const reservationMutation = useReservationPost();
  const FormSchema = ReservationFormSchema()();
  type FormSchemaType = z.infer<typeof FormSchema>;

  const onSubmit = async (data: z.infer<typeof FormSchema>) => {
    try {
      // TODO: here belongs redirection to payment gateway
      await reservationMutation.mutateAsync(data);
      toast.success("Rezervace byla úspěšně vytvořena");
    } catch (error) {
      toast.error("Chyba při vytváření rezervace");
      console.error("Error creating reservation:", error);
    }
  };

  const form = useForm<FormSchemaType>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      startDate: new Date(),
      endDate: new Date(),

      name: "",
      surname: "",
      email: "",
      phone: "",

      car: 0,
      moto: 0,
      caravan: 0,

      people: 0,
      children: 0,
      dog: 0,
      tent: 0,
      electricity: false,

      street: "",
      zip: "",
      city: "",
      country: "",

      note: "",
    },
  });

  // Handle start date change with automatic end date adjustment
  const handleStartDateChange = (date: Date | undefined) => {
    // Update the start date field
    form.setValue("startDate", date || new Date());

    if (date) {
      const currentEndDate = form.getValues("endDate");

      // If start date is after current end date, update end date to start date
      if (currentEndDate && date > currentEndDate) {
        // const newEndDate = new Date(date);
        date.setDate(date.getDate() + 1);
        form.setValue("endDate", date);
      }
    }
  };

  const FormItemString = (
    label: string,
    placeholder: string,
    field: ControllerRenderProps<
      FormSchemaType,
      | "name"
      | "surname"
      | "email"
      | "phone"
      | "street"
      | "zip"
      | "city"
      | "country"
      | "note"
    >
  ) => {
    return (
      <FormItem className="flex-1">
        <FormLabel>{label}*</FormLabel>
        <FormControl>
          <Input placeholder={placeholder} {...field} />
        </FormControl>
        <FormMessage />
      </FormItem>
    );
  };

  const FormItemNumber = (
    label: string,
    placeholder: string,
    field: ControllerRenderProps<
      FormSchemaType,
      "car" | "moto" | "caravan" | "people" | "children" | "dog" | "tent"
    >
  ) => {
    return (
      <FormItem className="flex-1">
        <FormLabel>{label}</FormLabel>
        <FormControl>
          <Input type="number" placeholder={placeholder} {...field} min={0} />
        </FormControl>
        <FormMessage />
      </FormItem>
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-4 mx-auto p-6 border-2"
      >
        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Start Date */}
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>{t("start_date")}*</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={handleStartDateChange}
                    placeholder={t("select_start_date")}
                    minDate={new Date()} // Prevent selecting past dates
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* End Date */}
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem className="flex-1">
                <FormLabel>{t("end_date")}*</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value}
                    onChange={field.onChange}
                    placeholder={t("select_end_date")}
                    minDate={form.watch("startDate") || new Date()} // End date must be after start date
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* People */}
          <FormField
            control={form.control}
            name="people"
            render={({ field }) =>
              FormItemNumber(
                t("people_older_than_6_years"),
                t("enter_number_of_people"),
                field
              )
            }
          />

          {/* Children */}
          <FormField
            control={form.control}
            name="children"
            render={({ field }) =>
              FormItemNumber(
                t("children_under_6_years"),
                t("enter_number_of_children"),
                field
              )
            }
          />
        </div>

        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) =>
              FormItemString(t("name"), t("enter_name"), field)
            }
          />

          {/* Surname */}
          <FormField
            control={form.control}
            name="surname"
            render={({ field }) =>
              FormItemString(t("surname"), t("enter_surname"), field)
            }
          />

          {/* Email */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) =>
              FormItemString(t("Email"), t("enter_email"), field)
            }
          />

          {/* Phone */}
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) =>
              FormItemString(t("phone"), t("enter_phone"), field)
            }
          />
        </div>

        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Street */}
          <FormField
            control={form.control}
            name="street"
            render={({ field }) =>
              FormItemString(t("street"), t("enter_street"), field)
            }
          />

          {/* Zip */}
          <FormField
            control={form.control}
            name="zip"
            render={({ field }) =>
              FormItemString(t("zip"), t("enter_zip"), field)
            }
          />

          {/* City */}
          <FormField
            control={form.control}
            name="city"
            render={({ field }) =>
              FormItemString(t("city"), t("enter_city"), field)
            }
          />

          {/* Country */}
          <FormField
            control={form.control}
            name="country"
            render={({ field }) =>
              FormItemString(t("country"), t("enter_country"), field)
            }
          />
        </div>

        <div className="flex flex-col md:flex-row md:items-start gap-4">
          {/* Dog */}
          <FormField
            control={form.control}
            name="dog"
            render={({ field }) =>
              FormItemNumber(t("dog"), t("enter_number_of_dogs"), field)
            }
          />

          {/* Car */}
          <FormField
            control={form.control}
            name="car"
            render={({ field }) =>
              FormItemNumber(t("car"), t("enter_number_of_cars"), field)
            }
          />

          {/* Motorcycle */}
          <FormField
            control={form.control}
            name="moto"
            render={({ field }) =>
              FormItemNumber(t("moto"), t("enter_number_of_motos"), field)
            }
          />

          {/* Tent */}
          <FormField
            control={form.control}
            name="tent"
            render={({ field }) =>
              FormItemNumber(t("tent"), t("enter_number_of_tents"), field)
            }
          />

          {/* Caravan */}
          <FormField
            control={form.control}
            name="caravan"
            render={({ field }) =>
              FormItemNumber(t("caravan"), t("enter_number_of_caravans"), field)
            }
          />

          {/* Electricity */}
          <FormField
            control={form.control}
            name="electricity"
            render={({ field }) => (
              <FormItem className="flex flex-col items-start gap-3 h-full">
                <FormLabel>{t("electricity_connection")}</FormLabel>
                <FormControl className="h-6 w-6 flex justify-center items-center">
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Note */}
        <FormField
          control={form.control}
          name="note"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t("note")}</FormLabel>
              <FormControl>
                <Textarea placeholder={t("enter_note")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* GDPR */}
        <FormField
          control={form.control}
          name="gdpr"
          render={({ field }) => (
            <FormItem className="flex flex-col items-start gap-3 h-full">
              <div className="flex flex-row items-start gap-3">
                <FormControl className="h-6 w-6">
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <p>{t("gdpr_text")}*</p>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Terms and Conditions */}
        <FormField
          control={form.control}
          name="terms_and_conditions"
          render={({ field }) => (
            <FormItem className="flex flex-col items-start gap-3 h-full">
              <div className="flex flex-row items-start gap-3">
                <FormControl className="h-6 w-6 flex justify-center items-center">
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <p>
                  {t("terms_and_conditions_text_1")}
                  <a
                    href="https://www.sindelova.cz/obcan/kemp-na-tajchu/"
                    className="underline"
                  >
                    {t("terms_and_conditions_text_2")}
                  </a>
                  {t("terms_and_conditions_text_3")}*
                </p>
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex flex-col md:flex-row gap-2 relative">
          <Button type="submit" className="w-1/2 mx-auto h-10">
            {t("submit_reservation")}
          </Button>
          <p className="md:absolute top-2 right-3">
            {t("total_price")}:{" "}
            {useTotalPrice(
              form.watch("startDate"),
              form.watch("endDate"),
              form.watch("people"),
              form.watch("children"),
              form.watch("dog"),
              form.watch("tent"),
              form.watch("car"),
              form.watch("moto"),
              form.watch("caravan"),
              form.watch("electricity")
            )}
            {" CZK"}
          </p>
        </div>
      </form>
    </Form>
  );
}

export default ReservationForm;
