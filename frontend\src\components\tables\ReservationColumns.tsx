import { useTotalPrice } from "@/counting/total-price";
import type { Reservation } from "@/types";
import { type ColumnDef } from "@tanstack/react-table";
import { Button } from "../ui/button";
import { useNavigate } from "react-router-dom";
import { Pencil, Trash2 } from "lucide-react";
import {
  Dialog,
  DialogTrigger,
} from "@/components/ui/dialog";
import DeleteDialog from "../DeleteDialog";

function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${day}. ${month}. ${year}`;
}

export const reservationColumns: ColumnDef<Reservation>[] = [
  {
    accessorKey: "startDate",
    header: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
    cell: ({ row }) => {
      return formatDate(new Date(row.original.startDate));
    },
  },
  {
    accessorKey: "endDate",
    header: "Konec",
    cell: ({ row }) => {
      return formatDate(new Date(row.original.endDate));
    },
  },
  {
    accessorKey: "name",
    header: "Jméno",
  },
  {
    accessorKey: "surname",
    header: "Příjmení",
  },
  {
    id: "people",
    header: "Lidí celkem",
    cell: ({ row }) => {
      const reservation = row.original;
      const people = reservation.people + reservation.children;

      return people;
    },
  },
  {
    id: "price",
    header: "Cena celkem",
    cell: ({ row }) => {
      const reservation = row.original;
      const Result = () => {
        const total_price = useTotalPrice(
          reservation.startDate,
          reservation.endDate,
          reservation.people,
          reservation.children,
          reservation.dog,
          reservation.tent,
          reservation.car,
          reservation.moto,
          reservation.caravan,
          reservation.electricity
        );

        return <p>{total_price}</p>;
      };
      return <Result />;
    },
  },
  // TODO implement actions - edit, delete
  {
    id: "actions",
    header: "Operace",
    cell: ({ row }) => {
      const reservation = row.original;

      const ActionButtons = ({ reservation }: { reservation: Reservation }) => {
        // const { handleApprove, handleDelete } = useProjectActions(project);
        const navigate = useNavigate();

        return (
          <div className="flex flex-row justify-center gap-1 my-2">
            <Button
              className="w-1 m-0"
              onClick={() => navigate(`./${reservation?.id}`)}
            >
              <Pencil />
            </Button>
            <Dialog>
              <DialogTrigger>
                <Button
                  variant="destructive"
                  // onClick={handleDelete}
                  className="w-1 m-0"
                >
                  <Trash2 />
                </Button>
              </DialogTrigger>
              <DeleteDialog />
            </Dialog>
          </div>
        );
      };

      return <ActionButtons reservation={reservation} />;
    },
  },
];
