/*
   Licensed to the Apache Software Foundation (ASF) under one or more
   contributor license agreements.  See the NOTICE file distributed with
   this work for additional information regarding copyright ownership.
   The ASF licenses this file to You under the Apache License, Version 2.0
   (the "License"); you may not use this file except in compliance with
   the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing, software
   distributed under the License is distributed on an "AS IS" BASIS,
   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   See the License for the specific language governing permissions and
   limitations under the License.
*/
$(document).ready(function() {

    $(".click-title").mouseenter( function(    e){
        e.preventDefault();
        this.style.cursor="pointer";
    });
    $(".click-title").mousedown( function(event){
        event.preventDefault();
    });

    // Ugly code while this script is shared among several pages
    try{
        refreshHitsPerSecond(true);
    } catch(e){}
    try{
        refreshResponseTimeOverTime(true);
    } catch(e){}
    try{
        refreshResponseTimePercentiles();
    } catch(e){}
});


var responseTimePercentilesInfos = {
        data: {"result": {"minY": 2.0, "minX": 0.0, "maxY": 180.0, "series": [{"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 3.0], [0.7, 3.0], [0.8, 3.0], [0.9, 3.0], [1.0, 3.0], [1.1, 3.0], [1.2, 3.0], [1.3, 3.0], [1.4, 3.0], [1.5, 3.0], [1.6, 3.0], [1.7, 3.0], [1.8, 3.0], [1.9, 3.0], [2.0, 3.0], [2.1, 3.0], [2.2, 3.0], [2.3, 3.0], [2.4, 3.0], [2.5, 3.0], [2.6, 3.0], [2.7, 3.0], [2.8, 3.0], [2.9, 3.0], [3.0, 3.0], [3.1, 3.0], [3.2, 3.0], [3.3, 3.0], [3.4, 3.0], [3.5, 3.0], [3.6, 3.0], [3.7, 3.0], [3.8, 3.0], [3.9, 3.0], [4.0, 3.0], [4.1, 3.0], [4.2, 3.0], [4.3, 3.0], [4.4, 3.0], [4.5, 3.0], [4.6, 3.0], [4.7, 3.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 4.0], [37.7, 4.0], [37.8, 4.0], [37.9, 4.0], [38.0, 4.0], [38.1, 4.0], [38.2, 4.0], [38.3, 4.0], [38.4, 4.0], [38.5, 4.0], [38.6, 4.0], [38.7, 4.0], [38.8, 4.0], [38.9, 4.0], [39.0, 4.0], [39.1, 4.0], [39.2, 4.0], [39.3, 4.0], [39.4, 4.0], [39.5, 4.0], [39.6, 4.0], [39.7, 4.0], [39.8, 4.0], [39.9, 4.0], [40.0, 4.0], [40.1, 4.0], [40.2, 4.0], [40.3, 4.0], [40.4, 4.0], [40.5, 4.0], [40.6, 4.0], [40.7, 4.0], [40.8, 4.0], [40.9, 4.0], [41.0, 4.0], [41.1, 4.0], [41.2, 4.0], [41.3, 4.0], [41.4, 4.0], [41.5, 4.0], [41.6, 4.0], [41.7, 4.0], [41.8, 4.0], [41.9, 4.0], [42.0, 4.0], [42.1, 4.0], [42.2, 4.0], [42.3, 4.0], [42.4, 4.0], [42.5, 4.0], [42.6, 4.0], [42.7, 4.0], [42.8, 4.0], [42.9, 4.0], [43.0, 4.0], [43.1, 4.0], [43.2, 4.0], [43.3, 4.0], [43.4, 4.0], [43.5, 4.0], [43.6, 4.0], [43.7, 4.0], [43.8, 4.0], [43.9, 4.0], [44.0, 4.0], [44.1, 4.0], [44.2, 4.0], [44.3, 4.0], [44.4, 4.0], [44.5, 4.0], [44.6, 4.0], [44.7, 4.0], [44.8, 4.0], [44.9, 4.0], [45.0, 4.0], [45.1, 4.0], [45.2, 4.0], [45.3, 4.0], [45.4, 4.0], [45.5, 4.0], [45.6, 4.0], [45.7, 4.0], [45.8, 4.0], [45.9, 4.0], [46.0, 4.0], [46.1, 4.0], [46.2, 4.0], [46.3, 4.0], [46.4, 4.0], [46.5, 4.0], [46.6, 4.0], [46.7, 4.0], [46.8, 4.0], [46.9, 4.0], [47.0, 4.0], [47.1, 4.0], [47.2, 4.0], [47.3, 4.0], [47.4, 4.0], [47.5, 4.0], [47.6, 4.0], [47.7, 4.0], [47.8, 4.0], [47.9, 4.0], [48.0, 4.0], [48.1, 4.0], [48.2, 4.0], [48.3, 4.0], [48.4, 4.0], [48.5, 4.0], [48.6, 4.0], [48.7, 4.0], [48.8, 4.0], [48.9, 4.0], [49.0, 4.0], [49.1, 4.0], [49.2, 4.0], [49.3, 4.0], [49.4, 4.0], [49.5, 4.0], [49.6, 4.0], [49.7, 4.0], [49.8, 4.0], [49.9, 4.0], [50.0, 4.0], [50.1, 4.0], [50.2, 4.0], [50.3, 4.0], [50.4, 4.0], [50.5, 4.0], [50.6, 4.0], [50.7, 4.0], [50.8, 4.0], [50.9, 4.0], [51.0, 4.0], [51.1, 4.0], [51.2, 4.0], [51.3, 4.0], [51.4, 4.0], [51.5, 4.0], [51.6, 4.0], [51.7, 4.0], [51.8, 4.0], [51.9, 4.0], [52.0, 4.0], [52.1, 4.0], [52.2, 4.0], [52.3, 4.0], [52.4, 4.0], [52.5, 4.0], [52.6, 4.0], [52.7, 4.0], [52.8, 4.0], [52.9, 4.0], [53.0, 4.0], [53.1, 4.0], [53.2, 4.0], [53.3, 4.0], [53.4, 4.0], [53.5, 4.0], [53.6, 4.0], [53.7, 4.0], [53.8, 4.0], [53.9, 4.0], [54.0, 4.0], [54.1, 4.0], [54.2, 4.0], [54.3, 4.0], [54.4, 4.0], [54.5, 4.0], [54.6, 4.0], [54.7, 4.0], [54.8, 4.0], [54.9, 4.0], [55.0, 4.0], [55.1, 4.0], [55.2, 4.0], [55.3, 4.0], [55.4, 4.0], [55.5, 4.0], [55.6, 4.0], [55.7, 4.0], [55.8, 4.0], [55.9, 4.0], [56.0, 4.0], [56.1, 4.0], [56.2, 4.0], [56.3, 4.0], [56.4, 4.0], [56.5, 4.0], [56.6, 4.0], [56.7, 4.0], [56.8, 4.0], [56.9, 4.0], [57.0, 4.0], [57.1, 4.0], [57.2, 4.0], [57.3, 4.0], [57.4, 4.0], [57.5, 4.0], [57.6, 4.0], [57.7, 4.0], [57.8, 4.0], [57.9, 4.0], [58.0, 4.0], [58.1, 4.0], [58.2, 4.0], [58.3, 4.0], [58.4, 4.0], [58.5, 4.0], [58.6, 4.0], [58.7, 4.0], [58.8, 4.0], [58.9, 4.0], [59.0, 4.0], [59.1, 4.0], [59.2, 4.0], [59.3, 4.0], [59.4, 4.0], [59.5, 4.0], [59.6, 4.0], [59.7, 4.0], [59.8, 4.0], [59.9, 4.0], [60.0, 4.0], [60.1, 4.0], [60.2, 4.0], [60.3, 4.0], [60.4, 4.0], [60.5, 4.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 6.0], [95.7, 6.0], [95.8, 6.0], [95.9, 6.0], [96.0, 6.0], [96.1, 6.0], [96.2, 6.0], [96.3, 6.0], [96.4, 6.0], [96.5, 6.0], [96.6, 6.0], [96.7, 6.0], [96.8, 6.0], [96.9, 6.0], [97.0, 6.0], [97.1, 6.0], [97.2, 6.0], [97.3, 6.0], [97.4, 6.0], [97.5, 6.0], [97.6, 6.0], [97.7, 6.0], [97.8, 6.0], [97.9, 6.0], [98.0, 6.0], [98.1, 6.0], [98.2, 6.0], [98.3, 6.0], [98.4, 6.0], [98.5, 6.0], [98.6, 6.0], [98.7, 6.0], [98.8, 7.0], [98.9, 7.0], [99.0, 7.0], [99.1, 7.0], [99.2, 7.0], [99.3, 7.0], [99.4, 11.0], [99.5, 11.0], [99.6, 13.0], [99.7, 13.0], [99.8, 16.0], [99.9, 16.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[0.0, 57.0], [0.1, 57.0], [0.2, 57.0], [0.3, 57.0], [0.4, 58.0], [0.5, 58.0], [0.6, 58.0], [0.7, 58.0], [0.8, 58.0], [0.9, 58.0], [1.0, 58.0], [1.1, 58.0], [1.2, 58.0], [1.3, 58.0], [1.4, 58.0], [1.5, 58.0], [1.6, 58.0], [1.7, 58.0], [1.8, 58.0], [1.9, 58.0], [2.0, 58.0], [2.1, 58.0], [2.2, 59.0], [2.3, 59.0], [2.4, 59.0], [2.5, 59.0], [2.6, 59.0], [2.7, 59.0], [2.8, 59.0], [2.9, 59.0], [3.0, 59.0], [3.1, 59.0], [3.2, 59.0], [3.3, 59.0], [3.4, 59.0], [3.5, 59.0], [3.6, 59.0], [3.7, 59.0], [3.8, 59.0], [3.9, 59.0], [4.0, 59.0], [4.1, 59.0], [4.2, 59.0], [4.3, 59.0], [4.4, 59.0], [4.5, 59.0], [4.6, 59.0], [4.7, 59.0], [4.8, 59.0], [4.9, 59.0], [5.0, 59.0], [5.1, 59.0], [5.2, 59.0], [5.3, 59.0], [5.4, 59.0], [5.5, 59.0], [5.6, 59.0], [5.7, 59.0], [5.8, 59.0], [5.9, 59.0], [6.0, 59.0], [6.1, 59.0], [6.2, 59.0], [6.3, 59.0], [6.4, 59.0], [6.5, 59.0], [6.6, 59.0], [6.7, 59.0], [6.8, 60.0], [6.9, 60.0], [7.0, 60.0], [7.1, 60.0], [7.2, 60.0], [7.3, 60.0], [7.4, 60.0], [7.5, 60.0], [7.6, 60.0], [7.7, 60.0], [7.8, 60.0], [7.9, 60.0], [8.0, 60.0], [8.1, 60.0], [8.2, 60.0], [8.3, 60.0], [8.4, 60.0], [8.5, 60.0], [8.6, 60.0], [8.7, 60.0], [8.8, 60.0], [8.9, 60.0], [9.0, 60.0], [9.1, 60.0], [9.2, 60.0], [9.3, 60.0], [9.4, 60.0], [9.5, 60.0], [9.6, 60.0], [9.7, 60.0], [9.8, 60.0], [9.9, 60.0], [10.0, 60.0], [10.1, 60.0], [10.2, 60.0], [10.3, 60.0], [10.4, 60.0], [10.5, 60.0], [10.6, 60.0], [10.7, 60.0], [10.8, 60.0], [10.9, 60.0], [11.0, 60.0], [11.1, 60.0], [11.2, 60.0], [11.3, 60.0], [11.4, 60.0], [11.5, 60.0], [11.6, 60.0], [11.7, 60.0], [11.8, 60.0], [11.9, 60.0], [12.0, 60.0], [12.1, 60.0], [12.2, 60.0], [12.3, 60.0], [12.4, 60.0], [12.5, 60.0], [12.6, 60.0], [12.7, 60.0], [12.8, 60.0], [12.9, 60.0], [13.0, 60.0], [13.1, 60.0], [13.2, 60.0], [13.3, 60.0], [13.4, 60.0], [13.5, 60.0], [13.6, 60.0], [13.7, 60.0], [13.8, 60.0], [13.9, 60.0], [14.0, 60.0], [14.1, 60.0], [14.2, 61.0], [14.3, 61.0], [14.4, 61.0], [14.5, 61.0], [14.6, 61.0], [14.7, 61.0], [14.8, 61.0], [14.9, 61.0], [15.0, 61.0], [15.1, 61.0], [15.2, 61.0], [15.3, 61.0], [15.4, 61.0], [15.5, 61.0], [15.6, 61.0], [15.7, 61.0], [15.8, 61.0], [15.9, 61.0], [16.0, 61.0], [16.1, 61.0], [16.2, 61.0], [16.3, 61.0], [16.4, 61.0], [16.5, 61.0], [16.6, 61.0], [16.7, 61.0], [16.8, 61.0], [16.9, 61.0], [17.0, 61.0], [17.1, 61.0], [17.2, 61.0], [17.3, 61.0], [17.4, 61.0], [17.5, 61.0], [17.6, 61.0], [17.7, 61.0], [17.8, 61.0], [17.9, 61.0], [18.0, 61.0], [18.1, 61.0], [18.2, 61.0], [18.3, 61.0], [18.4, 61.0], [18.5, 61.0], [18.6, 61.0], [18.7, 61.0], [18.8, 61.0], [18.9, 61.0], [19.0, 61.0], [19.1, 61.0], [19.2, 61.0], [19.3, 61.0], [19.4, 61.0], [19.5, 61.0], [19.6, 61.0], [19.7, 61.0], [19.8, 61.0], [19.9, 61.0], [20.0, 61.0], [20.1, 61.0], [20.2, 61.0], [20.3, 61.0], [20.4, 61.0], [20.5, 61.0], [20.6, 61.0], [20.7, 61.0], [20.8, 61.0], [20.9, 61.0], [21.0, 61.0], [21.1, 61.0], [21.2, 61.0], [21.3, 61.0], [21.4, 61.0], [21.5, 61.0], [21.6, 61.0], [21.7, 61.0], [21.8, 61.0], [21.9, 61.0], [22.0, 61.0], [22.1, 61.0], [22.2, 61.0], [22.3, 61.0], [22.4, 61.0], [22.5, 61.0], [22.6, 61.0], [22.7, 61.0], [22.8, 61.0], [22.9, 61.0], [23.0, 61.0], [23.1, 61.0], [23.2, 61.0], [23.3, 61.0], [23.4, 61.0], [23.5, 61.0], [23.6, 61.0], [23.7, 61.0], [23.8, 61.0], [23.9, 61.0], [24.0, 62.0], [24.1, 62.0], [24.2, 62.0], [24.3, 62.0], [24.4, 62.0], [24.5, 62.0], [24.6, 62.0], [24.7, 62.0], [24.8, 62.0], [24.9, 62.0], [25.0, 62.0], [25.1, 62.0], [25.2, 62.0], [25.3, 62.0], [25.4, 62.0], [25.5, 62.0], [25.6, 62.0], [25.7, 62.0], [25.8, 62.0], [25.9, 62.0], [26.0, 62.0], [26.1, 62.0], [26.2, 62.0], [26.3, 62.0], [26.4, 62.0], [26.5, 62.0], [26.6, 62.0], [26.7, 62.0], [26.8, 62.0], [26.9, 62.0], [27.0, 62.0], [27.1, 62.0], [27.2, 62.0], [27.3, 62.0], [27.4, 62.0], [27.5, 62.0], [27.6, 62.0], [27.7, 62.0], [27.8, 62.0], [27.9, 62.0], [28.0, 62.0], [28.1, 62.0], [28.2, 62.0], [28.3, 62.0], [28.4, 62.0], [28.5, 62.0], [28.6, 62.0], [28.7, 62.0], [28.8, 62.0], [28.9, 62.0], [29.0, 62.0], [29.1, 62.0], [29.2, 62.0], [29.3, 62.0], [29.4, 62.0], [29.5, 62.0], [29.6, 62.0], [29.7, 62.0], [29.8, 62.0], [29.9, 62.0], [30.0, 62.0], [30.1, 62.0], [30.2, 62.0], [30.3, 62.0], [30.4, 62.0], [30.5, 62.0], [30.6, 62.0], [30.7, 62.0], [30.8, 62.0], [30.9, 62.0], [31.0, 62.0], [31.1, 62.0], [31.2, 62.0], [31.3, 62.0], [31.4, 62.0], [31.5, 62.0], [31.6, 62.0], [31.7, 62.0], [31.8, 62.0], [31.9, 62.0], [32.0, 62.0], [32.1, 62.0], [32.2, 62.0], [32.3, 62.0], [32.4, 62.0], [32.5, 62.0], [32.6, 62.0], [32.7, 62.0], [32.8, 62.0], [32.9, 62.0], [33.0, 62.0], [33.1, 62.0], [33.2, 62.0], [33.3, 62.0], [33.4, 62.0], [33.5, 62.0], [33.6, 62.0], [33.7, 62.0], [33.8, 62.0], [33.9, 62.0], [34.0, 62.0], [34.1, 62.0], [34.2, 62.0], [34.3, 62.0], [34.4, 62.0], [34.5, 62.0], [34.6, 62.0], [34.7, 62.0], [34.8, 62.0], [34.9, 62.0], [35.0, 62.0], [35.1, 62.0], [35.2, 62.0], [35.3, 62.0], [35.4, 62.0], [35.5, 62.0], [35.6, 62.0], [35.7, 62.0], [35.8, 63.0], [35.9, 63.0], [36.0, 63.0], [36.1, 63.0], [36.2, 63.0], [36.3, 63.0], [36.4, 63.0], [36.5, 63.0], [36.6, 63.0], [36.7, 63.0], [36.8, 63.0], [36.9, 63.0], [37.0, 63.0], [37.1, 63.0], [37.2, 63.0], [37.3, 63.0], [37.4, 63.0], [37.5, 63.0], [37.6, 63.0], [37.7, 63.0], [37.8, 63.0], [37.9, 63.0], [38.0, 63.0], [38.1, 63.0], [38.2, 63.0], [38.3, 63.0], [38.4, 63.0], [38.5, 63.0], [38.6, 63.0], [38.7, 63.0], [38.8, 63.0], [38.9, 63.0], [39.0, 63.0], [39.1, 63.0], [39.2, 63.0], [39.3, 63.0], [39.4, 63.0], [39.5, 63.0], [39.6, 63.0], [39.7, 63.0], [39.8, 63.0], [39.9, 63.0], [40.0, 63.0], [40.1, 63.0], [40.2, 63.0], [40.3, 63.0], [40.4, 63.0], [40.5, 63.0], [40.6, 63.0], [40.7, 63.0], [40.8, 63.0], [40.9, 63.0], [41.0, 63.0], [41.1, 63.0], [41.2, 63.0], [41.3, 63.0], [41.4, 63.0], [41.5, 63.0], [41.6, 63.0], [41.7, 63.0], [41.8, 63.0], [41.9, 63.0], [42.0, 63.0], [42.1, 63.0], [42.2, 63.0], [42.3, 63.0], [42.4, 63.0], [42.5, 63.0], [42.6, 63.0], [42.7, 63.0], [42.8, 63.0], [42.9, 63.0], [43.0, 63.0], [43.1, 63.0], [43.2, 63.0], [43.3, 63.0], [43.4, 63.0], [43.5, 63.0], [43.6, 63.0], [43.7, 63.0], [43.8, 63.0], [43.9, 63.0], [44.0, 63.0], [44.1, 63.0], [44.2, 63.0], [44.3, 63.0], [44.4, 63.0], [44.5, 63.0], [44.6, 63.0], [44.7, 63.0], [44.8, 63.0], [44.9, 63.0], [45.0, 64.0], [45.1, 64.0], [45.2, 64.0], [45.3, 64.0], [45.4, 64.0], [45.5, 64.0], [45.6, 64.0], [45.7, 64.0], [45.8, 64.0], [45.9, 64.0], [46.0, 64.0], [46.1, 64.0], [46.2, 64.0], [46.3, 64.0], [46.4, 64.0], [46.5, 64.0], [46.6, 64.0], [46.7, 64.0], [46.8, 64.0], [46.9, 64.0], [47.0, 64.0], [47.1, 64.0], [47.2, 64.0], [47.3, 64.0], [47.4, 64.0], [47.5, 64.0], [47.6, 64.0], [47.7, 64.0], [47.8, 64.0], [47.9, 64.0], [48.0, 64.0], [48.1, 64.0], [48.2, 64.0], [48.3, 64.0], [48.4, 64.0], [48.5, 64.0], [48.6, 64.0], [48.7, 64.0], [48.8, 64.0], [48.9, 64.0], [49.0, 64.0], [49.1, 64.0], [49.2, 64.0], [49.3, 64.0], [49.4, 64.0], [49.5, 64.0], [49.6, 64.0], [49.7, 64.0], [49.8, 64.0], [49.9, 64.0], [50.0, 64.0], [50.1, 64.0], [50.2, 64.0], [50.3, 64.0], [50.4, 64.0], [50.5, 64.0], [50.6, 64.0], [50.7, 64.0], [50.8, 64.0], [50.9, 64.0], [51.0, 64.0], [51.1, 64.0], [51.2, 64.0], [51.3, 64.0], [51.4, 64.0], [51.5, 64.0], [51.6, 64.0], [51.7, 64.0], [51.8, 64.0], [51.9, 64.0], [52.0, 64.0], [52.1, 64.0], [52.2, 64.0], [52.3, 64.0], [52.4, 64.0], [52.5, 64.0], [52.6, 64.0], [52.7, 64.0], [52.8, 64.0], [52.9, 64.0], [53.0, 64.0], [53.1, 64.0], [53.2, 64.0], [53.3, 64.0], [53.4, 64.0], [53.5, 64.0], [53.6, 64.0], [53.7, 64.0], [53.8, 64.0], [53.9, 64.0], [54.0, 64.0], [54.1, 64.0], [54.2, 64.0], [54.3, 64.0], [54.4, 64.0], [54.5, 64.0], [54.6, 64.0], [54.7, 64.0], [54.8, 64.0], [54.9, 64.0], [55.0, 64.0], [55.1, 64.0], [55.2, 64.0], [55.3, 64.0], [55.4, 65.0], [55.5, 65.0], [55.6, 65.0], [55.7, 65.0], [55.8, 65.0], [55.9, 65.0], [56.0, 65.0], [56.1, 65.0], [56.2, 65.0], [56.3, 65.0], [56.4, 65.0], [56.5, 65.0], [56.6, 65.0], [56.7, 65.0], [56.8, 65.0], [56.9, 65.0], [57.0, 65.0], [57.1, 65.0], [57.2, 65.0], [57.3, 65.0], [57.4, 65.0], [57.5, 65.0], [57.6, 65.0], [57.7, 65.0], [57.8, 65.0], [57.9, 65.0], [58.0, 65.0], [58.1, 65.0], [58.2, 65.0], [58.3, 65.0], [58.4, 65.0], [58.5, 65.0], [58.6, 65.0], [58.7, 65.0], [58.8, 65.0], [58.9, 65.0], [59.0, 65.0], [59.1, 65.0], [59.2, 65.0], [59.3, 65.0], [59.4, 65.0], [59.5, 65.0], [59.6, 65.0], [59.7, 65.0], [59.8, 65.0], [59.9, 65.0], [60.0, 65.0], [60.1, 65.0], [60.2, 65.0], [60.3, 65.0], [60.4, 65.0], [60.5, 65.0], [60.6, 65.0], [60.7, 65.0], [60.8, 65.0], [60.9, 65.0], [61.0, 65.0], [61.1, 65.0], [61.2, 65.0], [61.3, 65.0], [61.4, 65.0], [61.5, 65.0], [61.6, 65.0], [61.7, 65.0], [61.8, 65.0], [61.9, 65.0], [62.0, 65.0], [62.1, 65.0], [62.2, 65.0], [62.3, 65.0], [62.4, 65.0], [62.5, 65.0], [62.6, 65.0], [62.7, 65.0], [62.8, 65.0], [62.9, 65.0], [63.0, 65.0], [63.1, 65.0], [63.2, 65.0], [63.3, 65.0], [63.4, 65.0], [63.5, 65.0], [63.6, 66.0], [63.7, 66.0], [63.8, 66.0], [63.9, 66.0], [64.0, 66.0], [64.1, 66.0], [64.2, 66.0], [64.3, 66.0], [64.4, 66.0], [64.5, 66.0], [64.6, 66.0], [64.7, 66.0], [64.8, 66.0], [64.9, 66.0], [65.0, 66.0], [65.1, 66.0], [65.2, 66.0], [65.3, 66.0], [65.4, 66.0], [65.5, 66.0], [65.6, 66.0], [65.7, 66.0], [65.8, 66.0], [65.9, 66.0], [66.0, 66.0], [66.1, 66.0], [66.2, 66.0], [66.3, 66.0], [66.4, 66.0], [66.5, 66.0], [66.6, 66.0], [66.7, 66.0], [66.8, 66.0], [66.9, 66.0], [67.0, 66.0], [67.1, 66.0], [67.2, 66.0], [67.3, 66.0], [67.4, 66.0], [67.5, 66.0], [67.6, 66.0], [67.7, 66.0], [67.8, 66.0], [67.9, 66.0], [68.0, 66.0], [68.1, 66.0], [68.2, 66.0], [68.3, 66.0], [68.4, 66.0], [68.5, 66.0], [68.6, 66.0], [68.7, 66.0], [68.8, 66.0], [68.9, 66.0], [69.0, 66.0], [69.1, 66.0], [69.2, 66.0], [69.3, 66.0], [69.4, 66.0], [69.5, 66.0], [69.6, 66.0], [69.7, 66.0], [69.8, 66.0], [69.9, 66.0], [70.0, 66.0], [70.1, 66.0], [70.2, 66.0], [70.3, 66.0], [70.4, 66.0], [70.5, 66.0], [70.6, 66.0], [70.7, 66.0], [70.8, 66.0], [70.9, 66.0], [71.0, 66.0], [71.1, 66.0], [71.2, 67.0], [71.3, 67.0], [71.4, 67.0], [71.5, 67.0], [71.6, 67.0], [71.7, 67.0], [71.8, 67.0], [71.9, 67.0], [72.0, 67.0], [72.1, 67.0], [72.2, 67.0], [72.3, 67.0], [72.4, 67.0], [72.5, 67.0], [72.6, 67.0], [72.7, 67.0], [72.8, 67.0], [72.9, 67.0], [73.0, 67.0], [73.1, 67.0], [73.2, 67.0], [73.3, 67.0], [73.4, 67.0], [73.5, 67.0], [73.6, 67.0], [73.7, 67.0], [73.8, 67.0], [73.9, 67.0], [74.0, 67.0], [74.1, 67.0], [74.2, 67.0], [74.3, 67.0], [74.4, 67.0], [74.5, 67.0], [74.6, 67.0], [74.7, 67.0], [74.8, 67.0], [74.9, 67.0], [75.0, 67.0], [75.1, 67.0], [75.2, 67.0], [75.3, 67.0], [75.4, 67.0], [75.5, 67.0], [75.6, 67.0], [75.7, 67.0], [75.8, 67.0], [75.9, 67.0], [76.0, 67.0], [76.1, 67.0], [76.2, 67.0], [76.3, 67.0], [76.4, 67.0], [76.5, 67.0], [76.6, 67.0], [76.7, 67.0], [76.8, 67.0], [76.9, 67.0], [77.0, 67.0], [77.1, 67.0], [77.2, 67.0], [77.3, 67.0], [77.4, 67.0], [77.5, 67.0], [77.6, 67.0], [77.7, 67.0], [77.8, 67.0], [77.9, 67.0], [78.0, 67.0], [78.1, 67.0], [78.2, 67.0], [78.3, 67.0], [78.4, 67.0], [78.5, 67.0], [78.6, 67.0], [78.7, 67.0], [78.8, 67.0], [78.9, 67.0], [79.0, 67.0], [79.1, 67.0], [79.2, 68.0], [79.3, 68.0], [79.4, 68.0], [79.5, 68.0], [79.6, 68.0], [79.7, 68.0], [79.8, 68.0], [79.9, 68.0], [80.0, 68.0], [80.1, 68.0], [80.2, 68.0], [80.3, 68.0], [80.4, 68.0], [80.5, 68.0], [80.6, 68.0], [80.7, 68.0], [80.8, 68.0], [80.9, 68.0], [81.0, 68.0], [81.1, 68.0], [81.2, 68.0], [81.3, 68.0], [81.4, 68.0], [81.5, 68.0], [81.6, 68.0], [81.7, 68.0], [81.8, 68.0], [81.9, 68.0], [82.0, 68.0], [82.1, 68.0], [82.2, 68.0], [82.3, 68.0], [82.4, 68.0], [82.5, 68.0], [82.6, 68.0], [82.7, 68.0], [82.8, 68.0], [82.9, 68.0], [83.0, 68.0], [83.1, 68.0], [83.2, 68.0], [83.3, 68.0], [83.4, 68.0], [83.5, 68.0], [83.6, 68.0], [83.7, 68.0], [83.8, 69.0], [83.9, 69.0], [84.0, 69.0], [84.1, 69.0], [84.2, 69.0], [84.3, 69.0], [84.4, 69.0], [84.5, 69.0], [84.6, 69.0], [84.7, 69.0], [84.8, 69.0], [84.9, 69.0], [85.0, 69.0], [85.1, 69.0], [85.2, 69.0], [85.3, 69.0], [85.4, 69.0], [85.5, 69.0], [85.6, 69.0], [85.7, 69.0], [85.8, 69.0], [85.9, 69.0], [86.0, 69.0], [86.1, 69.0], [86.2, 69.0], [86.3, 69.0], [86.4, 69.0], [86.5, 69.0], [86.6, 69.0], [86.7, 69.0], [86.8, 69.0], [86.9, 69.0], [87.0, 69.0], [87.1, 69.0], [87.2, 69.0], [87.3, 69.0], [87.4, 69.0], [87.5, 69.0], [87.6, 70.0], [87.7, 70.0], [87.8, 70.0], [87.9, 70.0], [88.0, 70.0], [88.1, 70.0], [88.2, 70.0], [88.3, 70.0], [88.4, 70.0], [88.5, 70.0], [88.6, 70.0], [88.7, 70.0], [88.8, 70.0], [88.9, 70.0], [89.0, 70.0], [89.1, 70.0], [89.2, 70.0], [89.3, 70.0], [89.4, 71.0], [89.5, 71.0], [89.6, 71.0], [89.7, 71.0], [89.8, 71.0], [89.9, 71.0], [90.0, 71.0], [90.1, 71.0], [90.2, 71.0], [90.3, 71.0], [90.4, 72.0], [90.5, 72.0], [90.6, 72.0], [90.7, 72.0], [90.8, 72.0], [90.9, 72.0], [91.0, 72.0], [91.1, 72.0], [91.2, 72.0], [91.3, 72.0], [91.4, 72.0], [91.5, 72.0], [91.6, 72.0], [91.7, 72.0], [91.8, 73.0], [91.9, 73.0], [92.0, 73.0], [92.1, 73.0], [92.2, 74.0], [92.3, 74.0], [92.4, 75.0], [92.5, 75.0], [92.6, 75.0], [92.7, 75.0], [92.8, 76.0], [92.9, 76.0], [93.0, 76.0], [93.1, 76.0], [93.2, 77.0], [93.3, 77.0], [93.4, 77.0], [93.5, 77.0], [93.6, 77.0], [93.7, 77.0], [93.8, 77.0], [93.9, 77.0], [94.0, 78.0], [94.1, 78.0], [94.2, 79.0], [94.3, 79.0], [94.4, 80.0], [94.5, 80.0], [94.6, 80.0], [94.7, 80.0], [94.8, 80.0], [94.9, 81.0], [95.0, 81.0], [95.1, 82.0], [95.2, 82.0], [95.3, 82.0], [95.4, 82.0], [95.5, 83.0], [95.6, 83.0], [95.7, 84.0], [95.8, 84.0], [95.9, 84.0], [96.0, 84.0], [96.1, 84.0], [96.2, 84.0], [96.3, 84.0], [96.4, 84.0], [96.5, 85.0], [96.6, 85.0], [96.7, 85.0], [96.8, 85.0], [96.9, 86.0], [97.0, 86.0], [97.1, 87.0], [97.2, 87.0], [97.3, 87.0], [97.4, 87.0], [97.5, 89.0], [97.6, 89.0], [97.7, 89.0], [97.8, 89.0], [97.9, 91.0], [98.0, 91.0], [98.1, 91.0], [98.2, 91.0], [98.3, 93.0], [98.4, 93.0], [98.5, 93.0], [98.6, 93.0], [98.7, 93.0], [98.8, 93.0], [98.9, 94.0], [99.0, 94.0], [99.1, 98.0], [99.2, 98.0], [99.3, 100.0], [99.4, 100.0], [99.5, 103.0], [99.6, 103.0], [99.7, 112.0], [99.8, 112.0], [99.9, 139.0], [100.0, 139.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 31.0], [0.1, 31.0], [0.2, 32.0], [0.3, 32.0], [0.4, 32.0], [0.5, 32.0], [0.6, 32.0], [0.7, 32.0], [0.8, 32.0], [0.9, 33.0], [1.0, 33.0], [1.1, 33.0], [1.2, 33.0], [1.3, 33.0], [1.4, 33.0], [1.5, 33.0], [1.6, 33.0], [1.7, 33.0], [1.8, 33.0], [1.9, 33.0], [2.0, 33.0], [2.1, 33.0], [2.2, 34.0], [2.3, 34.0], [2.4, 34.0], [2.5, 34.0], [2.6, 34.0], [2.7, 34.0], [2.8, 34.0], [2.9, 34.0], [3.0, 34.0], [3.1, 34.0], [3.2, 34.0], [3.3, 34.0], [3.4, 34.0], [3.5, 34.0], [3.6, 34.0], [3.7, 34.0], [3.8, 34.0], [3.9, 34.0], [4.0, 35.0], [4.1, 35.0], [4.2, 35.0], [4.3, 35.0], [4.4, 35.0], [4.5, 35.0], [4.6, 35.0], [4.7, 35.0], [4.8, 35.0], [4.9, 35.0], [5.0, 35.0], [5.1, 35.0], [5.2, 35.0], [5.3, 35.0], [5.4, 35.0], [5.5, 35.0], [5.6, 35.0], [5.7, 35.0], [5.8, 35.0], [5.9, 36.0], [6.0, 36.0], [6.1, 36.0], [6.2, 36.0], [6.3, 36.0], [6.4, 36.0], [6.5, 36.0], [6.6, 36.0], [6.7, 36.0], [6.8, 36.0], [6.9, 36.0], [7.0, 36.0], [7.1, 36.0], [7.2, 36.0], [7.3, 36.0], [7.4, 36.0], [7.5, 36.0], [7.6, 36.0], [7.7, 36.0], [7.8, 36.0], [7.9, 36.0], [8.0, 36.0], [8.1, 36.0], [8.2, 36.0], [8.3, 36.0], [8.4, 36.0], [8.5, 36.0], [8.6, 36.0], [8.7, 36.0], [8.8, 36.0], [8.9, 36.0], [9.0, 36.0], [9.1, 36.0], [9.2, 36.0], [9.3, 36.0], [9.4, 36.0], [9.5, 36.0], [9.6, 36.0], [9.7, 36.0], [9.8, 36.0], [9.9, 36.0], [10.0, 36.0], [10.1, 36.0], [10.2, 37.0], [10.3, 37.0], [10.4, 37.0], [10.5, 37.0], [10.6, 37.0], [10.7, 37.0], [10.8, 37.0], [10.9, 37.0], [11.0, 37.0], [11.1, 37.0], [11.2, 37.0], [11.3, 37.0], [11.4, 37.0], [11.5, 37.0], [11.6, 37.0], [11.7, 37.0], [11.8, 37.0], [11.9, 37.0], [12.0, 37.0], [12.1, 37.0], [12.2, 37.0], [12.3, 37.0], [12.4, 37.0], [12.5, 37.0], [12.6, 37.0], [12.7, 37.0], [12.8, 37.0], [12.9, 37.0], [13.0, 37.0], [13.1, 37.0], [13.2, 37.0], [13.3, 37.0], [13.4, 37.0], [13.5, 37.0], [13.6, 37.0], [13.7, 37.0], [13.8, 37.0], [13.9, 37.0], [14.0, 37.0], [14.1, 37.0], [14.2, 37.0], [14.3, 37.0], [14.4, 37.0], [14.5, 37.0], [14.6, 37.0], [14.7, 37.0], [14.8, 37.0], [14.9, 37.0], [15.0, 37.0], [15.1, 37.0], [15.2, 37.0], [15.3, 37.0], [15.4, 37.0], [15.5, 37.0], [15.6, 37.0], [15.7, 37.0], [15.8, 37.0], [15.9, 37.0], [16.0, 37.0], [16.1, 37.0], [16.2, 38.0], [16.3, 38.0], [16.4, 38.0], [16.5, 38.0], [16.6, 38.0], [16.7, 38.0], [16.8, 38.0], [16.9, 38.0], [17.0, 38.0], [17.1, 38.0], [17.2, 38.0], [17.3, 38.0], [17.4, 38.0], [17.5, 38.0], [17.6, 38.0], [17.7, 38.0], [17.8, 38.0], [17.9, 38.0], [18.0, 38.0], [18.1, 38.0], [18.2, 38.0], [18.3, 38.0], [18.4, 38.0], [18.5, 38.0], [18.6, 38.0], [18.7, 38.0], [18.8, 38.0], [18.9, 38.0], [19.0, 38.0], [19.1, 38.0], [19.2, 38.0], [19.3, 38.0], [19.4, 38.0], [19.5, 38.0], [19.6, 38.0], [19.7, 38.0], [19.8, 38.0], [19.9, 38.0], [20.0, 38.0], [20.1, 38.0], [20.2, 38.0], [20.3, 38.0], [20.4, 38.0], [20.5, 38.0], [20.6, 38.0], [20.7, 38.0], [20.8, 38.0], [20.9, 38.0], [21.0, 38.0], [21.1, 38.0], [21.2, 38.0], [21.3, 38.0], [21.4, 38.0], [21.5, 38.0], [21.6, 38.0], [21.7, 38.0], [21.8, 38.0], [21.9, 38.0], [22.0, 38.0], [22.1, 38.0], [22.2, 38.0], [22.3, 38.0], [22.4, 38.0], [22.5, 38.0], [22.6, 38.0], [22.7, 38.0], [22.8, 38.0], [22.9, 39.0], [23.0, 39.0], [23.1, 39.0], [23.2, 39.0], [23.3, 39.0], [23.4, 39.0], [23.5, 39.0], [23.6, 39.0], [23.7, 39.0], [23.8, 39.0], [23.9, 39.0], [24.0, 39.0], [24.1, 39.0], [24.2, 39.0], [24.3, 39.0], [24.4, 39.0], [24.5, 39.0], [24.6, 39.0], [24.7, 39.0], [24.8, 39.0], [24.9, 39.0], [25.0, 39.0], [25.1, 39.0], [25.2, 39.0], [25.3, 39.0], [25.4, 39.0], [25.5, 39.0], [25.6, 39.0], [25.7, 39.0], [25.8, 39.0], [25.9, 39.0], [26.0, 39.0], [26.1, 39.0], [26.2, 39.0], [26.3, 39.0], [26.4, 39.0], [26.5, 39.0], [26.6, 39.0], [26.7, 39.0], [26.8, 39.0], [26.9, 39.0], [27.0, 39.0], [27.1, 39.0], [27.2, 39.0], [27.3, 39.0], [27.4, 39.0], [27.5, 39.0], [27.6, 39.0], [27.7, 39.0], [27.8, 39.0], [27.9, 39.0], [28.0, 39.0], [28.1, 39.0], [28.2, 39.0], [28.3, 39.0], [28.4, 39.0], [28.5, 39.0], [28.6, 39.0], [28.7, 39.0], [28.8, 39.0], [28.9, 39.0], [29.0, 39.0], [29.1, 39.0], [29.2, 39.0], [29.3, 39.0], [29.4, 39.0], [29.5, 39.0], [29.6, 39.0], [29.7, 39.0], [29.8, 39.0], [29.9, 39.0], [30.0, 39.0], [30.1, 39.0], [30.2, 39.0], [30.3, 39.0], [30.4, 39.0], [30.5, 40.0], [30.6, 40.0], [30.7, 40.0], [30.8, 40.0], [30.9, 40.0], [31.0, 40.0], [31.1, 40.0], [31.2, 40.0], [31.3, 40.0], [31.4, 40.0], [31.5, 40.0], [31.6, 40.0], [31.7, 40.0], [31.8, 40.0], [31.9, 40.0], [32.0, 40.0], [32.1, 40.0], [32.2, 40.0], [32.3, 40.0], [32.4, 40.0], [32.5, 40.0], [32.6, 40.0], [32.7, 40.0], [32.8, 40.0], [32.9, 40.0], [33.0, 40.0], [33.1, 40.0], [33.2, 40.0], [33.3, 40.0], [33.4, 40.0], [33.5, 40.0], [33.6, 40.0], [33.7, 40.0], [33.8, 40.0], [33.9, 40.0], [34.0, 40.0], [34.1, 40.0], [34.2, 40.0], [34.3, 40.0], [34.4, 40.0], [34.5, 40.0], [34.6, 40.0], [34.7, 40.0], [34.8, 40.0], [34.9, 40.0], [35.0, 40.0], [35.1, 40.0], [35.2, 40.0], [35.3, 40.0], [35.4, 40.0], [35.5, 40.0], [35.6, 40.0], [35.7, 40.0], [35.8, 40.0], [35.9, 40.0], [36.0, 40.0], [36.1, 40.0], [36.2, 40.0], [36.3, 40.0], [36.4, 40.0], [36.5, 40.0], [36.6, 40.0], [36.7, 40.0], [36.8, 40.0], [36.9, 40.0], [37.0, 40.0], [37.1, 40.0], [37.2, 41.0], [37.3, 41.0], [37.4, 41.0], [37.5, 41.0], [37.6, 41.0], [37.7, 41.0], [37.8, 41.0], [37.9, 41.0], [38.0, 41.0], [38.1, 41.0], [38.2, 41.0], [38.3, 41.0], [38.4, 41.0], [38.5, 41.0], [38.6, 41.0], [38.7, 41.0], [38.8, 41.0], [38.9, 41.0], [39.0, 41.0], [39.1, 41.0], [39.2, 41.0], [39.3, 41.0], [39.4, 41.0], [39.5, 41.0], [39.6, 41.0], [39.7, 41.0], [39.8, 41.0], [39.9, 41.0], [40.0, 41.0], [40.1, 41.0], [40.2, 41.0], [40.3, 41.0], [40.4, 41.0], [40.5, 41.0], [40.6, 41.0], [40.7, 41.0], [40.8, 41.0], [40.9, 41.0], [41.0, 41.0], [41.1, 41.0], [41.2, 41.0], [41.3, 41.0], [41.4, 41.0], [41.5, 41.0], [41.6, 41.0], [41.7, 41.0], [41.8, 41.0], [41.9, 41.0], [42.0, 41.0], [42.1, 41.0], [42.2, 41.0], [42.3, 41.0], [42.4, 41.0], [42.5, 41.0], [42.6, 42.0], [42.7, 42.0], [42.8, 42.0], [42.9, 42.0], [43.0, 42.0], [43.1, 42.0], [43.2, 42.0], [43.3, 42.0], [43.4, 42.0], [43.5, 42.0], [43.6, 42.0], [43.7, 42.0], [43.8, 42.0], [43.9, 42.0], [44.0, 42.0], [44.1, 42.0], [44.2, 42.0], [44.3, 42.0], [44.4, 42.0], [44.5, 42.0], [44.6, 42.0], [44.7, 42.0], [44.8, 42.0], [44.9, 42.0], [45.0, 42.0], [45.1, 42.0], [45.2, 42.0], [45.3, 42.0], [45.4, 42.0], [45.5, 42.0], [45.6, 42.0], [45.7, 42.0], [45.8, 42.0], [45.9, 42.0], [46.0, 42.0], [46.1, 42.0], [46.2, 42.0], [46.3, 42.0], [46.4, 42.0], [46.5, 42.0], [46.6, 42.0], [46.7, 42.0], [46.8, 42.0], [46.9, 42.0], [47.0, 42.0], [47.1, 42.0], [47.2, 42.0], [47.3, 42.0], [47.4, 42.0], [47.5, 42.0], [47.6, 42.0], [47.7, 42.0], [47.8, 43.0], [47.9, 43.0], [48.0, 43.0], [48.1, 43.0], [48.2, 43.0], [48.3, 43.0], [48.4, 43.0], [48.5, 43.0], [48.6, 43.0], [48.7, 43.0], [48.8, 43.0], [48.9, 43.0], [49.0, 43.0], [49.1, 43.0], [49.2, 43.0], [49.3, 43.0], [49.4, 43.0], [49.5, 43.0], [49.6, 43.0], [49.7, 43.0], [49.8, 43.0], [49.9, 43.0], [50.0, 43.0], [50.1, 43.0], [50.2, 43.0], [50.3, 43.0], [50.4, 43.0], [50.5, 43.0], [50.6, 43.0], [50.7, 43.0], [50.8, 43.0], [50.9, 43.0], [51.0, 43.0], [51.1, 43.0], [51.2, 43.0], [51.3, 43.0], [51.4, 43.0], [51.5, 43.0], [51.6, 43.0], [51.7, 43.0], [51.8, 43.0], [51.9, 43.0], [52.0, 43.0], [52.1, 43.0], [52.2, 43.0], [52.3, 43.0], [52.4, 44.0], [52.5, 44.0], [52.6, 44.0], [52.7, 44.0], [52.8, 44.0], [52.9, 44.0], [53.0, 44.0], [53.1, 44.0], [53.2, 44.0], [53.3, 44.0], [53.4, 44.0], [53.5, 44.0], [53.6, 44.0], [53.7, 44.0], [53.8, 44.0], [53.9, 44.0], [54.0, 44.0], [54.1, 44.0], [54.2, 44.0], [54.3, 44.0], [54.4, 44.0], [54.5, 44.0], [54.6, 44.0], [54.7, 44.0], [54.8, 44.0], [54.9, 44.0], [55.0, 44.0], [55.1, 44.0], [55.2, 44.0], [55.3, 44.0], [55.4, 44.0], [55.5, 44.0], [55.6, 44.0], [55.7, 44.0], [55.8, 44.0], [55.9, 44.0], [56.0, 44.0], [56.1, 44.0], [56.2, 44.0], [56.3, 44.0], [56.4, 44.0], [56.5, 44.0], [56.6, 44.0], [56.7, 44.0], [56.8, 44.0], [56.9, 44.0], [57.0, 44.0], [57.1, 45.0], [57.2, 45.0], [57.3, 45.0], [57.4, 45.0], [57.5, 45.0], [57.6, 45.0], [57.7, 45.0], [57.8, 45.0], [57.9, 45.0], [58.0, 45.0], [58.1, 45.0], [58.2, 45.0], [58.3, 45.0], [58.4, 45.0], [58.5, 45.0], [58.6, 45.0], [58.7, 45.0], [58.8, 45.0], [58.9, 45.0], [59.0, 45.0], [59.1, 45.0], [59.2, 45.0], [59.3, 45.0], [59.4, 45.0], [59.5, 45.0], [59.6, 45.0], [59.7, 45.0], [59.8, 45.0], [59.9, 45.0], [60.0, 45.0], [60.1, 45.0], [60.2, 45.0], [60.3, 45.0], [60.4, 45.0], [60.5, 45.0], [60.6, 45.0], [60.7, 45.0], [60.8, 45.0], [60.9, 45.0], [61.0, 45.0], [61.1, 45.0], [61.2, 45.0], [61.3, 45.0], [61.4, 45.0], [61.5, 45.0], [61.6, 45.0], [61.7, 45.0], [61.8, 45.0], [61.9, 45.0], [62.0, 45.0], [62.1, 45.0], [62.2, 45.0], [62.3, 45.0], [62.4, 45.0], [62.5, 45.0], [62.6, 45.0], [62.7, 45.0], [62.8, 45.0], [62.9, 45.0], [63.0, 45.0], [63.1, 45.0], [63.2, 45.0], [63.3, 45.0], [63.4, 45.0], [63.5, 45.0], [63.6, 45.0], [63.7, 46.0], [63.8, 46.0], [63.9, 46.0], [64.0, 46.0], [64.1, 46.0], [64.2, 46.0], [64.3, 46.0], [64.4, 46.0], [64.5, 46.0], [64.6, 46.0], [64.7, 46.0], [64.8, 46.0], [64.9, 46.0], [65.0, 46.0], [65.1, 46.0], [65.2, 46.0], [65.3, 46.0], [65.4, 46.0], [65.5, 46.0], [65.6, 46.0], [65.7, 46.0], [65.8, 46.0], [65.9, 46.0], [66.0, 46.0], [66.1, 46.0], [66.2, 46.0], [66.3, 46.0], [66.4, 46.0], [66.5, 46.0], [66.6, 46.0], [66.7, 46.0], [66.8, 46.0], [66.9, 46.0], [67.0, 46.0], [67.1, 46.0], [67.2, 46.0], [67.3, 46.0], [67.4, 46.0], [67.5, 46.0], [67.6, 46.0], [67.7, 46.0], [67.8, 46.0], [67.9, 46.0], [68.0, 46.0], [68.1, 46.0], [68.2, 47.0], [68.3, 47.0], [68.4, 47.0], [68.5, 47.0], [68.6, 47.0], [68.7, 47.0], [68.8, 47.0], [68.9, 47.0], [69.0, 47.0], [69.1, 47.0], [69.2, 47.0], [69.3, 47.0], [69.4, 47.0], [69.5, 47.0], [69.6, 47.0], [69.7, 47.0], [69.8, 47.0], [69.9, 47.0], [70.0, 47.0], [70.1, 47.0], [70.2, 47.0], [70.3, 47.0], [70.4, 47.0], [70.5, 47.0], [70.6, 47.0], [70.7, 47.0], [70.8, 47.0], [70.9, 47.0], [71.0, 47.0], [71.1, 47.0], [71.2, 47.0], [71.3, 47.0], [71.4, 47.0], [71.5, 47.0], [71.6, 47.0], [71.7, 47.0], [71.8, 47.0], [71.9, 47.0], [72.0, 47.0], [72.1, 47.0], [72.2, 48.0], [72.3, 48.0], [72.4, 48.0], [72.5, 48.0], [72.6, 48.0], [72.7, 48.0], [72.8, 48.0], [72.9, 48.0], [73.0, 48.0], [73.1, 48.0], [73.2, 48.0], [73.3, 48.0], [73.4, 48.0], [73.5, 48.0], [73.6, 48.0], [73.7, 48.0], [73.8, 48.0], [73.9, 48.0], [74.0, 48.0], [74.1, 48.0], [74.2, 48.0], [74.3, 48.0], [74.4, 48.0], [74.5, 48.0], [74.6, 48.0], [74.7, 48.0], [74.8, 48.0], [74.9, 48.0], [75.0, 48.0], [75.1, 48.0], [75.2, 48.0], [75.3, 48.0], [75.4, 48.0], [75.5, 48.0], [75.6, 48.0], [75.7, 48.0], [75.8, 48.0], [75.9, 48.0], [76.0, 48.0], [76.1, 48.0], [76.2, 48.0], [76.3, 48.0], [76.4, 48.0], [76.5, 48.0], [76.6, 48.0], [76.7, 49.0], [76.8, 49.0], [76.9, 49.0], [77.0, 49.0], [77.1, 49.0], [77.2, 49.0], [77.3, 49.0], [77.4, 49.0], [77.5, 49.0], [77.6, 49.0], [77.7, 49.0], [77.8, 49.0], [77.9, 49.0], [78.0, 49.0], [78.1, 49.0], [78.2, 49.0], [78.3, 49.0], [78.4, 49.0], [78.5, 49.0], [78.6, 49.0], [78.7, 49.0], [78.8, 49.0], [78.9, 49.0], [79.0, 49.0], [79.1, 49.0], [79.2, 49.0], [79.3, 49.0], [79.4, 49.0], [79.5, 49.0], [79.6, 49.0], [79.7, 49.0], [79.8, 49.0], [79.9, 49.0], [80.0, 49.0], [80.1, 49.0], [80.2, 49.0], [80.3, 49.0], [80.4, 50.0], [80.5, 50.0], [80.6, 50.0], [80.7, 50.0], [80.8, 50.0], [80.9, 50.0], [81.0, 50.0], [81.1, 50.0], [81.2, 50.0], [81.3, 50.0], [81.4, 50.0], [81.5, 50.0], [81.6, 50.0], [81.7, 50.0], [81.8, 50.0], [81.9, 50.0], [82.0, 50.0], [82.1, 50.0], [82.2, 50.0], [82.3, 50.0], [82.4, 50.0], [82.5, 50.0], [82.6, 50.0], [82.7, 50.0], [82.8, 50.0], [82.9, 50.0], [83.0, 50.0], [83.1, 50.0], [83.2, 50.0], [83.3, 50.0], [83.4, 50.0], [83.5, 50.0], [83.6, 50.0], [83.7, 50.0], [83.8, 50.0], [83.9, 50.0], [84.0, 50.0], [84.1, 50.0], [84.2, 51.0], [84.3, 51.0], [84.4, 51.0], [84.5, 51.0], [84.6, 51.0], [84.7, 51.0], [84.8, 51.0], [84.9, 51.0], [85.0, 51.0], [85.1, 51.0], [85.2, 51.0], [85.3, 51.0], [85.4, 51.0], [85.5, 51.0], [85.6, 51.0], [85.7, 51.0], [85.8, 51.0], [85.9, 51.0], [86.0, 51.0], [86.1, 51.0], [86.2, 51.0], [86.3, 51.0], [86.4, 51.0], [86.5, 51.0], [86.6, 51.0], [86.7, 51.0], [86.8, 51.0], [86.9, 51.0], [87.0, 51.0], [87.1, 51.0], [87.2, 51.0], [87.3, 51.0], [87.4, 51.0], [87.5, 51.0], [87.6, 51.0], [87.7, 51.0], [87.8, 51.0], [87.9, 51.0], [88.0, 52.0], [88.1, 52.0], [88.2, 52.0], [88.3, 52.0], [88.4, 52.0], [88.5, 52.0], [88.6, 52.0], [88.7, 52.0], [88.8, 52.0], [88.9, 52.0], [89.0, 52.0], [89.1, 52.0], [89.2, 52.0], [89.3, 52.0], [89.4, 52.0], [89.5, 52.0], [89.6, 52.0], [89.7, 52.0], [89.8, 53.0], [89.9, 53.0], [90.0, 53.0], [90.1, 53.0], [90.2, 53.0], [90.3, 53.0], [90.4, 53.0], [90.5, 53.0], [90.6, 53.0], [90.7, 53.0], [90.8, 53.0], [90.9, 53.0], [91.0, 53.0], [91.1, 53.0], [91.2, 53.0], [91.3, 53.0], [91.4, 54.0], [91.5, 54.0], [91.6, 54.0], [91.7, 54.0], [91.8, 54.0], [91.9, 54.0], [92.0, 54.0], [92.1, 54.0], [92.2, 54.0], [92.3, 54.0], [92.4, 54.0], [92.5, 55.0], [92.6, 55.0], [92.7, 55.0], [92.8, 55.0], [92.9, 55.0], [93.0, 55.0], [93.1, 55.0], [93.2, 55.0], [93.3, 55.0], [93.4, 55.0], [93.5, 55.0], [93.6, 55.0], [93.7, 55.0], [93.8, 55.0], [93.9, 55.0], [94.0, 55.0], [94.1, 55.0], [94.2, 55.0], [94.3, 55.0], [94.4, 56.0], [94.5, 56.0], [94.6, 56.0], [94.7, 56.0], [94.8, 56.0], [94.9, 56.0], [95.0, 57.0], [95.1, 57.0], [95.2, 57.0], [95.3, 57.0], [95.4, 57.0], [95.5, 57.0], [95.6, 58.0], [95.7, 58.0], [95.8, 58.0], [95.9, 58.0], [96.0, 58.0], [96.1, 58.0], [96.2, 59.0], [96.3, 59.0], [96.4, 60.0], [96.5, 60.0], [96.6, 60.0], [96.7, 60.0], [96.8, 61.0], [96.9, 61.0], [97.0, 61.0], [97.1, 61.0], [97.2, 61.0], [97.3, 61.0], [97.4, 62.0], [97.5, 62.0], [97.6, 62.0], [97.7, 62.0], [97.8, 63.0], [97.9, 63.0], [98.0, 63.0], [98.1, 65.0], [98.2, 66.0], [98.3, 67.0], [98.4, 67.0], [98.5, 68.0], [98.6, 71.0], [98.7, 72.0], [98.8, 73.0], [98.9, 74.0], [99.0, 76.0], [99.1, 77.0], [99.2, 78.0], [99.3, 78.0], [99.4, 81.0], [99.5, 82.0], [99.6, 84.0], [99.7, 84.0], [99.8, 154.0], [99.9, 166.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 4.0], [0.1, 4.0], [0.2, 4.0], [0.3, 4.0], [0.4, 4.0], [0.5, 4.0], [0.6, 4.0], [0.7, 4.0], [0.8, 4.0], [0.9, 4.0], [1.0, 4.0], [1.1, 4.0], [1.2, 5.0], [1.3, 5.0], [1.4, 5.0], [1.5, 5.0], [1.6, 5.0], [1.7, 5.0], [1.8, 5.0], [1.9, 5.0], [2.0, 5.0], [2.1, 5.0], [2.2, 5.0], [2.3, 5.0], [2.4, 5.0], [2.5, 5.0], [2.6, 5.0], [2.7, 5.0], [2.8, 5.0], [2.9, 5.0], [3.0, 5.0], [3.1, 5.0], [3.2, 5.0], [3.3, 5.0], [3.4, 5.0], [3.5, 5.0], [3.6, 5.0], [3.7, 5.0], [3.8, 5.0], [3.9, 5.0], [4.0, 5.0], [4.1, 5.0], [4.2, 5.0], [4.3, 5.0], [4.4, 5.0], [4.5, 5.0], [4.6, 5.0], [4.7, 5.0], [4.8, 5.0], [4.9, 5.0], [5.0, 5.0], [5.1, 5.0], [5.2, 5.0], [5.3, 5.0], [5.4, 5.0], [5.5, 5.0], [5.6, 5.0], [5.7, 5.0], [5.8, 5.0], [5.9, 5.0], [6.0, 5.0], [6.1, 5.0], [6.2, 5.0], [6.3, 5.0], [6.4, 5.0], [6.5, 5.0], [6.6, 5.0], [6.7, 5.0], [6.8, 5.0], [6.9, 5.0], [7.0, 5.0], [7.1, 5.0], [7.2, 5.0], [7.3, 5.0], [7.4, 5.0], [7.5, 5.0], [7.6, 5.0], [7.7, 5.0], [7.8, 5.0], [7.9, 5.0], [8.0, 5.0], [8.1, 5.0], [8.2, 5.0], [8.3, 5.0], [8.4, 5.0], [8.5, 5.0], [8.6, 5.0], [8.7, 5.0], [8.8, 5.0], [8.9, 5.0], [9.0, 5.0], [9.1, 5.0], [9.2, 5.0], [9.3, 5.0], [9.4, 5.0], [9.5, 5.0], [9.6, 5.0], [9.7, 5.0], [9.8, 5.0], [9.9, 5.0], [10.0, 5.0], [10.1, 5.0], [10.2, 5.0], [10.3, 5.0], [10.4, 5.0], [10.5, 5.0], [10.6, 5.0], [10.7, 5.0], [10.8, 5.0], [10.9, 5.0], [11.0, 5.0], [11.1, 5.0], [11.2, 5.0], [11.3, 5.0], [11.4, 5.0], [11.5, 5.0], [11.6, 5.0], [11.7, 5.0], [11.8, 5.0], [11.9, 5.0], [12.0, 5.0], [12.1, 5.0], [12.2, 5.0], [12.3, 5.0], [12.4, 5.0], [12.5, 5.0], [12.6, 5.0], [12.7, 5.0], [12.8, 5.0], [12.9, 5.0], [13.0, 5.0], [13.1, 5.0], [13.2, 5.0], [13.3, 5.0], [13.4, 5.0], [13.5, 5.0], [13.6, 5.0], [13.7, 5.0], [13.8, 5.0], [13.9, 5.0], [14.0, 5.0], [14.1, 5.0], [14.2, 5.0], [14.3, 5.0], [14.4, 5.0], [14.5, 5.0], [14.6, 5.0], [14.7, 5.0], [14.8, 5.0], [14.9, 5.0], [15.0, 5.0], [15.1, 5.0], [15.2, 5.0], [15.3, 5.0], [15.4, 5.0], [15.5, 5.0], [15.6, 5.0], [15.7, 5.0], [15.8, 5.0], [15.9, 5.0], [16.0, 5.0], [16.1, 5.0], [16.2, 5.0], [16.3, 5.0], [16.4, 5.0], [16.5, 5.0], [16.6, 5.0], [16.7, 5.0], [16.8, 5.0], [16.9, 5.0], [17.0, 5.0], [17.1, 5.0], [17.2, 5.0], [17.3, 5.0], [17.4, 5.0], [17.5, 5.0], [17.6, 5.0], [17.7, 5.0], [17.8, 5.0], [17.9, 5.0], [18.0, 5.0], [18.1, 5.0], [18.2, 5.0], [18.3, 5.0], [18.4, 5.0], [18.5, 5.0], [18.6, 5.0], [18.7, 5.0], [18.8, 5.0], [18.9, 5.0], [19.0, 5.0], [19.1, 5.0], [19.2, 5.0], [19.3, 5.0], [19.4, 5.0], [19.5, 5.0], [19.6, 5.0], [19.7, 5.0], [19.8, 5.0], [19.9, 5.0], [20.0, 5.0], [20.1, 5.0], [20.2, 5.0], [20.3, 5.0], [20.4, 5.0], [20.5, 5.0], [20.6, 5.0], [20.7, 5.0], [20.8, 5.0], [20.9, 5.0], [21.0, 5.0], [21.1, 5.0], [21.2, 5.0], [21.3, 5.0], [21.4, 5.0], [21.5, 5.0], [21.6, 5.0], [21.7, 5.0], [21.8, 5.0], [21.9, 5.0], [22.0, 5.0], [22.1, 5.0], [22.2, 5.0], [22.3, 5.0], [22.4, 5.0], [22.5, 5.0], [22.6, 5.0], [22.7, 5.0], [22.8, 5.0], [22.9, 5.0], [23.0, 5.0], [23.1, 5.0], [23.2, 5.0], [23.3, 5.0], [23.4, 5.0], [23.5, 5.0], [23.6, 5.0], [23.7, 5.0], [23.8, 5.0], [23.9, 5.0], [24.0, 5.0], [24.1, 5.0], [24.2, 5.0], [24.3, 5.0], [24.4, 5.0], [24.5, 5.0], [24.6, 5.0], [24.7, 5.0], [24.8, 5.0], [24.9, 5.0], [25.0, 5.0], [25.1, 5.0], [25.2, 5.0], [25.3, 5.0], [25.4, 5.0], [25.5, 5.0], [25.6, 5.0], [25.7, 5.0], [25.8, 5.0], [25.9, 5.0], [26.0, 5.0], [26.1, 5.0], [26.2, 5.0], [26.3, 5.0], [26.4, 5.0], [26.5, 5.0], [26.6, 5.0], [26.7, 5.0], [26.8, 5.0], [26.9, 5.0], [27.0, 5.0], [27.1, 5.0], [27.2, 5.0], [27.3, 5.0], [27.4, 5.0], [27.5, 5.0], [27.6, 5.0], [27.7, 5.0], [27.8, 5.0], [27.9, 5.0], [28.0, 5.0], [28.1, 5.0], [28.2, 5.0], [28.3, 5.0], [28.4, 5.0], [28.5, 5.0], [28.6, 5.0], [28.7, 5.0], [28.8, 5.0], [28.9, 5.0], [29.0, 5.0], [29.1, 5.0], [29.2, 5.0], [29.3, 5.0], [29.4, 6.0], [29.5, 6.0], [29.6, 6.0], [29.7, 6.0], [29.8, 6.0], [29.9, 6.0], [30.0, 6.0], [30.1, 6.0], [30.2, 6.0], [30.3, 6.0], [30.4, 6.0], [30.5, 6.0], [30.6, 6.0], [30.7, 6.0], [30.8, 6.0], [30.9, 6.0], [31.0, 6.0], [31.1, 6.0], [31.2, 6.0], [31.3, 6.0], [31.4, 6.0], [31.5, 6.0], [31.6, 6.0], [31.7, 6.0], [31.8, 6.0], [31.9, 6.0], [32.0, 6.0], [32.1, 6.0], [32.2, 6.0], [32.3, 6.0], [32.4, 6.0], [32.5, 6.0], [32.6, 6.0], [32.7, 6.0], [32.8, 6.0], [32.9, 6.0], [33.0, 6.0], [33.1, 6.0], [33.2, 6.0], [33.3, 6.0], [33.4, 6.0], [33.5, 6.0], [33.6, 6.0], [33.7, 6.0], [33.8, 6.0], [33.9, 6.0], [34.0, 6.0], [34.1, 6.0], [34.2, 6.0], [34.3, 6.0], [34.4, 6.0], [34.5, 6.0], [34.6, 6.0], [34.7, 6.0], [34.8, 6.0], [34.9, 6.0], [35.0, 6.0], [35.1, 6.0], [35.2, 6.0], [35.3, 6.0], [35.4, 6.0], [35.5, 6.0], [35.6, 6.0], [35.7, 6.0], [35.8, 6.0], [35.9, 6.0], [36.0, 6.0], [36.1, 6.0], [36.2, 6.0], [36.3, 6.0], [36.4, 6.0], [36.5, 6.0], [36.6, 6.0], [36.7, 6.0], [36.8, 6.0], [36.9, 6.0], [37.0, 6.0], [37.1, 6.0], [37.2, 6.0], [37.3, 6.0], [37.4, 6.0], [37.5, 6.0], [37.6, 6.0], [37.7, 6.0], [37.8, 6.0], [37.9, 6.0], [38.0, 6.0], [38.1, 6.0], [38.2, 6.0], [38.3, 6.0], [38.4, 6.0], [38.5, 6.0], [38.6, 6.0], [38.7, 6.0], [38.8, 6.0], [38.9, 6.0], [39.0, 6.0], [39.1, 6.0], [39.2, 6.0], [39.3, 6.0], [39.4, 6.0], [39.5, 6.0], [39.6, 6.0], [39.7, 6.0], [39.8, 6.0], [39.9, 6.0], [40.0, 6.0], [40.1, 6.0], [40.2, 6.0], [40.3, 6.0], [40.4, 6.0], [40.5, 6.0], [40.6, 6.0], [40.7, 6.0], [40.8, 6.0], [40.9, 6.0], [41.0, 6.0], [41.1, 6.0], [41.2, 6.0], [41.3, 6.0], [41.4, 6.0], [41.5, 6.0], [41.6, 6.0], [41.7, 6.0], [41.8, 6.0], [41.9, 6.0], [42.0, 6.0], [42.1, 6.0], [42.2, 6.0], [42.3, 6.0], [42.4, 6.0], [42.5, 6.0], [42.6, 6.0], [42.7, 6.0], [42.8, 6.0], [42.9, 6.0], [43.0, 6.0], [43.1, 6.0], [43.2, 6.0], [43.3, 6.0], [43.4, 6.0], [43.5, 6.0], [43.6, 6.0], [43.7, 6.0], [43.8, 6.0], [43.9, 6.0], [44.0, 6.0], [44.1, 6.0], [44.2, 6.0], [44.3, 6.0], [44.4, 6.0], [44.5, 6.0], [44.6, 6.0], [44.7, 6.0], [44.8, 6.0], [44.9, 6.0], [45.0, 6.0], [45.1, 6.0], [45.2, 6.0], [45.3, 6.0], [45.4, 6.0], [45.5, 6.0], [45.6, 6.0], [45.7, 6.0], [45.8, 6.0], [45.9, 6.0], [46.0, 6.0], [46.1, 6.0], [46.2, 6.0], [46.3, 6.0], [46.4, 6.0], [46.5, 6.0], [46.6, 6.0], [46.7, 6.0], [46.8, 6.0], [46.9, 6.0], [47.0, 6.0], [47.1, 6.0], [47.2, 6.0], [47.3, 6.0], [47.4, 6.0], [47.5, 6.0], [47.6, 6.0], [47.7, 6.0], [47.8, 6.0], [47.9, 6.0], [48.0, 6.0], [48.1, 6.0], [48.2, 6.0], [48.3, 6.0], [48.4, 6.0], [48.5, 6.0], [48.6, 6.0], [48.7, 6.0], [48.8, 6.0], [48.9, 6.0], [49.0, 6.0], [49.1, 6.0], [49.2, 6.0], [49.3, 6.0], [49.4, 6.0], [49.5, 6.0], [49.6, 6.0], [49.7, 6.0], [49.8, 6.0], [49.9, 6.0], [50.0, 6.0], [50.1, 6.0], [50.2, 6.0], [50.3, 6.0], [50.4, 6.0], [50.5, 6.0], [50.6, 6.0], [50.7, 6.0], [50.8, 6.0], [50.9, 6.0], [51.0, 6.0], [51.1, 6.0], [51.2, 6.0], [51.3, 6.0], [51.4, 6.0], [51.5, 6.0], [51.6, 6.0], [51.7, 6.0], [51.8, 6.0], [51.9, 6.0], [52.0, 6.0], [52.1, 6.0], [52.2, 6.0], [52.3, 6.0], [52.4, 6.0], [52.5, 6.0], [52.6, 6.0], [52.7, 6.0], [52.8, 6.0], [52.9, 6.0], [53.0, 6.0], [53.1, 6.0], [53.2, 6.0], [53.3, 6.0], [53.4, 6.0], [53.5, 6.0], [53.6, 6.0], [53.7, 6.0], [53.8, 6.0], [53.9, 6.0], [54.0, 6.0], [54.1, 6.0], [54.2, 6.0], [54.3, 6.0], [54.4, 6.0], [54.5, 6.0], [54.6, 6.0], [54.7, 6.0], [54.8, 6.0], [54.9, 6.0], [55.0, 6.0], [55.1, 6.0], [55.2, 6.0], [55.3, 6.0], [55.4, 6.0], [55.5, 6.0], [55.6, 6.0], [55.7, 6.0], [55.8, 6.0], [55.9, 6.0], [56.0, 6.0], [56.1, 6.0], [56.2, 6.0], [56.3, 6.0], [56.4, 6.0], [56.5, 6.0], [56.6, 6.0], [56.7, 6.0], [56.8, 6.0], [56.9, 6.0], [57.0, 6.0], [57.1, 6.0], [57.2, 6.0], [57.3, 6.0], [57.4, 6.0], [57.5, 6.0], [57.6, 6.0], [57.7, 6.0], [57.8, 6.0], [57.9, 6.0], [58.0, 6.0], [58.1, 6.0], [58.2, 6.0], [58.3, 6.0], [58.4, 6.0], [58.5, 6.0], [58.6, 6.0], [58.7, 6.0], [58.8, 6.0], [58.9, 6.0], [59.0, 6.0], [59.1, 6.0], [59.2, 6.0], [59.3, 6.0], [59.4, 6.0], [59.5, 6.0], [59.6, 6.0], [59.7, 6.0], [59.8, 6.0], [59.9, 6.0], [60.0, 6.0], [60.1, 6.0], [60.2, 6.0], [60.3, 6.0], [60.4, 6.0], [60.5, 6.0], [60.6, 6.0], [60.7, 6.0], [60.8, 6.0], [60.9, 6.0], [61.0, 6.0], [61.1, 6.0], [61.2, 6.0], [61.3, 6.0], [61.4, 6.0], [61.5, 6.0], [61.6, 6.0], [61.7, 6.0], [61.8, 6.0], [61.9, 6.0], [62.0, 6.0], [62.1, 6.0], [62.2, 6.0], [62.3, 6.0], [62.4, 6.0], [62.5, 6.0], [62.6, 6.0], [62.7, 6.0], [62.8, 6.0], [62.9, 6.0], [63.0, 6.0], [63.1, 6.0], [63.2, 6.0], [63.3, 6.0], [63.4, 6.0], [63.5, 6.0], [63.6, 6.0], [63.7, 6.0], [63.8, 6.0], [63.9, 6.0], [64.0, 6.0], [64.1, 6.0], [64.2, 6.0], [64.3, 6.0], [64.4, 6.0], [64.5, 6.0], [64.6, 6.0], [64.7, 6.0], [64.8, 6.0], [64.9, 6.0], [65.0, 6.0], [65.1, 6.0], [65.2, 6.0], [65.3, 6.0], [65.4, 6.0], [65.5, 6.0], [65.6, 6.0], [65.7, 6.0], [65.8, 6.0], [65.9, 6.0], [66.0, 6.0], [66.1, 6.0], [66.2, 6.0], [66.3, 6.0], [66.4, 6.0], [66.5, 6.0], [66.6, 6.0], [66.7, 6.0], [66.8, 7.0], [66.9, 7.0], [67.0, 7.0], [67.1, 7.0], [67.2, 7.0], [67.3, 7.0], [67.4, 7.0], [67.5, 7.0], [67.6, 7.0], [67.7, 7.0], [67.8, 7.0], [67.9, 7.0], [68.0, 7.0], [68.1, 7.0], [68.2, 7.0], [68.3, 7.0], [68.4, 7.0], [68.5, 7.0], [68.6, 7.0], [68.7, 7.0], [68.8, 7.0], [68.9, 7.0], [69.0, 7.0], [69.1, 7.0], [69.2, 7.0], [69.3, 7.0], [69.4, 7.0], [69.5, 7.0], [69.6, 7.0], [69.7, 7.0], [69.8, 7.0], [69.9, 7.0], [70.0, 7.0], [70.1, 7.0], [70.2, 7.0], [70.3, 7.0], [70.4, 7.0], [70.5, 7.0], [70.6, 7.0], [70.7, 7.0], [70.8, 7.0], [70.9, 7.0], [71.0, 7.0], [71.1, 7.0], [71.2, 7.0], [71.3, 7.0], [71.4, 7.0], [71.5, 7.0], [71.6, 7.0], [71.7, 7.0], [71.8, 7.0], [71.9, 7.0], [72.0, 7.0], [72.1, 7.0], [72.2, 7.0], [72.3, 7.0], [72.4, 7.0], [72.5, 7.0], [72.6, 7.0], [72.7, 7.0], [72.8, 7.0], [72.9, 7.0], [73.0, 7.0], [73.1, 7.0], [73.2, 7.0], [73.3, 7.0], [73.4, 7.0], [73.5, 7.0], [73.6, 7.0], [73.7, 7.0], [73.8, 7.0], [73.9, 7.0], [74.0, 7.0], [74.1, 7.0], [74.2, 7.0], [74.3, 7.0], [74.4, 7.0], [74.5, 7.0], [74.6, 7.0], [74.7, 7.0], [74.8, 7.0], [74.9, 7.0], [75.0, 7.0], [75.1, 7.0], [75.2, 7.0], [75.3, 7.0], [75.4, 7.0], [75.5, 7.0], [75.6, 7.0], [75.7, 7.0], [75.8, 7.0], [75.9, 7.0], [76.0, 7.0], [76.1, 7.0], [76.2, 7.0], [76.3, 7.0], [76.4, 7.0], [76.5, 7.0], [76.6, 7.0], [76.7, 7.0], [76.8, 7.0], [76.9, 7.0], [77.0, 7.0], [77.1, 7.0], [77.2, 7.0], [77.3, 7.0], [77.4, 7.0], [77.5, 7.0], [77.6, 7.0], [77.7, 7.0], [77.8, 7.0], [77.9, 7.0], [78.0, 7.0], [78.1, 7.0], [78.2, 7.0], [78.3, 7.0], [78.4, 7.0], [78.5, 7.0], [78.6, 7.0], [78.7, 7.0], [78.8, 7.0], [78.9, 7.0], [79.0, 7.0], [79.1, 7.0], [79.2, 7.0], [79.3, 7.0], [79.4, 7.0], [79.5, 7.0], [79.6, 7.0], [79.7, 7.0], [79.8, 7.0], [79.9, 7.0], [80.0, 7.0], [80.1, 7.0], [80.2, 7.0], [80.3, 7.0], [80.4, 7.0], [80.5, 7.0], [80.6, 7.0], [80.7, 7.0], [80.8, 7.0], [80.9, 7.0], [81.0, 7.0], [81.1, 7.0], [81.2, 7.0], [81.3, 7.0], [81.4, 7.0], [81.5, 7.0], [81.6, 7.0], [81.7, 7.0], [81.8, 7.0], [81.9, 7.0], [82.0, 7.0], [82.1, 7.0], [82.2, 7.0], [82.3, 7.0], [82.4, 7.0], [82.5, 7.0], [82.6, 7.0], [82.7, 7.0], [82.8, 8.0], [82.9, 8.0], [83.0, 8.0], [83.1, 8.0], [83.2, 8.0], [83.3, 8.0], [83.4, 8.0], [83.5, 8.0], [83.6, 8.0], [83.7, 8.0], [83.8, 8.0], [83.9, 8.0], [84.0, 8.0], [84.1, 8.0], [84.2, 8.0], [84.3, 8.0], [84.4, 8.0], [84.5, 8.0], [84.6, 8.0], [84.7, 8.0], [84.8, 8.0], [84.9, 8.0], [85.0, 8.0], [85.1, 8.0], [85.2, 8.0], [85.3, 8.0], [85.4, 8.0], [85.5, 8.0], [85.6, 8.0], [85.7, 8.0], [85.8, 8.0], [85.9, 8.0], [86.0, 8.0], [86.1, 8.0], [86.2, 8.0], [86.3, 8.0], [86.4, 8.0], [86.5, 8.0], [86.6, 8.0], [86.7, 8.0], [86.8, 8.0], [86.9, 8.0], [87.0, 8.0], [87.1, 8.0], [87.2, 8.0], [87.3, 8.0], [87.4, 8.0], [87.5, 8.0], [87.6, 8.0], [87.7, 8.0], [87.8, 8.0], [87.9, 8.0], [88.0, 8.0], [88.1, 8.0], [88.2, 8.0], [88.3, 8.0], [88.4, 8.0], [88.5, 8.0], [88.6, 8.0], [88.7, 8.0], [88.8, 9.0], [88.9, 9.0], [89.0, 9.0], [89.1, 9.0], [89.2, 9.0], [89.3, 9.0], [89.4, 9.0], [89.5, 9.0], [89.6, 9.0], [89.7, 9.0], [89.8, 9.0], [89.9, 9.0], [90.0, 9.0], [90.1, 9.0], [90.2, 9.0], [90.3, 9.0], [90.4, 9.0], [90.5, 9.0], [90.6, 9.0], [90.7, 9.0], [90.8, 9.0], [90.9, 9.0], [91.0, 9.0], [91.1, 9.0], [91.2, 9.0], [91.3, 9.0], [91.4, 9.0], [91.5, 9.0], [91.6, 9.0], [91.7, 9.0], [91.8, 9.0], [91.9, 9.0], [92.0, 9.0], [92.1, 9.0], [92.2, 9.0], [92.3, 9.0], [92.4, 9.0], [92.5, 9.0], [92.6, 9.0], [92.7, 9.0], [92.8, 9.0], [92.9, 9.0], [93.0, 9.0], [93.1, 9.0], [93.2, 9.0], [93.3, 9.0], [93.4, 9.0], [93.5, 9.0], [93.6, 10.0], [93.7, 10.0], [93.8, 10.0], [93.9, 10.0], [94.0, 10.0], [94.1, 10.0], [94.2, 10.0], [94.3, 10.0], [94.4, 10.0], [94.5, 10.0], [94.6, 10.0], [94.7, 10.0], [94.8, 10.0], [94.9, 10.0], [95.0, 10.0], [95.1, 10.0], [95.2, 10.0], [95.3, 10.0], [95.4, 10.0], [95.5, 10.0], [95.6, 10.0], [95.7, 10.0], [95.8, 10.0], [95.9, 10.0], [96.0, 10.0], [96.1, 10.0], [96.2, 10.0], [96.3, 10.0], [96.4, 10.0], [96.5, 10.0], [96.6, 10.0], [96.7, 10.0], [96.8, 10.0], [96.9, 10.0], [97.0, 10.0], [97.1, 10.0], [97.2, 10.0], [97.3, 10.0], [97.4, 10.0], [97.5, 10.0], [97.6, 10.0], [97.7, 10.0], [97.8, 10.0], [97.9, 10.0], [98.0, 11.0], [98.1, 11.0], [98.2, 11.0], [98.3, 11.0], [98.4, 12.0], [98.5, 12.0], [98.6, 12.0], [98.7, 12.0], [98.8, 12.0], [98.9, 12.0], [99.0, 13.0], [99.1, 13.0], [99.2, 14.0], [99.3, 14.0], [99.4, 15.0], [99.5, 15.0], [99.6, 15.0], [99.7, 36.0], [99.8, 36.0], [99.9, 77.0], [100.0, 77.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 14.0], [0.1, 14.0], [0.2, 14.0], [0.3, 14.0], [0.4, 14.0], [0.5, 14.0], [0.6, 14.0], [0.7, 14.0], [0.8, 14.0], [0.9, 14.0], [1.0, 15.0], [1.1, 15.0], [1.2, 15.0], [1.3, 15.0], [1.4, 15.0], [1.5, 15.0], [1.6, 15.0], [1.7, 15.0], [1.8, 15.0], [1.9, 15.0], [2.0, 15.0], [2.1, 15.0], [2.2, 15.0], [2.3, 15.0], [2.4, 15.0], [2.5, 15.0], [2.6, 15.0], [2.7, 15.0], [2.8, 15.0], [2.9, 15.0], [3.0, 15.0], [3.1, 15.0], [3.2, 15.0], [3.3, 15.0], [3.4, 15.0], [3.5, 15.0], [3.6, 15.0], [3.7, 15.0], [3.8, 16.0], [3.9, 16.0], [4.0, 16.0], [4.1, 16.0], [4.2, 16.0], [4.3, 16.0], [4.4, 16.0], [4.5, 16.0], [4.6, 16.0], [4.7, 16.0], [4.8, 16.0], [4.9, 16.0], [5.0, 16.0], [5.1, 16.0], [5.2, 16.0], [5.3, 16.0], [5.4, 16.0], [5.5, 16.0], [5.6, 16.0], [5.7, 16.0], [5.8, 16.0], [5.9, 16.0], [6.0, 16.0], [6.1, 16.0], [6.2, 16.0], [6.3, 16.0], [6.4, 16.0], [6.5, 16.0], [6.6, 16.0], [6.7, 16.0], [6.8, 16.0], [6.9, 16.0], [7.0, 16.0], [7.1, 16.0], [7.2, 16.0], [7.3, 16.0], [7.4, 16.0], [7.5, 16.0], [7.6, 16.0], [7.7, 16.0], [7.8, 16.0], [7.9, 16.0], [8.0, 16.0], [8.1, 16.0], [8.2, 16.0], [8.3, 16.0], [8.4, 16.0], [8.5, 16.0], [8.6, 16.0], [8.7, 16.0], [8.8, 16.0], [8.9, 16.0], [9.0, 16.0], [9.1, 16.0], [9.2, 16.0], [9.3, 16.0], [9.4, 16.0], [9.5, 16.0], [9.6, 16.0], [9.7, 16.0], [9.8, 16.0], [9.9, 16.0], [10.0, 16.0], [10.1, 16.0], [10.2, 16.0], [10.3, 16.0], [10.4, 16.0], [10.5, 16.0], [10.6, 16.0], [10.7, 16.0], [10.8, 16.0], [10.9, 16.0], [11.0, 16.0], [11.1, 16.0], [11.2, 17.0], [11.3, 17.0], [11.4, 17.0], [11.5, 17.0], [11.6, 17.0], [11.7, 17.0], [11.8, 17.0], [11.9, 17.0], [12.0, 17.0], [12.1, 17.0], [12.2, 17.0], [12.3, 17.0], [12.4, 17.0], [12.5, 17.0], [12.6, 17.0], [12.7, 17.0], [12.8, 17.0], [12.9, 17.0], [13.0, 17.0], [13.1, 17.0], [13.2, 17.0], [13.3, 17.0], [13.4, 17.0], [13.5, 17.0], [13.6, 17.0], [13.7, 17.0], [13.8, 17.0], [13.9, 17.0], [14.0, 17.0], [14.1, 17.0], [14.2, 17.0], [14.3, 17.0], [14.4, 17.0], [14.5, 17.0], [14.6, 17.0], [14.7, 17.0], [14.8, 17.0], [14.9, 17.0], [15.0, 17.0], [15.1, 17.0], [15.2, 17.0], [15.3, 17.0], [15.4, 17.0], [15.5, 17.0], [15.6, 17.0], [15.7, 17.0], [15.8, 17.0], [15.9, 17.0], [16.0, 17.0], [16.1, 17.0], [16.2, 17.0], [16.3, 17.0], [16.4, 17.0], [16.5, 17.0], [16.6, 17.0], [16.7, 17.0], [16.8, 17.0], [16.9, 17.0], [17.0, 17.0], [17.1, 17.0], [17.2, 17.0], [17.3, 17.0], [17.4, 17.0], [17.5, 17.0], [17.6, 17.0], [17.7, 17.0], [17.8, 17.0], [17.9, 17.0], [18.0, 17.0], [18.1, 17.0], [18.2, 17.0], [18.3, 17.0], [18.4, 17.0], [18.5, 17.0], [18.6, 17.0], [18.7, 17.0], [18.8, 17.0], [18.9, 17.0], [19.0, 17.0], [19.1, 17.0], [19.2, 17.0], [19.3, 17.0], [19.4, 18.0], [19.5, 18.0], [19.6, 18.0], [19.7, 18.0], [19.8, 18.0], [19.9, 18.0], [20.0, 18.0], [20.1, 18.0], [20.2, 18.0], [20.3, 18.0], [20.4, 18.0], [20.5, 18.0], [20.6, 18.0], [20.7, 18.0], [20.8, 18.0], [20.9, 18.0], [21.0, 18.0], [21.1, 18.0], [21.2, 18.0], [21.3, 18.0], [21.4, 18.0], [21.5, 18.0], [21.6, 18.0], [21.7, 18.0], [21.8, 18.0], [21.9, 18.0], [22.0, 18.0], [22.1, 18.0], [22.2, 18.0], [22.3, 18.0], [22.4, 18.0], [22.5, 18.0], [22.6, 18.0], [22.7, 18.0], [22.8, 18.0], [22.9, 18.0], [23.0, 18.0], [23.1, 18.0], [23.2, 18.0], [23.3, 18.0], [23.4, 18.0], [23.5, 18.0], [23.6, 18.0], [23.7, 18.0], [23.8, 18.0], [23.9, 18.0], [24.0, 18.0], [24.1, 18.0], [24.2, 18.0], [24.3, 18.0], [24.4, 18.0], [24.5, 18.0], [24.6, 18.0], [24.7, 18.0], [24.8, 18.0], [24.9, 18.0], [25.0, 18.0], [25.1, 18.0], [25.2, 18.0], [25.3, 18.0], [25.4, 18.0], [25.5, 18.0], [25.6, 18.0], [25.7, 18.0], [25.8, 18.0], [25.9, 18.0], [26.0, 18.0], [26.1, 18.0], [26.2, 18.0], [26.3, 18.0], [26.4, 18.0], [26.5, 18.0], [26.6, 18.0], [26.7, 18.0], [26.8, 18.0], [26.9, 18.0], [27.0, 18.0], [27.1, 18.0], [27.2, 18.0], [27.3, 18.0], [27.4, 18.0], [27.5, 18.0], [27.6, 18.0], [27.7, 18.0], [27.8, 18.0], [27.9, 18.0], [28.0, 18.0], [28.1, 18.0], [28.2, 18.0], [28.3, 18.0], [28.4, 18.0], [28.5, 18.0], [28.6, 18.0], [28.7, 18.0], [28.8, 18.0], [28.9, 18.0], [29.0, 18.0], [29.1, 18.0], [29.2, 18.0], [29.3, 18.0], [29.4, 18.0], [29.5, 18.0], [29.6, 18.0], [29.7, 18.0], [29.8, 18.0], [29.9, 18.0], [30.0, 18.0], [30.1, 18.0], [30.2, 18.0], [30.3, 18.0], [30.4, 18.0], [30.5, 18.0], [30.6, 18.0], [30.7, 18.0], [30.8, 18.0], [30.9, 18.0], [31.0, 18.0], [31.1, 18.0], [31.2, 18.0], [31.3, 18.0], [31.4, 18.0], [31.5, 18.0], [31.6, 18.0], [31.7, 18.0], [31.8, 18.0], [31.9, 18.0], [32.0, 18.0], [32.1, 18.0], [32.2, 18.0], [32.3, 18.0], [32.4, 18.0], [32.5, 18.0], [32.6, 18.0], [32.7, 18.0], [32.8, 18.0], [32.9, 18.0], [33.0, 18.0], [33.1, 18.0], [33.2, 18.0], [33.3, 18.0], [33.4, 19.0], [33.5, 19.0], [33.6, 19.0], [33.7, 19.0], [33.8, 19.0], [33.9, 19.0], [34.0, 19.0], [34.1, 19.0], [34.2, 19.0], [34.3, 19.0], [34.4, 19.0], [34.5, 19.0], [34.6, 19.0], [34.7, 19.0], [34.8, 19.0], [34.9, 19.0], [35.0, 19.0], [35.1, 19.0], [35.2, 19.0], [35.3, 19.0], [35.4, 19.0], [35.5, 19.0], [35.6, 19.0], [35.7, 19.0], [35.8, 19.0], [35.9, 19.0], [36.0, 19.0], [36.1, 19.0], [36.2, 19.0], [36.3, 19.0], [36.4, 19.0], [36.5, 19.0], [36.6, 19.0], [36.7, 19.0], [36.8, 19.0], [36.9, 19.0], [37.0, 19.0], [37.1, 19.0], [37.2, 19.0], [37.3, 19.0], [37.4, 19.0], [37.5, 19.0], [37.6, 19.0], [37.7, 19.0], [37.8, 19.0], [37.9, 19.0], [38.0, 19.0], [38.1, 19.0], [38.2, 19.0], [38.3, 19.0], [38.4, 19.0], [38.5, 19.0], [38.6, 19.0], [38.7, 19.0], [38.8, 19.0], [38.9, 19.0], [39.0, 19.0], [39.1, 19.0], [39.2, 19.0], [39.3, 19.0], [39.4, 19.0], [39.5, 19.0], [39.6, 19.0], [39.7, 19.0], [39.8, 19.0], [39.9, 19.0], [40.0, 19.0], [40.1, 19.0], [40.2, 19.0], [40.3, 19.0], [40.4, 19.0], [40.5, 19.0], [40.6, 19.0], [40.7, 19.0], [40.8, 19.0], [40.9, 19.0], [41.0, 19.0], [41.1, 19.0], [41.2, 19.0], [41.3, 19.0], [41.4, 19.0], [41.5, 19.0], [41.6, 19.0], [41.7, 19.0], [41.8, 19.0], [41.9, 19.0], [42.0, 19.0], [42.1, 19.0], [42.2, 19.0], [42.3, 19.0], [42.4, 19.0], [42.5, 19.0], [42.6, 19.0], [42.7, 19.0], [42.8, 19.0], [42.9, 19.0], [43.0, 19.0], [43.1, 19.0], [43.2, 19.0], [43.3, 19.0], [43.4, 19.0], [43.5, 19.0], [43.6, 19.0], [43.7, 19.0], [43.8, 19.0], [43.9, 19.0], [44.0, 19.0], [44.1, 19.0], [44.2, 19.0], [44.3, 19.0], [44.4, 19.0], [44.5, 19.0], [44.6, 19.0], [44.7, 19.0], [44.8, 19.0], [44.9, 19.0], [45.0, 19.0], [45.1, 19.0], [45.2, 19.0], [45.3, 19.0], [45.4, 19.0], [45.5, 19.0], [45.6, 19.0], [45.7, 19.0], [45.8, 19.0], [45.9, 19.0], [46.0, 19.0], [46.1, 19.0], [46.2, 19.0], [46.3, 19.0], [46.4, 19.0], [46.5, 19.0], [46.6, 19.0], [46.7, 19.0], [46.8, 19.0], [46.9, 19.0], [47.0, 19.0], [47.1, 19.0], [47.2, 19.0], [47.3, 19.0], [47.4, 19.0], [47.5, 19.0], [47.6, 19.0], [47.7, 19.0], [47.8, 19.0], [47.9, 19.0], [48.0, 19.0], [48.1, 19.0], [48.2, 19.0], [48.3, 19.0], [48.4, 19.0], [48.5, 19.0], [48.6, 19.0], [48.7, 19.0], [48.8, 19.0], [48.9, 19.0], [49.0, 19.0], [49.1, 19.0], [49.2, 19.0], [49.3, 19.0], [49.4, 19.0], [49.5, 19.0], [49.6, 19.0], [49.7, 19.0], [49.8, 19.0], [49.9, 19.0], [50.0, 19.0], [50.1, 19.0], [50.2, 19.0], [50.3, 19.0], [50.4, 19.0], [50.5, 19.0], [50.6, 19.0], [50.7, 19.0], [50.8, 19.0], [50.9, 19.0], [51.0, 19.0], [51.1, 19.0], [51.2, 19.0], [51.3, 19.0], [51.4, 19.0], [51.5, 19.0], [51.6, 19.0], [51.7, 19.0], [51.8, 19.0], [51.9, 19.0], [52.0, 19.0], [52.1, 19.0], [52.2, 20.0], [52.3, 20.0], [52.4, 20.0], [52.5, 20.0], [52.6, 20.0], [52.7, 20.0], [52.8, 20.0], [52.9, 20.0], [53.0, 20.0], [53.1, 20.0], [53.2, 20.0], [53.3, 20.0], [53.4, 20.0], [53.5, 20.0], [53.6, 20.0], [53.7, 20.0], [53.8, 20.0], [53.9, 20.0], [54.0, 20.0], [54.1, 20.0], [54.2, 20.0], [54.3, 20.0], [54.4, 20.0], [54.5, 20.0], [54.6, 20.0], [54.7, 20.0], [54.8, 20.0], [54.9, 20.0], [55.0, 20.0], [55.1, 20.0], [55.2, 20.0], [55.3, 20.0], [55.4, 20.0], [55.5, 20.0], [55.6, 20.0], [55.7, 20.0], [55.8, 20.0], [55.9, 20.0], [56.0, 20.0], [56.1, 20.0], [56.2, 20.0], [56.3, 20.0], [56.4, 20.0], [56.5, 20.0], [56.6, 20.0], [56.7, 20.0], [56.8, 20.0], [56.9, 20.0], [57.0, 20.0], [57.1, 20.0], [57.2, 20.0], [57.3, 20.0], [57.4, 20.0], [57.5, 20.0], [57.6, 20.0], [57.7, 20.0], [57.8, 20.0], [57.9, 20.0], [58.0, 20.0], [58.1, 20.0], [58.2, 20.0], [58.3, 20.0], [58.4, 20.0], [58.5, 20.0], [58.6, 20.0], [58.7, 20.0], [58.8, 20.0], [58.9, 20.0], [59.0, 20.0], [59.1, 20.0], [59.2, 20.0], [59.3, 20.0], [59.4, 20.0], [59.5, 20.0], [59.6, 20.0], [59.7, 20.0], [59.8, 20.0], [59.9, 20.0], [60.0, 20.0], [60.1, 20.0], [60.2, 20.0], [60.3, 20.0], [60.4, 20.0], [60.5, 20.0], [60.6, 20.0], [60.7, 20.0], [60.8, 20.0], [60.9, 20.0], [61.0, 20.0], [61.1, 20.0], [61.2, 20.0], [61.3, 20.0], [61.4, 20.0], [61.5, 20.0], [61.6, 20.0], [61.7, 20.0], [61.8, 20.0], [61.9, 20.0], [62.0, 20.0], [62.1, 20.0], [62.2, 20.0], [62.3, 20.0], [62.4, 20.0], [62.5, 20.0], [62.6, 20.0], [62.7, 20.0], [62.8, 20.0], [62.9, 20.0], [63.0, 20.0], [63.1, 20.0], [63.2, 20.0], [63.3, 20.0], [63.4, 20.0], [63.5, 20.0], [63.6, 20.0], [63.7, 20.0], [63.8, 20.0], [63.9, 20.0], [64.0, 20.0], [64.1, 20.0], [64.2, 20.0], [64.3, 20.0], [64.4, 20.0], [64.5, 20.0], [64.6, 20.0], [64.7, 20.0], [64.8, 20.0], [64.9, 20.0], [65.0, 20.0], [65.1, 20.0], [65.2, 20.0], [65.3, 20.0], [65.4, 20.0], [65.5, 20.0], [65.6, 20.0], [65.7, 20.0], [65.8, 20.0], [65.9, 20.0], [66.0, 20.0], [66.1, 20.0], [66.2, 21.0], [66.3, 21.0], [66.4, 21.0], [66.5, 21.0], [66.6, 21.0], [66.7, 21.0], [66.8, 21.0], [66.9, 21.0], [67.0, 21.0], [67.1, 21.0], [67.2, 21.0], [67.3, 21.0], [67.4, 21.0], [67.5, 21.0], [67.6, 21.0], [67.7, 21.0], [67.8, 21.0], [67.9, 21.0], [68.0, 21.0], [68.1, 21.0], [68.2, 21.0], [68.3, 21.0], [68.4, 21.0], [68.5, 21.0], [68.6, 21.0], [68.7, 21.0], [68.8, 21.0], [68.9, 21.0], [69.0, 21.0], [69.1, 21.0], [69.2, 21.0], [69.3, 21.0], [69.4, 21.0], [69.5, 21.0], [69.6, 21.0], [69.7, 21.0], [69.8, 21.0], [69.9, 21.0], [70.0, 21.0], [70.1, 21.0], [70.2, 21.0], [70.3, 21.0], [70.4, 21.0], [70.5, 21.0], [70.6, 21.0], [70.7, 21.0], [70.8, 21.0], [70.9, 21.0], [71.0, 21.0], [71.1, 21.0], [71.2, 21.0], [71.3, 21.0], [71.4, 21.0], [71.5, 21.0], [71.6, 21.0], [71.7, 21.0], [71.8, 21.0], [71.9, 21.0], [72.0, 21.0], [72.1, 21.0], [72.2, 21.0], [72.3, 21.0], [72.4, 21.0], [72.5, 21.0], [72.6, 21.0], [72.7, 21.0], [72.8, 21.0], [72.9, 21.0], [73.0, 21.0], [73.1, 21.0], [73.2, 21.0], [73.3, 21.0], [73.4, 21.0], [73.5, 21.0], [73.6, 21.0], [73.7, 21.0], [73.8, 21.0], [73.9, 21.0], [74.0, 21.0], [74.1, 21.0], [74.2, 21.0], [74.3, 21.0], [74.4, 21.0], [74.5, 21.0], [74.6, 21.0], [74.7, 21.0], [74.8, 21.0], [74.9, 21.0], [75.0, 21.0], [75.1, 21.0], [75.2, 21.0], [75.3, 21.0], [75.4, 21.0], [75.5, 21.0], [75.6, 21.0], [75.7, 21.0], [75.8, 21.0], [75.9, 21.0], [76.0, 21.0], [76.1, 21.0], [76.2, 21.0], [76.3, 21.0], [76.4, 22.0], [76.5, 22.0], [76.6, 22.0], [76.7, 22.0], [76.8, 22.0], [76.9, 22.0], [77.0, 22.0], [77.1, 22.0], [77.2, 22.0], [77.3, 22.0], [77.4, 22.0], [77.5, 22.0], [77.6, 22.0], [77.7, 22.0], [77.8, 22.0], [77.9, 22.0], [78.0, 22.0], [78.1, 22.0], [78.2, 22.0], [78.3, 22.0], [78.4, 22.0], [78.5, 22.0], [78.6, 22.0], [78.7, 22.0], [78.8, 22.0], [78.9, 22.0], [79.0, 22.0], [79.1, 22.0], [79.2, 22.0], [79.3, 22.0], [79.4, 22.0], [79.5, 22.0], [79.6, 22.0], [79.7, 22.0], [79.8, 22.0], [79.9, 22.0], [80.0, 22.0], [80.1, 22.0], [80.2, 22.0], [80.3, 22.0], [80.4, 22.0], [80.5, 22.0], [80.6, 22.0], [80.7, 22.0], [80.8, 22.0], [80.9, 22.0], [81.0, 22.0], [81.1, 22.0], [81.2, 22.0], [81.3, 22.0], [81.4, 22.0], [81.5, 22.0], [81.6, 22.0], [81.7, 22.0], [81.8, 22.0], [81.9, 22.0], [82.0, 22.0], [82.1, 22.0], [82.2, 22.0], [82.3, 22.0], [82.4, 22.0], [82.5, 22.0], [82.6, 22.0], [82.7, 22.0], [82.8, 22.0], [82.9, 22.0], [83.0, 22.0], [83.1, 22.0], [83.2, 22.0], [83.3, 22.0], [83.4, 22.0], [83.5, 22.0], [83.6, 22.0], [83.7, 22.0], [83.8, 22.0], [83.9, 22.0], [84.0, 22.0], [84.1, 22.0], [84.2, 22.0], [84.3, 22.0], [84.4, 22.0], [84.5, 22.0], [84.6, 22.0], [84.7, 22.0], [84.8, 22.0], [84.9, 22.0], [85.0, 22.0], [85.1, 22.0], [85.2, 23.0], [85.3, 23.0], [85.4, 23.0], [85.5, 23.0], [85.6, 23.0], [85.7, 23.0], [85.8, 23.0], [85.9, 23.0], [86.0, 23.0], [86.1, 23.0], [86.2, 23.0], [86.3, 23.0], [86.4, 23.0], [86.5, 23.0], [86.6, 23.0], [86.7, 23.0], [86.8, 23.0], [86.9, 23.0], [87.0, 23.0], [87.1, 23.0], [87.2, 23.0], [87.3, 23.0], [87.4, 23.0], [87.5, 23.0], [87.6, 23.0], [87.7, 23.0], [87.8, 23.0], [87.9, 23.0], [88.0, 23.0], [88.1, 23.0], [88.2, 23.0], [88.3, 23.0], [88.4, 23.0], [88.5, 23.0], [88.6, 23.0], [88.7, 23.0], [88.8, 23.0], [88.9, 23.0], [89.0, 23.0], [89.1, 23.0], [89.2, 23.0], [89.3, 23.0], [89.4, 23.0], [89.5, 23.0], [89.6, 23.0], [89.7, 23.0], [89.8, 23.0], [89.9, 23.0], [90.0, 24.0], [90.1, 24.0], [90.2, 24.0], [90.3, 24.0], [90.4, 24.0], [90.5, 24.0], [90.6, 24.0], [90.7, 24.0], [90.8, 24.0], [90.9, 24.0], [91.0, 24.0], [91.1, 24.0], [91.2, 24.0], [91.3, 24.0], [91.4, 24.0], [91.5, 24.0], [91.6, 24.0], [91.7, 24.0], [91.8, 24.0], [91.9, 24.0], [92.0, 24.0], [92.1, 24.0], [92.2, 24.0], [92.3, 24.0], [92.4, 24.0], [92.5, 24.0], [92.6, 24.0], [92.7, 24.0], [92.8, 24.0], [92.9, 24.0], [93.0, 24.0], [93.1, 24.0], [93.2, 24.0], [93.3, 24.0], [93.4, 24.0], [93.5, 24.0], [93.6, 24.0], [93.7, 24.0], [93.8, 25.0], [93.9, 25.0], [94.0, 25.0], [94.1, 25.0], [94.2, 25.0], [94.3, 25.0], [94.4, 25.0], [94.5, 25.0], [94.6, 25.0], [94.7, 25.0], [94.8, 25.0], [94.9, 25.0], [95.0, 25.0], [95.1, 25.0], [95.2, 25.0], [95.3, 25.0], [95.4, 25.0], [95.5, 25.0], [95.6, 26.0], [95.7, 26.0], [95.8, 26.0], [95.9, 26.0], [96.0, 26.0], [96.1, 26.0], [96.2, 28.0], [96.3, 28.0], [96.4, 29.0], [96.5, 29.0], [96.6, 29.0], [96.7, 29.0], [96.8, 30.0], [96.9, 30.0], [97.0, 30.0], [97.1, 30.0], [97.2, 30.0], [97.3, 30.0], [97.4, 31.0], [97.5, 31.0], [97.6, 32.0], [97.7, 32.0], [97.8, 33.0], [97.9, 33.0], [98.0, 33.0], [98.1, 33.0], [98.2, 33.0], [98.3, 33.0], [98.4, 34.0], [98.5, 34.0], [98.6, 35.0], [98.7, 35.0], [98.8, 37.0], [98.9, 37.0], [99.0, 38.0], [99.1, 38.0], [99.2, 39.0], [99.3, 39.0], [99.4, 42.0], [99.5, 42.0], [99.6, 42.0], [99.7, 42.0], [99.8, 42.0], [99.9, 43.0], [100.0, 43.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 3.0], [1.5, 3.0], [1.6, 3.0], [1.7, 3.0], [1.8, 3.0], [1.9, 3.0], [2.0, 3.0], [2.1, 3.0], [2.2, 3.0], [2.3, 3.0], [2.4, 3.0], [2.5, 3.0], [2.6, 3.0], [2.7, 3.0], [2.8, 3.0], [2.9, 3.0], [3.0, 3.0], [3.1, 3.0], [3.2, 3.0], [3.3, 3.0], [3.4, 3.0], [3.5, 3.0], [3.6, 3.0], [3.7, 3.0], [3.8, 3.0], [3.9, 3.0], [4.0, 3.0], [4.1, 3.0], [4.2, 3.0], [4.3, 3.0], [4.4, 3.0], [4.5, 3.0], [4.6, 3.0], [4.7, 3.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 4.0], [47.5, 4.0], [47.6, 4.0], [47.7, 4.0], [47.8, 4.0], [47.9, 4.0], [48.0, 4.0], [48.1, 4.0], [48.2, 4.0], [48.3, 4.0], [48.4, 4.0], [48.5, 4.0], [48.6, 4.0], [48.7, 4.0], [48.8, 4.0], [48.9, 4.0], [49.0, 4.0], [49.1, 4.0], [49.2, 4.0], [49.3, 4.0], [49.4, 4.0], [49.5, 4.0], [49.6, 4.0], [49.7, 4.0], [49.8, 4.0], [49.9, 4.0], [50.0, 4.0], [50.1, 4.0], [50.2, 4.0], [50.3, 4.0], [50.4, 4.0], [50.5, 4.0], [50.6, 4.0], [50.7, 4.0], [50.8, 4.0], [50.9, 4.0], [51.0, 4.0], [51.1, 4.0], [51.2, 4.0], [51.3, 4.0], [51.4, 4.0], [51.5, 4.0], [51.6, 4.0], [51.7, 4.0], [51.8, 4.0], [51.9, 4.0], [52.0, 4.0], [52.1, 4.0], [52.2, 4.0], [52.3, 4.0], [52.4, 4.0], [52.5, 4.0], [52.6, 4.0], [52.7, 4.0], [52.8, 4.0], [52.9, 4.0], [53.0, 4.0], [53.1, 4.0], [53.2, 4.0], [53.3, 4.0], [53.4, 4.0], [53.5, 4.0], [53.6, 4.0], [53.7, 4.0], [53.8, 4.0], [53.9, 4.0], [54.0, 4.0], [54.1, 4.0], [54.2, 4.0], [54.3, 4.0], [54.4, 4.0], [54.5, 4.0], [54.6, 4.0], [54.7, 4.0], [54.8, 4.0], [54.9, 4.0], [55.0, 4.0], [55.1, 4.0], [55.2, 4.0], [55.3, 4.0], [55.4, 4.0], [55.5, 4.0], [55.6, 4.0], [55.7, 4.0], [55.8, 4.0], [55.9, 4.0], [56.0, 4.0], [56.1, 4.0], [56.2, 4.0], [56.3, 4.0], [56.4, 4.0], [56.5, 4.0], [56.6, 4.0], [56.7, 4.0], [56.8, 4.0], [56.9, 4.0], [57.0, 4.0], [57.1, 4.0], [57.2, 4.0], [57.3, 4.0], [57.4, 4.0], [57.5, 4.0], [57.6, 4.0], [57.7, 4.0], [57.8, 4.0], [57.9, 4.0], [58.0, 4.0], [58.1, 4.0], [58.2, 4.0], [58.3, 4.0], [58.4, 4.0], [58.5, 4.0], [58.6, 4.0], [58.7, 4.0], [58.8, 4.0], [58.9, 4.0], [59.0, 4.0], [59.1, 4.0], [59.2, 4.0], [59.3, 4.0], [59.4, 4.0], [59.5, 4.0], [59.6, 4.0], [59.7, 4.0], [59.8, 4.0], [59.9, 4.0], [60.0, 4.0], [60.1, 4.0], [60.2, 4.0], [60.3, 4.0], [60.4, 4.0], [60.5, 4.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 5.0], [72.9, 5.0], [73.0, 5.0], [73.1, 5.0], [73.2, 5.0], [73.3, 5.0], [73.4, 5.0], [73.5, 5.0], [73.6, 5.0], [73.7, 5.0], [73.8, 5.0], [73.9, 5.0], [74.0, 5.0], [74.1, 5.0], [74.2, 5.0], [74.3, 5.0], [74.4, 5.0], [74.5, 5.0], [74.6, 5.0], [74.7, 5.0], [74.8, 5.0], [74.9, 5.0], [75.0, 5.0], [75.1, 5.0], [75.2, 5.0], [75.3, 5.0], [75.4, 5.0], [75.5, 5.0], [75.6, 5.0], [75.7, 5.0], [75.8, 5.0], [75.9, 5.0], [76.0, 5.0], [76.1, 5.0], [76.2, 5.0], [76.3, 5.0], [76.4, 5.0], [76.5, 5.0], [76.6, 5.0], [76.7, 5.0], [76.8, 5.0], [76.9, 5.0], [77.0, 5.0], [77.1, 5.0], [77.2, 5.0], [77.3, 5.0], [77.4, 5.0], [77.5, 5.0], [77.6, 5.0], [77.7, 5.0], [77.8, 5.0], [77.9, 5.0], [78.0, 5.0], [78.1, 5.0], [78.2, 5.0], [78.3, 5.0], [78.4, 5.0], [78.5, 5.0], [78.6, 5.0], [78.7, 5.0], [78.8, 5.0], [78.9, 5.0], [79.0, 5.0], [79.1, 5.0], [79.2, 5.0], [79.3, 5.0], [79.4, 5.0], [79.5, 5.0], [79.6, 5.0], [79.7, 5.0], [79.8, 5.0], [79.9, 5.0], [80.0, 5.0], [80.1, 5.0], [80.2, 5.0], [80.3, 5.0], [80.4, 5.0], [80.5, 5.0], [80.6, 5.0], [80.7, 5.0], [80.8, 5.0], [80.9, 5.0], [81.0, 5.0], [81.1, 5.0], [81.2, 5.0], [81.3, 5.0], [81.4, 5.0], [81.5, 5.0], [81.6, 5.0], [81.7, 5.0], [81.8, 5.0], [81.9, 5.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 6.0], [88.7, 6.0], [88.8, 6.0], [88.9, 6.0], [89.0, 6.0], [89.1, 6.0], [89.2, 6.0], [89.3, 6.0], [89.4, 6.0], [89.5, 6.0], [89.6, 6.0], [89.7, 6.0], [89.8, 6.0], [89.9, 6.0], [90.0, 6.0], [90.1, 6.0], [90.2, 6.0], [90.3, 6.0], [90.4, 6.0], [90.5, 6.0], [90.6, 6.0], [90.7, 6.0], [90.8, 6.0], [90.9, 6.0], [91.0, 6.0], [91.1, 6.0], [91.2, 6.0], [91.3, 6.0], [91.4, 6.0], [91.5, 6.0], [91.6, 6.0], [91.7, 6.0], [91.8, 6.0], [91.9, 6.0], [92.0, 6.0], [92.1, 6.0], [92.2, 6.0], [92.3, 6.0], [92.4, 6.0], [92.5, 6.0], [92.6, 6.0], [92.7, 6.0], [92.8, 6.0], [92.9, 6.0], [93.0, 6.0], [93.1, 6.0], [93.2, 6.0], [93.3, 6.0], [93.4, 6.0], [93.5, 6.0], [93.6, 6.0], [93.7, 6.0], [93.8, 6.0], [93.9, 6.0], [94.0, 6.0], [94.1, 6.0], [94.2, 6.0], [94.3, 6.0], [94.4, 6.0], [94.5, 6.0], [94.6, 6.0], [94.7, 6.0], [94.8, 6.0], [94.9, 6.0], [95.0, 6.0], [95.1, 6.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 7.0], [95.7, 7.0], [95.8, 7.0], [95.9, 7.0], [96.0, 7.0], [96.1, 7.0], [96.2, 7.0], [96.3, 7.0], [96.4, 7.0], [96.5, 7.0], [96.6, 7.0], [96.7, 7.0], [96.8, 7.0], [96.9, 7.0], [97.0, 7.0], [97.1, 7.0], [97.2, 7.0], [97.3, 7.0], [97.4, 7.0], [97.5, 7.0], [97.6, 7.0], [97.7, 7.0], [97.8, 7.0], [97.9, 7.0], [98.0, 7.0], [98.1, 7.0], [98.2, 7.0], [98.3, 7.0], [98.4, 7.0], [98.5, 7.0], [98.6, 7.0], [98.7, 7.0], [98.8, 8.0], [98.9, 8.0], [99.0, 9.0], [99.1, 9.0], [99.2, 10.0], [99.3, 10.0], [99.4, 10.0], [99.5, 10.0], [99.6, 10.0], [99.7, 10.0], [99.8, 11.0], [99.9, 11.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 3.0], [0.3, 3.0], [0.4, 3.0], [0.5, 3.0], [0.6, 3.0], [0.7, 3.0], [0.8, 3.0], [0.9, 3.0], [1.0, 3.0], [1.1, 3.0], [1.2, 3.0], [1.3, 3.0], [1.4, 3.0], [1.5, 3.0], [1.6, 3.0], [1.7, 3.0], [1.8, 3.0], [1.9, 3.0], [2.0, 3.0], [2.1, 3.0], [2.2, 3.0], [2.3, 3.0], [2.4, 3.0], [2.5, 3.0], [2.6, 3.0], [2.7, 3.0], [2.8, 3.0], [2.9, 3.0], [3.0, 3.0], [3.1, 3.0], [3.2, 3.0], [3.3, 3.0], [3.4, 3.0], [3.5, 3.0], [3.6, 3.0], [3.7, 3.0], [3.8, 3.0], [3.9, 3.0], [4.0, 3.0], [4.1, 3.0], [4.2, 3.0], [4.3, 3.0], [4.4, 3.0], [4.5, 3.0], [4.6, 3.0], [4.7, 3.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 4.0], [53.3, 4.0], [53.4, 4.0], [53.5, 4.0], [53.6, 4.0], [53.7, 4.0], [53.8, 4.0], [53.9, 4.0], [54.0, 4.0], [54.1, 4.0], [54.2, 4.0], [54.3, 4.0], [54.4, 4.0], [54.5, 4.0], [54.6, 4.0], [54.7, 4.0], [54.8, 4.0], [54.9, 4.0], [55.0, 4.0], [55.1, 4.0], [55.2, 4.0], [55.3, 4.0], [55.4, 4.0], [55.5, 4.0], [55.6, 4.0], [55.7, 4.0], [55.8, 4.0], [55.9, 4.0], [56.0, 4.0], [56.1, 4.0], [56.2, 4.0], [56.3, 4.0], [56.4, 4.0], [56.5, 4.0], [56.6, 4.0], [56.7, 4.0], [56.8, 4.0], [56.9, 4.0], [57.0, 4.0], [57.1, 4.0], [57.2, 4.0], [57.3, 4.0], [57.4, 4.0], [57.5, 4.0], [57.6, 4.0], [57.7, 4.0], [57.8, 4.0], [57.9, 4.0], [58.0, 4.0], [58.1, 4.0], [58.2, 4.0], [58.3, 4.0], [58.4, 4.0], [58.5, 4.0], [58.6, 4.0], [58.7, 4.0], [58.8, 4.0], [58.9, 4.0], [59.0, 4.0], [59.1, 4.0], [59.2, 4.0], [59.3, 4.0], [59.4, 4.0], [59.5, 4.0], [59.6, 4.0], [59.7, 4.0], [59.8, 4.0], [59.9, 4.0], [60.0, 4.0], [60.1, 4.0], [60.2, 4.0], [60.3, 4.0], [60.4, 4.0], [60.5, 4.0], [60.6, 4.0], [60.7, 4.0], [60.8, 4.0], [60.9, 4.0], [61.0, 4.0], [61.1, 4.0], [61.2, 4.0], [61.3, 4.0], [61.4, 4.0], [61.5, 4.0], [61.6, 4.0], [61.7, 4.0], [61.8, 4.0], [61.9, 4.0], [62.0, 4.0], [62.1, 4.0], [62.2, 4.0], [62.3, 4.0], [62.4, 4.0], [62.5, 4.0], [62.6, 4.0], [62.7, 4.0], [62.8, 4.0], [62.9, 4.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 4.0], [84.7, 4.0], [84.8, 4.0], [84.9, 4.0], [85.0, 4.0], [85.1, 4.0], [85.2, 4.0], [85.3, 4.0], [85.4, 4.0], [85.5, 4.0], [85.6, 4.0], [85.7, 4.0], [85.8, 4.0], [85.9, 4.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 6.0], [93.7, 6.0], [93.8, 6.0], [93.9, 6.0], [94.0, 6.0], [94.1, 6.0], [94.2, 6.0], [94.3, 6.0], [94.4, 6.0], [94.5, 6.0], [94.6, 6.0], [94.7, 6.0], [94.8, 6.0], [94.9, 6.0], [95.0, 6.0], [95.1, 6.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 6.0], [95.7, 6.0], [95.8, 6.0], [95.9, 6.0], [96.0, 6.0], [96.1, 6.0], [96.2, 6.0], [96.3, 6.0], [96.4, 6.0], [96.5, 6.0], [96.6, 6.0], [96.7, 6.0], [96.8, 6.0], [96.9, 6.0], [97.0, 6.0], [97.1, 6.0], [97.2, 6.0], [97.3, 6.0], [97.4, 6.0], [97.5, 6.0], [97.6, 6.0], [97.7, 6.0], [97.8, 6.0], [97.9, 6.0], [98.0, 7.0], [98.1, 7.0], [98.2, 7.0], [98.3, 7.0], [98.4, 7.0], [98.5, 7.0], [98.6, 7.0], [98.7, 7.0], [98.8, 7.0], [98.9, 7.0], [99.0, 8.0], [99.1, 8.0], [99.2, 8.0], [99.3, 8.0], [99.4, 8.0], [99.5, 8.0], [99.6, 8.0], [99.7, 8.0], [99.8, 8.0], [99.9, 8.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 3.0], [2.1, 3.0], [2.2, 3.0], [2.3, 3.0], [2.4, 3.0], [2.5, 3.0], [2.6, 3.0], [2.7, 3.0], [2.8, 3.0], [2.9, 3.0], [3.0, 3.0], [3.1, 3.0], [3.2, 3.0], [3.3, 3.0], [3.4, 3.0], [3.5, 3.0], [3.6, 3.0], [3.7, 3.0], [3.8, 3.0], [3.9, 3.0], [4.0, 3.0], [4.1, 3.0], [4.2, 3.0], [4.3, 3.0], [4.4, 3.0], [4.5, 3.0], [4.6, 3.0], [4.7, 3.0], [4.8, 3.0], [4.9, 3.0], [5.0, 3.0], [5.1, 3.0], [5.2, 3.0], [5.3, 3.0], [5.4, 3.0], [5.5, 3.0], [5.6, 3.0], [5.7, 3.0], [5.8, 3.0], [5.9, 3.0], [6.0, 3.0], [6.1, 3.0], [6.2, 3.0], [6.3, 3.0], [6.4, 3.0], [6.5, 3.0], [6.6, 3.0], [6.7, 3.0], [6.8, 3.0], [6.9, 3.0], [7.0, 3.0], [7.1, 3.0], [7.2, 3.0], [7.3, 3.0], [7.4, 3.0], [7.5, 3.0], [7.6, 3.0], [7.7, 3.0], [7.8, 3.0], [7.9, 3.0], [8.0, 3.0], [8.1, 3.0], [8.2, 3.0], [8.3, 3.0], [8.4, 3.0], [8.5, 3.0], [8.6, 3.0], [8.7, 3.0], [8.8, 3.0], [8.9, 3.0], [9.0, 3.0], [9.1, 3.0], [9.2, 3.0], [9.3, 3.0], [9.4, 3.0], [9.5, 3.0], [9.6, 3.0], [9.7, 3.0], [9.8, 3.0], [9.9, 3.0], [10.0, 3.0], [10.1, 3.0], [10.2, 3.0], [10.3, 3.0], [10.4, 3.0], [10.5, 3.0], [10.6, 3.0], [10.7, 3.0], [10.8, 3.0], [10.9, 3.0], [11.0, 3.0], [11.1, 3.0], [11.2, 3.0], [11.3, 3.0], [11.4, 3.0], [11.5, 3.0], [11.6, 3.0], [11.7, 3.0], [11.8, 3.0], [11.9, 3.0], [12.0, 3.0], [12.1, 3.0], [12.2, 3.0], [12.3, 3.0], [12.4, 3.0], [12.5, 3.0], [12.6, 3.0], [12.7, 3.0], [12.8, 3.0], [12.9, 3.0], [13.0, 3.0], [13.1, 3.0], [13.2, 3.0], [13.3, 3.0], [13.4, 3.0], [13.5, 3.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 4.0], [63.1, 4.0], [63.2, 4.0], [63.3, 4.0], [63.4, 4.0], [63.5, 4.0], [63.6, 4.0], [63.7, 4.0], [63.8, 4.0], [63.9, 4.0], [64.0, 4.0], [64.1, 4.0], [64.2, 4.0], [64.3, 4.0], [64.4, 4.0], [64.5, 4.0], [64.6, 4.0], [64.7, 4.0], [64.8, 4.0], [64.9, 4.0], [65.0, 4.0], [65.1, 4.0], [65.2, 4.0], [65.3, 4.0], [65.4, 4.0], [65.5, 4.0], [65.6, 4.0], [65.7, 4.0], [65.8, 4.0], [65.9, 4.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 6.0], [93.7, 6.0], [93.8, 6.0], [93.9, 6.0], [94.0, 6.0], [94.1, 6.0], [94.2, 6.0], [94.3, 6.0], [94.4, 6.0], [94.5, 6.0], [94.6, 6.0], [94.7, 6.0], [94.8, 6.0], [94.9, 6.0], [95.0, 6.0], [95.1, 6.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 6.0], [95.7, 6.0], [95.8, 6.0], [95.9, 6.0], [96.0, 6.0], [96.1, 6.0], [96.2, 6.0], [96.3, 6.0], [96.4, 6.0], [96.5, 6.0], [96.6, 6.0], [96.7, 6.0], [96.8, 6.0], [96.9, 6.0], [97.0, 6.0], [97.1, 6.0], [97.2, 6.0], [97.3, 6.0], [97.4, 6.0], [97.5, 6.0], [97.6, 6.0], [97.7, 6.0], [97.8, 6.0], [97.9, 6.0], [98.0, 6.0], [98.1, 6.0], [98.2, 7.0], [98.3, 7.0], [98.4, 7.0], [98.5, 7.0], [98.6, 7.0], [98.7, 7.0], [98.8, 7.0], [98.9, 7.0], [99.0, 8.0], [99.1, 8.0], [99.2, 8.0], [99.3, 8.0], [99.4, 8.0], [99.5, 8.0], [99.6, 9.0], [99.7, 9.0], [99.8, 13.0], [99.9, 13.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 2.0], [10.5, 2.0], [10.6, 2.0], [10.7, 2.0], [10.8, 2.0], [10.9, 2.0], [11.0, 2.0], [11.1, 2.0], [11.2, 2.0], [11.3, 2.0], [11.4, 2.0], [11.5, 2.0], [11.6, 2.0], [11.7, 2.0], [11.8, 2.0], [11.9, 2.0], [12.0, 2.0], [12.1, 2.0], [12.2, 2.0], [12.3, 2.0], [12.4, 2.0], [12.5, 2.0], [12.6, 2.0], [12.7, 2.0], [12.8, 2.0], [12.9, 2.0], [13.0, 2.0], [13.1, 2.0], [13.2, 2.0], [13.3, 2.0], [13.4, 2.0], [13.5, 2.0], [13.6, 2.0], [13.7, 2.0], [13.8, 2.0], [13.9, 2.0], [14.0, 2.0], [14.1, 2.0], [14.2, 2.0], [14.3, 2.0], [14.4, 2.0], [14.5, 2.0], [14.6, 2.0], [14.7, 2.0], [14.8, 2.0], [14.9, 2.0], [15.0, 2.0], [15.1, 2.0], [15.2, 2.0], [15.3, 2.0], [15.4, 2.0], [15.5, 2.0], [15.6, 2.0], [15.7, 2.0], [15.8, 2.0], [15.9, 2.0], [16.0, 2.0], [16.1, 2.0], [16.2, 2.0], [16.3, 2.0], [16.4, 2.0], [16.5, 2.0], [16.6, 2.0], [16.7, 2.0], [16.8, 2.0], [16.9, 2.0], [17.0, 2.0], [17.1, 2.0], [17.2, 2.0], [17.3, 2.0], [17.4, 2.0], [17.5, 2.0], [17.6, 2.0], [17.7, 2.0], [17.8, 2.0], [17.9, 2.0], [18.0, 2.0], [18.1, 2.0], [18.2, 2.0], [18.3, 2.0], [18.4, 2.0], [18.5, 2.0], [18.6, 2.0], [18.7, 2.0], [18.8, 2.0], [18.9, 2.0], [19.0, 2.0], [19.1, 2.0], [19.2, 2.0], [19.3, 2.0], [19.4, 2.0], [19.5, 2.0], [19.6, 2.0], [19.7, 2.0], [19.8, 2.0], [19.9, 2.0], [20.0, 2.0], [20.1, 2.0], [20.2, 2.0], [20.3, 2.0], [20.4, 2.0], [20.5, 2.0], [20.6, 2.0], [20.7, 2.0], [20.8, 2.0], [20.9, 2.0], [21.0, 2.0], [21.1, 2.0], [21.2, 2.0], [21.3, 2.0], [21.4, 2.0], [21.5, 2.0], [21.6, 2.0], [21.7, 2.0], [21.8, 2.0], [21.9, 2.0], [22.0, 2.0], [22.1, 2.0], [22.2, 2.0], [22.3, 2.0], [22.4, 2.0], [22.5, 2.0], [22.6, 2.0], [22.7, 2.0], [22.8, 2.0], [22.9, 2.0], [23.0, 2.0], [23.1, 2.0], [23.2, 2.0], [23.3, 2.0], [23.4, 2.0], [23.5, 2.0], [23.6, 2.0], [23.7, 2.0], [23.8, 2.0], [23.9, 2.0], [24.0, 2.0], [24.1, 2.0], [24.2, 2.0], [24.3, 2.0], [24.4, 2.0], [24.5, 2.0], [24.6, 2.0], [24.7, 2.0], [24.8, 2.0], [24.9, 2.0], [25.0, 2.0], [25.1, 2.0], [25.2, 2.0], [25.3, 2.0], [25.4, 2.0], [25.5, 2.0], [25.6, 2.0], [25.7, 2.0], [25.8, 2.0], [25.9, 2.0], [26.0, 2.0], [26.1, 2.0], [26.2, 2.0], [26.3, 2.0], [26.4, 2.0], [26.5, 2.0], [26.6, 2.0], [26.7, 2.0], [26.8, 2.0], [26.9, 2.0], [27.0, 2.0], [27.1, 2.0], [27.2, 2.0], [27.3, 2.0], [27.4, 2.0], [27.5, 2.0], [27.6, 2.0], [27.7, 2.0], [27.8, 2.0], [27.9, 2.0], [28.0, 2.0], [28.1, 2.0], [28.2, 2.0], [28.3, 2.0], [28.4, 2.0], [28.5, 2.0], [28.6, 2.0], [28.7, 2.0], [28.8, 2.0], [28.9, 2.0], [29.0, 2.0], [29.1, 2.0], [29.2, 2.0], [29.3, 2.0], [29.4, 2.0], [29.5, 2.0], [29.6, 2.0], [29.7, 2.0], [29.8, 2.0], [29.9, 2.0], [30.0, 2.0], [30.1, 2.0], [30.2, 2.0], [30.3, 2.0], [30.4, 2.0], [30.5, 2.0], [30.6, 2.0], [30.7, 2.0], [30.8, 2.0], [30.9, 2.0], [31.0, 2.0], [31.1, 2.0], [31.2, 2.0], [31.3, 2.0], [31.4, 2.0], [31.5, 2.0], [31.6, 2.0], [31.7, 2.0], [31.8, 2.0], [31.9, 2.0], [32.0, 2.0], [32.1, 2.0], [32.2, 2.0], [32.3, 2.0], [32.4, 2.0], [32.5, 2.0], [32.6, 2.0], [32.7, 2.0], [32.8, 2.0], [32.9, 2.0], [33.0, 2.0], [33.1, 2.0], [33.2, 2.0], [33.3, 2.0], [33.4, 2.0], [33.5, 2.0], [33.6, 2.0], [33.7, 2.0], [33.8, 2.0], [33.9, 2.0], [34.0, 2.0], [34.1, 2.0], [34.2, 2.0], [34.3, 2.0], [34.4, 2.0], [34.5, 2.0], [34.6, 2.0], [34.7, 2.0], [34.8, 2.0], [34.9, 2.0], [35.0, 2.0], [35.1, 2.0], [35.2, 2.0], [35.3, 2.0], [35.4, 2.0], [35.5, 2.0], [35.6, 2.0], [35.7, 2.0], [35.8, 2.0], [35.9, 2.0], [36.0, 2.0], [36.1, 2.0], [36.2, 2.0], [36.3, 2.0], [36.4, 2.0], [36.5, 2.0], [36.6, 2.0], [36.7, 2.0], [36.8, 2.0], [36.9, 2.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 3.0], [63.1, 3.0], [63.2, 3.0], [63.3, 3.0], [63.4, 3.0], [63.5, 3.0], [63.6, 3.0], [63.7, 3.0], [63.8, 3.0], [63.9, 3.0], [64.0, 3.0], [64.1, 3.0], [64.2, 3.0], [64.3, 3.0], [64.4, 3.0], [64.5, 3.0], [64.6, 3.0], [64.7, 3.0], [64.8, 3.0], [64.9, 3.0], [65.0, 3.0], [65.1, 3.0], [65.2, 3.0], [65.3, 3.0], [65.4, 3.0], [65.5, 3.0], [65.6, 3.0], [65.7, 3.0], [65.8, 3.0], [65.9, 3.0], [66.0, 3.0], [66.1, 3.0], [66.2, 3.0], [66.3, 3.0], [66.4, 3.0], [66.5, 3.0], [66.6, 3.0], [66.7, 3.0], [66.8, 3.0], [66.9, 3.0], [67.0, 3.0], [67.1, 3.0], [67.2, 3.0], [67.3, 3.0], [67.4, 3.0], [67.5, 3.0], [67.6, 3.0], [67.7, 3.0], [67.8, 3.0], [67.9, 3.0], [68.0, 3.0], [68.1, 3.0], [68.2, 3.0], [68.3, 3.0], [68.4, 3.0], [68.5, 3.0], [68.6, 3.0], [68.7, 3.0], [68.8, 3.0], [68.9, 3.0], [69.0, 3.0], [69.1, 3.0], [69.2, 3.0], [69.3, 3.0], [69.4, 3.0], [69.5, 3.0], [69.6, 3.0], [69.7, 3.0], [69.8, 3.0], [69.9, 3.0], [70.0, 3.0], [70.1, 3.0], [70.2, 3.0], [70.3, 3.0], [70.4, 3.0], [70.5, 3.0], [70.6, 3.0], [70.7, 3.0], [70.8, 3.0], [70.9, 3.0], [71.0, 3.0], [71.1, 3.0], [71.2, 3.0], [71.3, 3.0], [71.4, 3.0], [71.5, 3.0], [71.6, 3.0], [71.7, 3.0], [71.8, 3.0], [71.9, 3.0], [72.0, 3.0], [72.1, 3.0], [72.2, 3.0], [72.3, 3.0], [72.4, 3.0], [72.5, 3.0], [72.6, 3.0], [72.7, 3.0], [72.8, 3.0], [72.9, 3.0], [73.0, 3.0], [73.1, 3.0], [73.2, 3.0], [73.3, 3.0], [73.4, 3.0], [73.5, 3.0], [73.6, 3.0], [73.7, 3.0], [73.8, 3.0], [73.9, 3.0], [74.0, 3.0], [74.1, 3.0], [74.2, 3.0], [74.3, 3.0], [74.4, 3.0], [74.5, 3.0], [74.6, 3.0], [74.7, 3.0], [74.8, 3.0], [74.9, 3.0], [75.0, 3.0], [75.1, 3.0], [75.2, 3.0], [75.3, 3.0], [75.4, 3.0], [75.5, 3.0], [75.6, 3.0], [75.7, 3.0], [75.8, 3.0], [75.9, 3.0], [76.0, 3.0], [76.1, 3.0], [76.2, 3.0], [76.3, 3.0], [76.4, 3.0], [76.5, 3.0], [76.6, 3.0], [76.7, 3.0], [76.8, 3.0], [76.9, 3.0], [77.0, 3.0], [77.1, 3.0], [77.2, 3.0], [77.3, 3.0], [77.4, 3.0], [77.5, 3.0], [77.6, 3.0], [77.7, 3.0], [77.8, 3.0], [77.9, 3.0], [78.0, 3.0], [78.1, 3.0], [78.2, 3.0], [78.3, 3.0], [78.4, 3.0], [78.5, 3.0], [78.6, 3.0], [78.7, 3.0], [78.8, 3.0], [78.9, 3.0], [79.0, 3.0], [79.1, 3.0], [79.2, 3.0], [79.3, 3.0], [79.4, 3.0], [79.5, 3.0], [79.6, 3.0], [79.7, 3.0], [79.8, 3.0], [79.9, 3.0], [80.0, 3.0], [80.1, 3.0], [80.2, 3.0], [80.3, 3.0], [80.4, 3.0], [80.5, 3.0], [80.6, 3.0], [80.7, 3.0], [80.8, 3.0], [80.9, 3.0], [81.0, 3.0], [81.1, 3.0], [81.2, 3.0], [81.3, 3.0], [81.4, 3.0], [81.5, 3.0], [81.6, 3.0], [81.7, 3.0], [81.8, 3.0], [81.9, 3.0], [82.0, 3.0], [82.1, 3.0], [82.2, 3.0], [82.3, 3.0], [82.4, 3.0], [82.5, 3.0], [82.6, 3.0], [82.7, 3.0], [82.8, 3.0], [82.9, 3.0], [83.0, 3.0], [83.1, 3.0], [83.2, 3.0], [83.3, 3.0], [83.4, 3.0], [83.5, 3.0], [83.6, 3.0], [83.7, 3.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 4.0], [84.7, 4.0], [84.8, 4.0], [84.9, 4.0], [85.0, 4.0], [85.1, 4.0], [85.2, 4.0], [85.3, 4.0], [85.4, 4.0], [85.5, 4.0], [85.6, 4.0], [85.7, 4.0], [85.8, 4.0], [85.9, 4.0], [86.0, 4.0], [86.1, 4.0], [86.2, 4.0], [86.3, 4.0], [86.4, 4.0], [86.5, 4.0], [86.6, 4.0], [86.7, 4.0], [86.8, 4.0], [86.9, 4.0], [87.0, 4.0], [87.1, 4.0], [87.2, 4.0], [87.3, 4.0], [87.4, 4.0], [87.5, 4.0], [87.6, 4.0], [87.7, 4.0], [87.8, 4.0], [87.9, 4.0], [88.0, 4.0], [88.1, 4.0], [88.2, 4.0], [88.3, 4.0], [88.4, 4.0], [88.5, 4.0], [88.6, 4.0], [88.7, 4.0], [88.8, 4.0], [88.9, 4.0], [89.0, 4.0], [89.1, 4.0], [89.2, 4.0], [89.3, 4.0], [89.4, 4.0], [89.5, 4.0], [89.6, 4.0], [89.7, 4.0], [89.8, 4.0], [89.9, 4.0], [90.0, 4.0], [90.1, 4.0], [90.2, 4.0], [90.3, 4.0], [90.4, 4.0], [90.5, 4.0], [90.6, 4.0], [90.7, 4.0], [90.8, 4.0], [90.9, 4.0], [91.0, 4.0], [91.1, 4.0], [91.2, 4.0], [91.3, 4.0], [91.4, 4.0], [91.5, 4.0], [91.6, 4.0], [91.7, 4.0], [91.8, 4.0], [91.9, 4.0], [92.0, 4.0], [92.1, 4.0], [92.2, 4.0], [92.3, 4.0], [92.4, 4.0], [92.5, 4.0], [92.6, 4.0], [92.7, 4.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 5.0], [95.3, 5.0], [95.4, 5.0], [95.5, 5.0], [95.6, 5.0], [95.7, 5.0], [95.8, 5.0], [95.9, 5.0], [96.0, 5.0], [96.1, 5.0], [96.2, 5.0], [96.3, 5.0], [96.4, 5.0], [96.5, 5.0], [96.6, 5.0], [96.7, 5.0], [96.8, 5.0], [96.9, 5.0], [97.0, 5.0], [97.1, 5.0], [97.2, 5.0], [97.3, 5.0], [97.4, 5.0], [97.5, 5.0], [97.6, 5.0], [97.7, 5.0], [97.8, 5.0], [97.9, 5.0], [98.0, 5.0], [98.1, 5.0], [98.2, 5.0], [98.3, 5.0], [98.4, 5.0], [98.5, 5.0], [98.6, 6.0], [98.7, 6.0], [98.8, 6.0], [98.9, 6.0], [99.0, 6.0], [99.1, 6.0], [99.2, 6.0], [99.3, 6.0], [99.4, 7.0], [99.5, 7.0], [99.6, 8.0], [99.7, 8.0], [99.8, 9.0], [99.9, 9.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 33.0], [0.1, 33.0], [0.2, 33.0], [0.3, 34.0], [0.4, 34.0], [0.5, 34.0], [0.6, 34.0], [0.7, 35.0], [0.8, 35.0], [0.9, 35.0], [1.0, 35.0], [1.1, 35.0], [1.2, 35.0], [1.3, 35.0], [1.4, 35.0], [1.5, 35.0], [1.6, 35.0], [1.7, 35.0], [1.8, 35.0], [1.9, 35.0], [2.0, 35.0], [2.1, 35.0], [2.2, 35.0], [2.3, 35.0], [2.4, 35.0], [2.5, 35.0], [2.6, 35.0], [2.7, 35.0], [2.8, 35.0], [2.9, 35.0], [3.0, 35.0], [3.1, 35.0], [3.2, 35.0], [3.3, 35.0], [3.4, 35.0], [3.5, 35.0], [3.6, 35.0], [3.7, 35.0], [3.8, 35.0], [3.9, 35.0], [4.0, 35.0], [4.1, 36.0], [4.2, 36.0], [4.3, 36.0], [4.4, 36.0], [4.5, 36.0], [4.6, 36.0], [4.7, 36.0], [4.8, 36.0], [4.9, 36.0], [5.0, 36.0], [5.1, 36.0], [5.2, 36.0], [5.3, 36.0], [5.4, 36.0], [5.5, 36.0], [5.6, 36.0], [5.7, 36.0], [5.8, 36.0], [5.9, 36.0], [6.0, 36.0], [6.1, 36.0], [6.2, 36.0], [6.3, 36.0], [6.4, 36.0], [6.5, 36.0], [6.6, 36.0], [6.7, 36.0], [6.8, 36.0], [6.9, 36.0], [7.0, 36.0], [7.1, 36.0], [7.2, 36.0], [7.3, 36.0], [7.4, 36.0], [7.5, 36.0], [7.6, 36.0], [7.7, 36.0], [7.8, 36.0], [7.9, 36.0], [8.0, 36.0], [8.1, 36.0], [8.2, 36.0], [8.3, 36.0], [8.4, 36.0], [8.5, 36.0], [8.6, 36.0], [8.7, 36.0], [8.8, 36.0], [8.9, 36.0], [9.0, 36.0], [9.1, 36.0], [9.2, 36.0], [9.3, 36.0], [9.4, 36.0], [9.5, 36.0], [9.6, 36.0], [9.7, 36.0], [9.8, 36.0], [9.9, 36.0], [10.0, 36.0], [10.1, 36.0], [10.2, 36.0], [10.3, 36.0], [10.4, 36.0], [10.5, 36.0], [10.6, 36.0], [10.7, 36.0], [10.8, 36.0], [10.9, 37.0], [11.0, 37.0], [11.1, 37.0], [11.2, 37.0], [11.3, 37.0], [11.4, 37.0], [11.5, 37.0], [11.6, 37.0], [11.7, 37.0], [11.8, 37.0], [11.9, 37.0], [12.0, 37.0], [12.1, 37.0], [12.2, 37.0], [12.3, 37.0], [12.4, 37.0], [12.5, 37.0], [12.6, 37.0], [12.7, 37.0], [12.8, 37.0], [12.9, 37.0], [13.0, 37.0], [13.1, 37.0], [13.2, 37.0], [13.3, 37.0], [13.4, 37.0], [13.5, 37.0], [13.6, 37.0], [13.7, 37.0], [13.8, 37.0], [13.9, 37.0], [14.0, 37.0], [14.1, 37.0], [14.2, 37.0], [14.3, 37.0], [14.4, 37.0], [14.5, 37.0], [14.6, 37.0], [14.7, 37.0], [14.8, 37.0], [14.9, 37.0], [15.0, 37.0], [15.1, 37.0], [15.2, 37.0], [15.3, 37.0], [15.4, 37.0], [15.5, 37.0], [15.6, 37.0], [15.7, 37.0], [15.8, 37.0], [15.9, 37.0], [16.0, 37.0], [16.1, 37.0], [16.2, 37.0], [16.3, 37.0], [16.4, 37.0], [16.5, 37.0], [16.6, 37.0], [16.7, 37.0], [16.8, 37.0], [16.9, 37.0], [17.0, 37.0], [17.1, 37.0], [17.2, 37.0], [17.3, 37.0], [17.4, 37.0], [17.5, 37.0], [17.6, 37.0], [17.7, 37.0], [17.8, 37.0], [17.9, 37.0], [18.0, 37.0], [18.1, 37.0], [18.2, 37.0], [18.3, 37.0], [18.4, 37.0], [18.5, 37.0], [18.6, 37.0], [18.7, 37.0], [18.8, 37.0], [18.9, 37.0], [19.0, 37.0], [19.1, 37.0], [19.2, 37.0], [19.3, 37.0], [19.4, 37.0], [19.5, 37.0], [19.6, 37.0], [19.7, 37.0], [19.8, 37.0], [19.9, 37.0], [20.0, 37.0], [20.1, 37.0], [20.2, 37.0], [20.3, 37.0], [20.4, 37.0], [20.5, 37.0], [20.6, 37.0], [20.7, 37.0], [20.8, 37.0], [20.9, 37.0], [21.0, 37.0], [21.1, 38.0], [21.2, 38.0], [21.3, 38.0], [21.4, 38.0], [21.5, 38.0], [21.6, 38.0], [21.7, 38.0], [21.8, 38.0], [21.9, 38.0], [22.0, 38.0], [22.1, 38.0], [22.2, 38.0], [22.3, 38.0], [22.4, 38.0], [22.5, 38.0], [22.6, 38.0], [22.7, 38.0], [22.8, 38.0], [22.9, 38.0], [23.0, 38.0], [23.1, 38.0], [23.2, 38.0], [23.3, 38.0], [23.4, 38.0], [23.5, 38.0], [23.6, 38.0], [23.7, 38.0], [23.8, 38.0], [23.9, 38.0], [24.0, 38.0], [24.1, 38.0], [24.2, 38.0], [24.3, 38.0], [24.4, 38.0], [24.5, 38.0], [24.6, 38.0], [24.7, 38.0], [24.8, 38.0], [24.9, 38.0], [25.0, 38.0], [25.1, 38.0], [25.2, 38.0], [25.3, 38.0], [25.4, 38.0], [25.5, 38.0], [25.6, 38.0], [25.7, 38.0], [25.8, 38.0], [25.9, 38.0], [26.0, 38.0], [26.1, 38.0], [26.2, 38.0], [26.3, 38.0], [26.4, 38.0], [26.5, 38.0], [26.6, 38.0], [26.7, 38.0], [26.8, 38.0], [26.9, 38.0], [27.0, 38.0], [27.1, 38.0], [27.2, 38.0], [27.3, 38.0], [27.4, 38.0], [27.5, 38.0], [27.6, 38.0], [27.7, 38.0], [27.8, 38.0], [27.9, 38.0], [28.0, 38.0], [28.1, 38.0], [28.2, 38.0], [28.3, 38.0], [28.4, 38.0], [28.5, 38.0], [28.6, 38.0], [28.7, 38.0], [28.8, 38.0], [28.9, 38.0], [29.0, 38.0], [29.1, 38.0], [29.2, 38.0], [29.3, 38.0], [29.4, 38.0], [29.5, 38.0], [29.6, 38.0], [29.7, 38.0], [29.8, 38.0], [29.9, 38.0], [30.0, 38.0], [30.1, 38.0], [30.2, 38.0], [30.3, 38.0], [30.4, 38.0], [30.5, 38.0], [30.6, 38.0], [30.7, 38.0], [30.8, 38.0], [30.9, 38.0], [31.0, 38.0], [31.1, 38.0], [31.2, 38.0], [31.3, 38.0], [31.4, 38.0], [31.5, 38.0], [31.6, 38.0], [31.7, 38.0], [31.8, 38.0], [31.9, 38.0], [32.0, 38.0], [32.1, 38.0], [32.2, 38.0], [32.3, 38.0], [32.4, 38.0], [32.5, 38.0], [32.6, 38.0], [32.7, 38.0], [32.8, 38.0], [32.9, 38.0], [33.0, 38.0], [33.1, 38.0], [33.2, 38.0], [33.3, 38.0], [33.4, 38.0], [33.5, 38.0], [33.6, 38.0], [33.7, 38.0], [33.8, 38.0], [33.9, 38.0], [34.0, 38.0], [34.1, 38.0], [34.2, 38.0], [34.3, 38.0], [34.4, 38.0], [34.5, 38.0], [34.6, 38.0], [34.7, 38.0], [34.8, 38.0], [34.9, 38.0], [35.0, 38.0], [35.1, 38.0], [35.2, 38.0], [35.3, 38.0], [35.4, 38.0], [35.5, 38.0], [35.6, 38.0], [35.7, 38.0], [35.8, 38.0], [35.9, 38.0], [36.0, 38.0], [36.1, 38.0], [36.2, 38.0], [36.3, 38.0], [36.4, 38.0], [36.5, 38.0], [36.6, 38.0], [36.7, 38.0], [36.8, 38.0], [36.9, 38.0], [37.0, 38.0], [37.1, 38.0], [37.2, 38.0], [37.3, 38.0], [37.4, 38.0], [37.5, 38.0], [37.6, 38.0], [37.7, 38.0], [37.8, 39.0], [37.9, 39.0], [38.0, 39.0], [38.1, 39.0], [38.2, 39.0], [38.3, 39.0], [38.4, 39.0], [38.5, 39.0], [38.6, 39.0], [38.7, 39.0], [38.8, 39.0], [38.9, 39.0], [39.0, 39.0], [39.1, 39.0], [39.2, 39.0], [39.3, 39.0], [39.4, 39.0], [39.5, 39.0], [39.6, 39.0], [39.7, 39.0], [39.8, 39.0], [39.9, 39.0], [40.0, 39.0], [40.1, 39.0], [40.2, 39.0], [40.3, 39.0], [40.4, 39.0], [40.5, 39.0], [40.6, 39.0], [40.7, 39.0], [40.8, 39.0], [40.9, 39.0], [41.0, 39.0], [41.1, 39.0], [41.2, 39.0], [41.3, 39.0], [41.4, 39.0], [41.5, 39.0], [41.6, 39.0], [41.7, 39.0], [41.8, 39.0], [41.9, 39.0], [42.0, 39.0], [42.1, 39.0], [42.2, 39.0], [42.3, 39.0], [42.4, 39.0], [42.5, 39.0], [42.6, 39.0], [42.7, 39.0], [42.8, 39.0], [42.9, 39.0], [43.0, 39.0], [43.1, 39.0], [43.2, 39.0], [43.3, 39.0], [43.4, 39.0], [43.5, 39.0], [43.6, 39.0], [43.7, 39.0], [43.8, 39.0], [43.9, 39.0], [44.0, 39.0], [44.1, 39.0], [44.2, 39.0], [44.3, 39.0], [44.4, 39.0], [44.5, 39.0], [44.6, 39.0], [44.7, 39.0], [44.8, 39.0], [44.9, 39.0], [45.0, 39.0], [45.1, 39.0], [45.2, 39.0], [45.3, 39.0], [45.4, 39.0], [45.5, 39.0], [45.6, 39.0], [45.7, 39.0], [45.8, 39.0], [45.9, 39.0], [46.0, 39.0], [46.1, 39.0], [46.2, 39.0], [46.3, 39.0], [46.4, 39.0], [46.5, 39.0], [46.6, 39.0], [46.7, 39.0], [46.8, 39.0], [46.9, 39.0], [47.0, 39.0], [47.1, 39.0], [47.2, 39.0], [47.3, 39.0], [47.4, 39.0], [47.5, 39.0], [47.6, 39.0], [47.7, 39.0], [47.8, 39.0], [47.9, 39.0], [48.0, 39.0], [48.1, 39.0], [48.2, 39.0], [48.3, 39.0], [48.4, 39.0], [48.5, 39.0], [48.6, 39.0], [48.7, 39.0], [48.8, 39.0], [48.9, 39.0], [49.0, 39.0], [49.1, 39.0], [49.2, 39.0], [49.3, 39.0], [49.4, 39.0], [49.5, 39.0], [49.6, 39.0], [49.7, 39.0], [49.8, 39.0], [49.9, 39.0], [50.0, 39.0], [50.1, 39.0], [50.2, 39.0], [50.3, 39.0], [50.4, 39.0], [50.5, 39.0], [50.6, 39.0], [50.7, 39.0], [50.8, 39.0], [50.9, 39.0], [51.0, 39.0], [51.1, 39.0], [51.2, 39.0], [51.3, 39.0], [51.4, 39.0], [51.5, 39.0], [51.6, 39.0], [51.7, 39.0], [51.8, 39.0], [51.9, 39.0], [52.0, 39.0], [52.1, 39.0], [52.2, 39.0], [52.3, 39.0], [52.4, 39.0], [52.5, 39.0], [52.6, 39.0], [52.7, 39.0], [52.8, 39.0], [52.9, 39.0], [53.0, 39.0], [53.1, 39.0], [53.2, 39.0], [53.3, 39.0], [53.4, 39.0], [53.5, 39.0], [53.6, 39.0], [53.7, 39.0], [53.8, 39.0], [53.9, 39.0], [54.0, 39.0], [54.1, 39.0], [54.2, 39.0], [54.3, 39.0], [54.4, 39.0], [54.5, 39.0], [54.6, 39.0], [54.7, 39.0], [54.8, 39.0], [54.9, 39.0], [55.0, 39.0], [55.1, 39.0], [55.2, 39.0], [55.3, 39.0], [55.4, 39.0], [55.5, 39.0], [55.6, 39.0], [55.7, 39.0], [55.8, 39.0], [55.9, 39.0], [56.0, 39.0], [56.1, 39.0], [56.2, 39.0], [56.3, 39.0], [56.4, 39.0], [56.5, 39.0], [56.6, 40.0], [56.7, 40.0], [56.8, 40.0], [56.9, 40.0], [57.0, 40.0], [57.1, 40.0], [57.2, 40.0], [57.3, 40.0], [57.4, 40.0], [57.5, 40.0], [57.6, 40.0], [57.7, 40.0], [57.8, 40.0], [57.9, 40.0], [58.0, 40.0], [58.1, 40.0], [58.2, 40.0], [58.3, 40.0], [58.4, 40.0], [58.5, 40.0], [58.6, 40.0], [58.7, 40.0], [58.8, 40.0], [58.9, 40.0], [59.0, 40.0], [59.1, 40.0], [59.2, 40.0], [59.3, 40.0], [59.4, 40.0], [59.5, 40.0], [59.6, 40.0], [59.7, 40.0], [59.8, 40.0], [59.9, 40.0], [60.0, 40.0], [60.1, 40.0], [60.2, 40.0], [60.3, 40.0], [60.4, 40.0], [60.5, 40.0], [60.6, 40.0], [60.7, 40.0], [60.8, 40.0], [60.9, 40.0], [61.0, 40.0], [61.1, 40.0], [61.2, 40.0], [61.3, 40.0], [61.4, 40.0], [61.5, 40.0], [61.6, 40.0], [61.7, 40.0], [61.8, 40.0], [61.9, 40.0], [62.0, 40.0], [62.1, 40.0], [62.2, 40.0], [62.3, 40.0], [62.4, 40.0], [62.5, 40.0], [62.6, 40.0], [62.7, 40.0], [62.8, 40.0], [62.9, 40.0], [63.0, 40.0], [63.1, 40.0], [63.2, 40.0], [63.3, 40.0], [63.4, 40.0], [63.5, 40.0], [63.6, 40.0], [63.7, 40.0], [63.8, 40.0], [63.9, 40.0], [64.0, 40.0], [64.1, 40.0], [64.2, 40.0], [64.3, 40.0], [64.4, 40.0], [64.5, 40.0], [64.6, 40.0], [64.7, 40.0], [64.8, 40.0], [64.9, 40.0], [65.0, 40.0], [65.1, 40.0], [65.2, 40.0], [65.3, 40.0], [65.4, 40.0], [65.5, 40.0], [65.6, 40.0], [65.7, 40.0], [65.8, 40.0], [65.9, 40.0], [66.0, 40.0], [66.1, 40.0], [66.2, 40.0], [66.3, 40.0], [66.4, 40.0], [66.5, 40.0], [66.6, 40.0], [66.7, 40.0], [66.8, 40.0], [66.9, 40.0], [67.0, 40.0], [67.1, 40.0], [67.2, 40.0], [67.3, 40.0], [67.4, 40.0], [67.5, 40.0], [67.6, 40.0], [67.7, 40.0], [67.8, 40.0], [67.9, 40.0], [68.0, 40.0], [68.1, 40.0], [68.2, 40.0], [68.3, 40.0], [68.4, 40.0], [68.5, 40.0], [68.6, 40.0], [68.7, 40.0], [68.8, 40.0], [68.9, 40.0], [69.0, 40.0], [69.1, 40.0], [69.2, 40.0], [69.3, 40.0], [69.4, 40.0], [69.5, 40.0], [69.6, 40.0], [69.7, 40.0], [69.8, 40.0], [69.9, 40.0], [70.0, 40.0], [70.1, 40.0], [70.2, 40.0], [70.3, 40.0], [70.4, 40.0], [70.5, 40.0], [70.6, 40.0], [70.7, 40.0], [70.8, 40.0], [70.9, 40.0], [71.0, 40.0], [71.1, 40.0], [71.2, 40.0], [71.3, 40.0], [71.4, 40.0], [71.5, 40.0], [71.6, 40.0], [71.7, 40.0], [71.8, 40.0], [71.9, 40.0], [72.0, 40.0], [72.1, 40.0], [72.2, 40.0], [72.3, 40.0], [72.4, 41.0], [72.5, 41.0], [72.6, 41.0], [72.7, 41.0], [72.8, 41.0], [72.9, 41.0], [73.0, 41.0], [73.1, 41.0], [73.2, 41.0], [73.3, 41.0], [73.4, 41.0], [73.5, 41.0], [73.6, 41.0], [73.7, 41.0], [73.8, 41.0], [73.9, 41.0], [74.0, 41.0], [74.1, 41.0], [74.2, 41.0], [74.3, 41.0], [74.4, 41.0], [74.5, 41.0], [74.6, 41.0], [74.7, 41.0], [74.8, 41.0], [74.9, 41.0], [75.0, 41.0], [75.1, 41.0], [75.2, 41.0], [75.3, 41.0], [75.4, 41.0], [75.5, 41.0], [75.6, 41.0], [75.7, 41.0], [75.8, 41.0], [75.9, 41.0], [76.0, 41.0], [76.1, 41.0], [76.2, 41.0], [76.3, 41.0], [76.4, 41.0], [76.5, 41.0], [76.6, 41.0], [76.7, 41.0], [76.8, 41.0], [76.9, 41.0], [77.0, 41.0], [77.1, 41.0], [77.2, 41.0], [77.3, 41.0], [77.4, 41.0], [77.5, 41.0], [77.6, 41.0], [77.7, 41.0], [77.8, 41.0], [77.9, 41.0], [78.0, 41.0], [78.1, 41.0], [78.2, 41.0], [78.3, 41.0], [78.4, 41.0], [78.5, 41.0], [78.6, 41.0], [78.7, 41.0], [78.8, 41.0], [78.9, 41.0], [79.0, 41.0], [79.1, 41.0], [79.2, 41.0], [79.3, 41.0], [79.4, 41.0], [79.5, 41.0], [79.6, 41.0], [79.7, 41.0], [79.8, 41.0], [79.9, 41.0], [80.0, 41.0], [80.1, 41.0], [80.2, 41.0], [80.3, 41.0], [80.4, 41.0], [80.5, 41.0], [80.6, 41.0], [80.7, 41.0], [80.8, 41.0], [80.9, 41.0], [81.0, 41.0], [81.1, 41.0], [81.2, 41.0], [81.3, 41.0], [81.4, 41.0], [81.5, 41.0], [81.6, 41.0], [81.7, 41.0], [81.8, 41.0], [81.9, 41.0], [82.0, 41.0], [82.1, 41.0], [82.2, 41.0], [82.3, 41.0], [82.4, 41.0], [82.5, 41.0], [82.6, 41.0], [82.7, 41.0], [82.8, 41.0], [82.9, 42.0], [83.0, 42.0], [83.1, 42.0], [83.2, 42.0], [83.3, 42.0], [83.4, 42.0], [83.5, 42.0], [83.6, 42.0], [83.7, 42.0], [83.8, 42.0], [83.9, 42.0], [84.0, 42.0], [84.1, 42.0], [84.2, 42.0], [84.3, 42.0], [84.4, 42.0], [84.5, 42.0], [84.6, 42.0], [84.7, 42.0], [84.8, 42.0], [84.9, 42.0], [85.0, 42.0], [85.1, 42.0], [85.2, 42.0], [85.3, 42.0], [85.4, 42.0], [85.5, 42.0], [85.6, 42.0], [85.7, 42.0], [85.8, 42.0], [85.9, 42.0], [86.0, 42.0], [86.1, 42.0], [86.2, 42.0], [86.3, 42.0], [86.4, 42.0], [86.5, 42.0], [86.6, 42.0], [86.7, 42.0], [86.8, 42.0], [86.9, 42.0], [87.0, 42.0], [87.1, 42.0], [87.2, 42.0], [87.3, 42.0], [87.4, 42.0], [87.5, 42.0], [87.6, 42.0], [87.7, 42.0], [87.8, 42.0], [87.9, 42.0], [88.0, 42.0], [88.1, 42.0], [88.2, 42.0], [88.3, 42.0], [88.4, 42.0], [88.5, 42.0], [88.6, 43.0], [88.7, 43.0], [88.8, 43.0], [88.9, 43.0], [89.0, 43.0], [89.1, 43.0], [89.2, 43.0], [89.3, 43.0], [89.4, 43.0], [89.5, 43.0], [89.6, 43.0], [89.7, 43.0], [89.8, 43.0], [89.9, 43.0], [90.0, 43.0], [90.1, 43.0], [90.2, 43.0], [90.3, 43.0], [90.4, 43.0], [90.5, 43.0], [90.6, 43.0], [90.7, 43.0], [90.8, 43.0], [90.9, 44.0], [91.0, 44.0], [91.1, 44.0], [91.2, 44.0], [91.3, 44.0], [91.4, 44.0], [91.5, 44.0], [91.6, 44.0], [91.7, 44.0], [91.8, 44.0], [91.9, 44.0], [92.0, 44.0], [92.1, 44.0], [92.2, 44.0], [92.3, 44.0], [92.4, 44.0], [92.5, 44.0], [92.6, 44.0], [92.7, 45.0], [92.8, 45.0], [92.9, 45.0], [93.0, 45.0], [93.1, 45.0], [93.2, 45.0], [93.3, 45.0], [93.4, 45.0], [93.5, 45.0], [93.6, 45.0], [93.7, 46.0], [93.8, 46.0], [93.9, 46.0], [94.0, 46.0], [94.1, 47.0], [94.2, 47.0], [94.3, 48.0], [94.4, 48.0], [94.5, 49.0], [94.6, 49.0], [94.7, 49.0], [94.8, 49.0], [94.9, 51.0], [95.0, 51.0], [95.1, 51.0], [95.2, 51.0], [95.3, 51.0], [95.4, 52.0], [95.5, 52.0], [95.6, 52.0], [95.7, 52.0], [95.8, 52.0], [95.9, 53.0], [96.0, 53.0], [96.1, 53.0], [96.2, 54.0], [96.3, 54.0], [96.4, 55.0], [96.5, 55.0], [96.6, 55.0], [96.7, 58.0], [96.8, 59.0], [96.9, 59.0], [97.0, 59.0], [97.1, 60.0], [97.2, 60.0], [97.3, 60.0], [97.4, 60.0], [97.5, 61.0], [97.6, 62.0], [97.7, 62.0], [97.8, 62.0], [97.9, 63.0], [98.0, 63.0], [98.1, 63.0], [98.2, 63.0], [98.3, 64.0], [98.4, 64.0], [98.5, 65.0], [98.6, 66.0], [98.7, 66.0], [98.8, 67.0], [98.9, 67.0], [99.0, 68.0], [99.1, 68.0], [99.2, 70.0], [99.3, 71.0], [99.4, 71.0], [99.5, 71.0], [99.6, 72.0], [99.7, 73.0], [99.8, 73.0], [99.9, 77.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 2.0], [10.5, 2.0], [10.6, 2.0], [10.7, 2.0], [10.8, 2.0], [10.9, 2.0], [11.0, 2.0], [11.1, 2.0], [11.2, 2.0], [11.3, 2.0], [11.4, 2.0], [11.5, 2.0], [11.6, 2.0], [11.7, 2.0], [11.8, 2.0], [11.9, 2.0], [12.0, 2.0], [12.1, 2.0], [12.2, 2.0], [12.3, 2.0], [12.4, 2.0], [12.5, 2.0], [12.6, 2.0], [12.7, 2.0], [12.8, 2.0], [12.9, 2.0], [13.0, 2.0], [13.1, 2.0], [13.2, 2.0], [13.3, 2.0], [13.4, 2.0], [13.5, 2.0], [13.6, 2.0], [13.7, 2.0], [13.8, 2.0], [13.9, 2.0], [14.0, 2.0], [14.1, 2.0], [14.2, 2.0], [14.3, 2.0], [14.4, 2.0], [14.5, 2.0], [14.6, 2.0], [14.7, 2.0], [14.8, 2.0], [14.9, 2.0], [15.0, 2.0], [15.1, 2.0], [15.2, 2.0], [15.3, 2.0], [15.4, 2.0], [15.5, 2.0], [15.6, 2.0], [15.7, 2.0], [15.8, 2.0], [15.9, 2.0], [16.0, 2.0], [16.1, 2.0], [16.2, 2.0], [16.3, 2.0], [16.4, 2.0], [16.5, 2.0], [16.6, 2.0], [16.7, 2.0], [16.8, 2.0], [16.9, 2.0], [17.0, 2.0], [17.1, 2.0], [17.2, 2.0], [17.3, 2.0], [17.4, 2.0], [17.5, 2.0], [17.6, 2.0], [17.7, 2.0], [17.8, 2.0], [17.9, 2.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 3.0], [63.1, 3.0], [63.2, 3.0], [63.3, 3.0], [63.4, 3.0], [63.5, 3.0], [63.6, 3.0], [63.7, 3.0], [63.8, 3.0], [63.9, 3.0], [64.0, 3.0], [64.1, 3.0], [64.2, 3.0], [64.3, 3.0], [64.4, 3.0], [64.5, 3.0], [64.6, 3.0], [64.7, 3.0], [64.8, 3.0], [64.9, 3.0], [65.0, 3.0], [65.1, 3.0], [65.2, 3.0], [65.3, 3.0], [65.4, 3.0], [65.5, 3.0], [65.6, 3.0], [65.7, 3.0], [65.8, 3.0], [65.9, 3.0], [66.0, 4.0], [66.1, 4.0], [66.2, 4.0], [66.3, 4.0], [66.4, 4.0], [66.5, 4.0], [66.6, 4.0], [66.7, 4.0], [66.8, 4.0], [66.9, 4.0], [67.0, 4.0], [67.1, 4.0], [67.2, 4.0], [67.3, 4.0], [67.4, 4.0], [67.5, 4.0], [67.6, 4.0], [67.7, 4.0], [67.8, 4.0], [67.9, 4.0], [68.0, 4.0], [68.1, 4.0], [68.2, 4.0], [68.3, 4.0], [68.4, 4.0], [68.5, 4.0], [68.6, 4.0], [68.7, 4.0], [68.8, 4.0], [68.9, 4.0], [69.0, 4.0], [69.1, 4.0], [69.2, 4.0], [69.3, 4.0], [69.4, 4.0], [69.5, 4.0], [69.6, 4.0], [69.7, 4.0], [69.8, 4.0], [69.9, 4.0], [70.0, 4.0], [70.1, 4.0], [70.2, 4.0], [70.3, 4.0], [70.4, 4.0], [70.5, 4.0], [70.6, 4.0], [70.7, 4.0], [70.8, 4.0], [70.9, 4.0], [71.0, 4.0], [71.1, 4.0], [71.2, 4.0], [71.3, 4.0], [71.4, 4.0], [71.5, 4.0], [71.6, 4.0], [71.7, 4.0], [71.8, 4.0], [71.9, 4.0], [72.0, 4.0], [72.1, 4.0], [72.2, 4.0], [72.3, 4.0], [72.4, 4.0], [72.5, 4.0], [72.6, 4.0], [72.7, 4.0], [72.8, 4.0], [72.9, 4.0], [73.0, 4.0], [73.1, 4.0], [73.2, 4.0], [73.3, 4.0], [73.4, 4.0], [73.5, 4.0], [73.6, 4.0], [73.7, 4.0], [73.8, 4.0], [73.9, 4.0], [74.0, 4.0], [74.1, 4.0], [74.2, 4.0], [74.3, 4.0], [74.4, 4.0], [74.5, 4.0], [74.6, 4.0], [74.7, 4.0], [74.8, 4.0], [74.9, 4.0], [75.0, 4.0], [75.1, 4.0], [75.2, 4.0], [75.3, 4.0], [75.4, 4.0], [75.5, 4.0], [75.6, 4.0], [75.7, 4.0], [75.8, 4.0], [75.9, 4.0], [76.0, 4.0], [76.1, 4.0], [76.2, 4.0], [76.3, 4.0], [76.4, 4.0], [76.5, 4.0], [76.6, 4.0], [76.7, 4.0], [76.8, 4.0], [76.9, 4.0], [77.0, 4.0], [77.1, 4.0], [77.2, 4.0], [77.3, 4.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 5.0], [82.1, 5.0], [82.2, 5.0], [82.3, 5.0], [82.4, 5.0], [82.5, 5.0], [82.6, 5.0], [82.7, 5.0], [82.8, 5.0], [82.9, 5.0], [83.0, 5.0], [83.1, 5.0], [83.2, 5.0], [83.3, 5.0], [83.4, 5.0], [83.5, 5.0], [83.6, 5.0], [83.7, 5.0], [83.8, 5.0], [83.9, 5.0], [84.0, 5.0], [84.1, 5.0], [84.2, 5.0], [84.3, 5.0], [84.4, 5.0], [84.5, 5.0], [84.6, 5.0], [84.7, 5.0], [84.8, 5.0], [84.9, 5.0], [85.0, 5.0], [85.1, 5.0], [85.2, 5.0], [85.3, 5.0], [85.4, 5.0], [85.5, 5.0], [85.6, 5.0], [85.7, 5.0], [85.8, 5.0], [85.9, 5.0], [86.0, 5.0], [86.1, 5.0], [86.2, 5.0], [86.3, 5.0], [86.4, 5.0], [86.5, 5.0], [86.6, 5.0], [86.7, 5.0], [86.8, 5.0], [86.9, 5.0], [87.0, 5.0], [87.1, 5.0], [87.2, 5.0], [87.3, 5.0], [87.4, 5.0], [87.5, 5.0], [87.6, 5.0], [87.7, 5.0], [87.8, 5.0], [87.9, 5.0], [88.0, 5.0], [88.1, 5.0], [88.2, 5.0], [88.3, 5.0], [88.4, 5.0], [88.5, 5.0], [88.6, 5.0], [88.7, 5.0], [88.8, 5.0], [88.9, 5.0], [89.0, 5.0], [89.1, 5.0], [89.2, 5.0], [89.3, 5.0], [89.4, 5.0], [89.5, 5.0], [89.6, 5.0], [89.7, 5.0], [89.8, 5.0], [89.9, 5.0], [90.0, 5.0], [90.1, 5.0], [90.2, 5.0], [90.3, 5.0], [90.4, 5.0], [90.5, 5.0], [90.6, 5.0], [90.7, 5.0], [90.8, 5.0], [90.9, 5.0], [91.0, 5.0], [91.1, 5.0], [91.2, 5.0], [91.3, 5.0], [91.4, 5.0], [91.5, 5.0], [91.6, 5.0], [91.7, 5.0], [91.8, 5.0], [91.9, 5.0], [92.0, 5.0], [92.1, 5.0], [92.2, 5.0], [92.3, 5.0], [92.4, 5.0], [92.5, 5.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 6.0], [93.5, 6.0], [93.6, 6.0], [93.7, 6.0], [93.8, 6.0], [93.9, 6.0], [94.0, 6.0], [94.1, 6.0], [94.2, 6.0], [94.3, 6.0], [94.4, 6.0], [94.5, 6.0], [94.6, 6.0], [94.7, 6.0], [94.8, 6.0], [94.9, 6.0], [95.0, 6.0], [95.1, 6.0], [95.2, 6.0], [95.3, 6.0], [95.4, 6.0], [95.5, 6.0], [95.6, 6.0], [95.7, 6.0], [95.8, 6.0], [95.9, 6.0], [96.0, 6.0], [96.1, 6.0], [96.2, 6.0], [96.3, 6.0], [96.4, 6.0], [96.5, 6.0], [96.6, 6.0], [96.7, 6.0], [96.8, 6.0], [96.9, 6.0], [97.0, 7.0], [97.1, 7.0], [97.2, 7.0], [97.3, 7.0], [97.4, 7.0], [97.5, 7.0], [97.6, 7.0], [97.7, 7.0], [97.8, 7.0], [97.9, 7.0], [98.0, 7.0], [98.1, 7.0], [98.2, 7.0], [98.3, 7.0], [98.4, 7.0], [98.5, 7.0], [98.6, 8.0], [98.7, 8.0], [98.8, 8.0], [98.9, 8.0], [99.0, 8.0], [99.1, 8.0], [99.2, 8.0], [99.3, 8.0], [99.4, 8.0], [99.5, 8.0], [99.6, 9.0], [99.7, 9.0], [99.8, 9.0], [99.9, 9.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 3.0], [0.3, 3.0], [0.4, 3.0], [0.5, 3.0], [0.6, 3.0], [0.7, 3.0], [0.8, 3.0], [0.9, 3.0], [1.0, 3.0], [1.1, 3.0], [1.2, 3.0], [1.3, 3.0], [1.4, 3.0], [1.5, 3.0], [1.6, 3.0], [1.7, 3.0], [1.8, 3.0], [1.9, 3.0], [2.0, 3.0], [2.1, 3.0], [2.2, 3.0], [2.3, 3.0], [2.4, 3.0], [2.5, 3.0], [2.6, 3.0], [2.7, 3.0], [2.8, 3.0], [2.9, 3.0], [3.0, 3.0], [3.1, 3.0], [3.2, 3.0], [3.3, 3.0], [3.4, 3.0], [3.5, 3.0], [3.6, 3.0], [3.7, 3.0], [3.8, 3.0], [3.9, 3.0], [4.0, 3.0], [4.1, 3.0], [4.2, 3.0], [4.3, 3.0], [4.4, 3.0], [4.5, 3.0], [4.6, 3.0], [4.7, 4.0], [4.8, 4.0], [4.9, 4.0], [5.0, 4.0], [5.1, 4.0], [5.2, 4.0], [5.3, 4.0], [5.4, 4.0], [5.5, 4.0], [5.6, 4.0], [5.7, 4.0], [5.8, 4.0], [5.9, 4.0], [6.0, 4.0], [6.1, 4.0], [6.2, 4.0], [6.3, 4.0], [6.4, 4.0], [6.5, 4.0], [6.6, 4.0], [6.7, 4.0], [6.8, 4.0], [6.9, 4.0], [7.0, 4.0], [7.1, 4.0], [7.2, 4.0], [7.3, 4.0], [7.4, 4.0], [7.5, 4.0], [7.6, 4.0], [7.7, 4.0], [7.8, 4.0], [7.9, 4.0], [8.0, 4.0], [8.1, 4.0], [8.2, 4.0], [8.3, 4.0], [8.4, 4.0], [8.5, 4.0], [8.6, 4.0], [8.7, 4.0], [8.8, 4.0], [8.9, 4.0], [9.0, 4.0], [9.1, 4.0], [9.2, 4.0], [9.3, 4.0], [9.4, 4.0], [9.5, 4.0], [9.6, 4.0], [9.7, 4.0], [9.8, 4.0], [9.9, 4.0], [10.0, 4.0], [10.1, 4.0], [10.2, 4.0], [10.3, 4.0], [10.4, 4.0], [10.5, 4.0], [10.6, 4.0], [10.7, 4.0], [10.8, 4.0], [10.9, 4.0], [11.0, 4.0], [11.1, 4.0], [11.2, 4.0], [11.3, 4.0], [11.4, 4.0], [11.5, 4.0], [11.6, 4.0], [11.7, 4.0], [11.8, 4.0], [11.9, 4.0], [12.0, 4.0], [12.1, 4.0], [12.2, 4.0], [12.3, 4.0], [12.4, 4.0], [12.5, 4.0], [12.6, 4.0], [12.7, 4.0], [12.8, 4.0], [12.9, 4.0], [13.0, 4.0], [13.1, 4.0], [13.2, 4.0], [13.3, 4.0], [13.4, 4.0], [13.5, 4.0], [13.6, 4.0], [13.7, 4.0], [13.8, 4.0], [13.9, 4.0], [14.0, 4.0], [14.1, 4.0], [14.2, 4.0], [14.3, 4.0], [14.4, 4.0], [14.5, 4.0], [14.6, 4.0], [14.7, 4.0], [14.8, 4.0], [14.9, 4.0], [15.0, 4.0], [15.1, 4.0], [15.2, 4.0], [15.3, 4.0], [15.4, 4.0], [15.5, 4.0], [15.6, 4.0], [15.7, 4.0], [15.8, 4.0], [15.9, 4.0], [16.0, 4.0], [16.1, 4.0], [16.2, 4.0], [16.3, 4.0], [16.4, 4.0], [16.5, 4.0], [16.6, 4.0], [16.7, 4.0], [16.8, 4.0], [16.9, 4.0], [17.0, 4.0], [17.1, 4.0], [17.2, 4.0], [17.3, 4.0], [17.4, 4.0], [17.5, 4.0], [17.6, 4.0], [17.7, 4.0], [17.8, 4.0], [17.9, 4.0], [18.0, 4.0], [18.1, 4.0], [18.2, 4.0], [18.3, 4.0], [18.4, 4.0], [18.5, 4.0], [18.6, 4.0], [18.7, 4.0], [18.8, 4.0], [18.9, 4.0], [19.0, 4.0], [19.1, 4.0], [19.2, 4.0], [19.3, 4.0], [19.4, 4.0], [19.5, 4.0], [19.6, 4.0], [19.7, 4.0], [19.8, 4.0], [19.9, 4.0], [20.0, 4.0], [20.1, 4.0], [20.2, 4.0], [20.3, 4.0], [20.4, 4.0], [20.5, 4.0], [20.6, 4.0], [20.7, 4.0], [20.8, 4.0], [20.9, 4.0], [21.0, 4.0], [21.1, 4.0], [21.2, 4.0], [21.3, 4.0], [21.4, 4.0], [21.5, 4.0], [21.6, 4.0], [21.7, 4.0], [21.8, 4.0], [21.9, 4.0], [22.0, 4.0], [22.1, 4.0], [22.2, 4.0], [22.3, 4.0], [22.4, 4.0], [22.5, 4.0], [22.6, 4.0], [22.7, 4.0], [22.8, 4.0], [22.9, 4.0], [23.0, 4.0], [23.1, 4.0], [23.2, 4.0], [23.3, 4.0], [23.4, 4.0], [23.5, 4.0], [23.6, 4.0], [23.7, 4.0], [23.8, 4.0], [23.9, 4.0], [24.0, 4.0], [24.1, 4.0], [24.2, 4.0], [24.3, 4.0], [24.4, 4.0], [24.5, 4.0], [24.6, 4.0], [24.7, 4.0], [24.8, 4.0], [24.9, 4.0], [25.0, 4.0], [25.1, 4.0], [25.2, 4.0], [25.3, 4.0], [25.4, 4.0], [25.5, 4.0], [25.6, 4.0], [25.7, 4.0], [25.8, 4.0], [25.9, 4.0], [26.0, 4.0], [26.1, 4.0], [26.2, 4.0], [26.3, 4.0], [26.4, 4.0], [26.5, 4.0], [26.6, 4.0], [26.7, 4.0], [26.8, 4.0], [26.9, 4.0], [27.0, 4.0], [27.1, 4.0], [27.2, 4.0], [27.3, 4.0], [27.4, 4.0], [27.5, 4.0], [27.6, 4.0], [27.7, 4.0], [27.8, 4.0], [27.9, 4.0], [28.0, 4.0], [28.1, 4.0], [28.2, 4.0], [28.3, 4.0], [28.4, 4.0], [28.5, 4.0], [28.6, 4.0], [28.7, 4.0], [28.8, 4.0], [28.9, 4.0], [29.0, 4.0], [29.1, 4.0], [29.2, 4.0], [29.3, 4.0], [29.4, 4.0], [29.5, 4.0], [29.6, 4.0], [29.7, 4.0], [29.8, 4.0], [29.9, 4.0], [30.0, 4.0], [30.1, 4.0], [30.2, 4.0], [30.3, 4.0], [30.4, 4.0], [30.5, 4.0], [30.6, 4.0], [30.7, 4.0], [30.8, 4.0], [30.9, 4.0], [31.0, 4.0], [31.1, 4.0], [31.2, 4.0], [31.3, 4.0], [31.4, 4.0], [31.5, 4.0], [31.6, 4.0], [31.7, 4.0], [31.8, 4.0], [31.9, 4.0], [32.0, 4.0], [32.1, 4.0], [32.2, 4.0], [32.3, 4.0], [32.4, 4.0], [32.5, 4.0], [32.6, 4.0], [32.7, 4.0], [32.8, 4.0], [32.9, 4.0], [33.0, 5.0], [33.1, 5.0], [33.2, 5.0], [33.3, 5.0], [33.4, 5.0], [33.5, 5.0], [33.6, 5.0], [33.7, 5.0], [33.8, 5.0], [33.9, 5.0], [34.0, 5.0], [34.1, 5.0], [34.2, 5.0], [34.3, 5.0], [34.4, 5.0], [34.5, 5.0], [34.6, 5.0], [34.7, 5.0], [34.8, 5.0], [34.9, 5.0], [35.0, 5.0], [35.1, 5.0], [35.2, 5.0], [35.3, 5.0], [35.4, 5.0], [35.5, 5.0], [35.6, 5.0], [35.7, 5.0], [35.8, 5.0], [35.9, 5.0], [36.0, 5.0], [36.1, 5.0], [36.2, 5.0], [36.3, 5.0], [36.4, 5.0], [36.5, 5.0], [36.6, 5.0], [36.7, 5.0], [36.8, 5.0], [36.9, 5.0], [37.0, 5.0], [37.1, 5.0], [37.2, 5.0], [37.3, 5.0], [37.4, 5.0], [37.5, 5.0], [37.6, 5.0], [37.7, 5.0], [37.8, 5.0], [37.9, 5.0], [38.0, 5.0], [38.1, 5.0], [38.2, 5.0], [38.3, 5.0], [38.4, 5.0], [38.5, 5.0], [38.6, 5.0], [38.7, 5.0], [38.8, 5.0], [38.9, 5.0], [39.0, 5.0], [39.1, 5.0], [39.2, 5.0], [39.3, 5.0], [39.4, 5.0], [39.5, 5.0], [39.6, 5.0], [39.7, 5.0], [39.8, 5.0], [39.9, 5.0], [40.0, 5.0], [40.1, 5.0], [40.2, 5.0], [40.3, 5.0], [40.4, 5.0], [40.5, 5.0], [40.6, 5.0], [40.7, 5.0], [40.8, 5.0], [40.9, 5.0], [41.0, 5.0], [41.1, 5.0], [41.2, 5.0], [41.3, 5.0], [41.4, 5.0], [41.5, 5.0], [41.6, 5.0], [41.7, 5.0], [41.8, 5.0], [41.9, 5.0], [42.0, 5.0], [42.1, 5.0], [42.2, 5.0], [42.3, 5.0], [42.4, 5.0], [42.5, 5.0], [42.6, 5.0], [42.7, 5.0], [42.8, 5.0], [42.9, 5.0], [43.0, 5.0], [43.1, 5.0], [43.2, 5.0], [43.3, 5.0], [43.4, 5.0], [43.5, 5.0], [43.6, 5.0], [43.7, 5.0], [43.8, 5.0], [43.9, 5.0], [44.0, 5.0], [44.1, 5.0], [44.2, 5.0], [44.3, 5.0], [44.4, 5.0], [44.5, 5.0], [44.6, 5.0], [44.7, 5.0], [44.8, 5.0], [44.9, 5.0], [45.0, 5.0], [45.1, 5.0], [45.2, 5.0], [45.3, 5.0], [45.4, 5.0], [45.5, 5.0], [45.6, 5.0], [45.7, 5.0], [45.8, 5.0], [45.9, 5.0], [46.0, 5.0], [46.1, 5.0], [46.2, 5.0], [46.3, 5.0], [46.4, 5.0], [46.5, 5.0], [46.6, 5.0], [46.7, 5.0], [46.8, 5.0], [46.9, 5.0], [47.0, 5.0], [47.1, 5.0], [47.2, 5.0], [47.3, 5.0], [47.4, 5.0], [47.5, 5.0], [47.6, 5.0], [47.7, 5.0], [47.8, 5.0], [47.9, 5.0], [48.0, 5.0], [48.1, 5.0], [48.2, 5.0], [48.3, 5.0], [48.4, 5.0], [48.5, 5.0], [48.6, 5.0], [48.7, 5.0], [48.8, 5.0], [48.9, 5.0], [49.0, 5.0], [49.1, 5.0], [49.2, 5.0], [49.3, 5.0], [49.4, 5.0], [49.5, 5.0], [49.6, 5.0], [49.7, 5.0], [49.8, 5.0], [49.9, 5.0], [50.0, 5.0], [50.1, 5.0], [50.2, 5.0], [50.3, 5.0], [50.4, 5.0], [50.5, 5.0], [50.6, 5.0], [50.7, 5.0], [50.8, 5.0], [50.9, 5.0], [51.0, 5.0], [51.1, 5.0], [51.2, 5.0], [51.3, 5.0], [51.4, 5.0], [51.5, 5.0], [51.6, 5.0], [51.7, 5.0], [51.8, 5.0], [51.9, 5.0], [52.0, 5.0], [52.1, 5.0], [52.2, 5.0], [52.3, 5.0], [52.4, 5.0], [52.5, 5.0], [52.6, 5.0], [52.7, 5.0], [52.8, 5.0], [52.9, 5.0], [53.0, 5.0], [53.1, 5.0], [53.2, 5.0], [53.3, 5.0], [53.4, 5.0], [53.5, 5.0], [53.6, 5.0], [53.7, 5.0], [53.8, 5.0], [53.9, 5.0], [54.0, 5.0], [54.1, 5.0], [54.2, 5.0], [54.3, 5.0], [54.4, 5.0], [54.5, 5.0], [54.6, 5.0], [54.7, 5.0], [54.8, 5.0], [54.9, 5.0], [55.0, 5.0], [55.1, 5.0], [55.2, 5.0], [55.3, 5.0], [55.4, 5.0], [55.5, 5.0], [55.6, 5.0], [55.7, 5.0], [55.8, 5.0], [55.9, 5.0], [56.0, 5.0], [56.1, 5.0], [56.2, 5.0], [56.3, 5.0], [56.4, 5.0], [56.5, 5.0], [56.6, 5.0], [56.7, 5.0], [56.8, 5.0], [56.9, 5.0], [57.0, 5.0], [57.1, 5.0], [57.2, 5.0], [57.3, 5.0], [57.4, 5.0], [57.5, 5.0], [57.6, 5.0], [57.7, 5.0], [57.8, 5.0], [57.9, 5.0], [58.0, 5.0], [58.1, 5.0], [58.2, 5.0], [58.3, 5.0], [58.4, 5.0], [58.5, 5.0], [58.6, 5.0], [58.7, 5.0], [58.8, 5.0], [58.9, 5.0], [59.0, 5.0], [59.1, 5.0], [59.2, 5.0], [59.3, 5.0], [59.4, 5.0], [59.5, 5.0], [59.6, 5.0], [59.7, 5.0], [59.8, 5.0], [59.9, 5.0], [60.0, 5.0], [60.1, 5.0], [60.2, 5.0], [60.3, 5.0], [60.4, 5.0], [60.5, 5.0], [60.6, 5.0], [60.7, 5.0], [60.8, 5.0], [60.9, 5.0], [61.0, 5.0], [61.1, 5.0], [61.2, 5.0], [61.3, 5.0], [61.4, 5.0], [61.5, 5.0], [61.6, 5.0], [61.7, 5.0], [61.8, 5.0], [61.9, 5.0], [62.0, 5.0], [62.1, 5.0], [62.2, 5.0], [62.3, 5.0], [62.4, 5.0], [62.5, 5.0], [62.6, 5.0], [62.7, 5.0], [62.8, 5.0], [62.9, 5.0], [63.0, 5.0], [63.1, 5.0], [63.2, 5.0], [63.3, 5.0], [63.4, 5.0], [63.5, 5.0], [63.6, 5.0], [63.7, 5.0], [63.8, 5.0], [63.9, 5.0], [64.0, 5.0], [64.1, 5.0], [64.2, 5.0], [64.3, 5.0], [64.4, 5.0], [64.5, 5.0], [64.6, 5.0], [64.7, 5.0], [64.8, 5.0], [64.9, 5.0], [65.0, 5.0], [65.1, 5.0], [65.2, 5.0], [65.3, 5.0], [65.4, 5.0], [65.5, 5.0], [65.6, 5.0], [65.7, 5.0], [65.8, 5.0], [65.9, 5.0], [66.0, 5.0], [66.1, 5.0], [66.2, 5.0], [66.3, 5.0], [66.4, 5.0], [66.5, 5.0], [66.6, 5.0], [66.7, 5.0], [66.8, 5.0], [66.9, 5.0], [67.0, 5.0], [67.1, 5.0], [67.2, 5.0], [67.3, 5.0], [67.4, 5.0], [67.5, 5.0], [67.6, 5.0], [67.7, 5.0], [67.8, 5.0], [67.9, 5.0], [68.0, 5.0], [68.1, 5.0], [68.2, 5.0], [68.3, 5.0], [68.4, 5.0], [68.5, 5.0], [68.6, 5.0], [68.7, 5.0], [68.8, 5.0], [68.9, 5.0], [69.0, 5.0], [69.1, 5.0], [69.2, 5.0], [69.3, 5.0], [69.4, 5.0], [69.5, 5.0], [69.6, 5.0], [69.7, 5.0], [69.8, 5.0], [69.9, 5.0], [70.0, 5.0], [70.1, 5.0], [70.2, 5.0], [70.3, 5.0], [70.4, 5.0], [70.5, 5.0], [70.6, 5.0], [70.7, 5.0], [70.8, 6.0], [70.9, 6.0], [71.0, 6.0], [71.1, 6.0], [71.2, 6.0], [71.3, 6.0], [71.4, 6.0], [71.5, 6.0], [71.6, 6.0], [71.7, 6.0], [71.8, 6.0], [71.9, 6.0], [72.0, 6.0], [72.1, 6.0], [72.2, 6.0], [72.3, 6.0], [72.4, 6.0], [72.5, 6.0], [72.6, 6.0], [72.7, 6.0], [72.8, 6.0], [72.9, 6.0], [73.0, 6.0], [73.1, 6.0], [73.2, 6.0], [73.3, 6.0], [73.4, 6.0], [73.5, 6.0], [73.6, 6.0], [73.7, 6.0], [73.8, 6.0], [73.9, 6.0], [74.0, 6.0], [74.1, 6.0], [74.2, 6.0], [74.3, 6.0], [74.4, 6.0], [74.5, 6.0], [74.6, 6.0], [74.7, 6.0], [74.8, 6.0], [74.9, 6.0], [75.0, 6.0], [75.1, 6.0], [75.2, 6.0], [75.3, 6.0], [75.4, 6.0], [75.5, 6.0], [75.6, 6.0], [75.7, 6.0], [75.8, 6.0], [75.9, 6.0], [76.0, 6.0], [76.1, 6.0], [76.2, 6.0], [76.3, 6.0], [76.4, 6.0], [76.5, 6.0], [76.6, 6.0], [76.7, 6.0], [76.8, 6.0], [76.9, 6.0], [77.0, 6.0], [77.1, 6.0], [77.2, 6.0], [77.3, 6.0], [77.4, 6.0], [77.5, 6.0], [77.6, 6.0], [77.7, 6.0], [77.8, 6.0], [77.9, 6.0], [78.0, 6.0], [78.1, 6.0], [78.2, 6.0], [78.3, 6.0], [78.4, 6.0], [78.5, 6.0], [78.6, 6.0], [78.7, 6.0], [78.8, 6.0], [78.9, 6.0], [79.0, 6.0], [79.1, 6.0], [79.2, 6.0], [79.3, 6.0], [79.4, 6.0], [79.5, 6.0], [79.6, 6.0], [79.7, 6.0], [79.8, 6.0], [79.9, 6.0], [80.0, 6.0], [80.1, 6.0], [80.2, 6.0], [80.3, 6.0], [80.4, 6.0], [80.5, 6.0], [80.6, 6.0], [80.7, 6.0], [80.8, 6.0], [80.9, 6.0], [81.0, 6.0], [81.1, 6.0], [81.2, 6.0], [81.3, 6.0], [81.4, 6.0], [81.5, 6.0], [81.6, 6.0], [81.7, 6.0], [81.8, 6.0], [81.9, 6.0], [82.0, 6.0], [82.1, 6.0], [82.2, 6.0], [82.3, 6.0], [82.4, 6.0], [82.5, 6.0], [82.6, 6.0], [82.7, 6.0], [82.8, 6.0], [82.9, 6.0], [83.0, 6.0], [83.1, 6.0], [83.2, 6.0], [83.3, 6.0], [83.4, 6.0], [83.5, 6.0], [83.6, 6.0], [83.7, 6.0], [83.8, 6.0], [83.9, 6.0], [84.0, 6.0], [84.1, 6.0], [84.2, 6.0], [84.3, 6.0], [84.4, 6.0], [84.5, 6.0], [84.6, 6.0], [84.7, 6.0], [84.8, 6.0], [84.9, 6.0], [85.0, 6.0], [85.1, 6.0], [85.2, 6.0], [85.3, 6.0], [85.4, 6.0], [85.5, 6.0], [85.6, 6.0], [85.7, 6.0], [85.8, 6.0], [85.9, 6.0], [86.0, 6.0], [86.1, 6.0], [86.2, 6.0], [86.3, 6.0], [86.4, 6.0], [86.5, 6.0], [86.6, 6.0], [86.7, 6.0], [86.8, 6.0], [86.9, 6.0], [87.0, 6.0], [87.1, 6.0], [87.2, 7.0], [87.3, 7.0], [87.4, 7.0], [87.5, 7.0], [87.6, 7.0], [87.7, 7.0], [87.8, 7.0], [87.9, 7.0], [88.0, 7.0], [88.1, 7.0], [88.2, 7.0], [88.3, 7.0], [88.4, 7.0], [88.5, 7.0], [88.6, 7.0], [88.7, 7.0], [88.8, 7.0], [88.9, 7.0], [89.0, 7.0], [89.1, 7.0], [89.2, 7.0], [89.3, 7.0], [89.4, 7.0], [89.5, 7.0], [89.6, 7.0], [89.7, 7.0], [89.8, 7.0], [89.9, 7.0], [90.0, 7.0], [90.1, 7.0], [90.2, 7.0], [90.3, 7.0], [90.4, 7.0], [90.5, 7.0], [90.6, 7.0], [90.7, 7.0], [90.8, 7.0], [90.9, 7.0], [91.0, 7.0], [91.1, 7.0], [91.2, 7.0], [91.3, 7.0], [91.4, 7.0], [91.5, 7.0], [91.6, 7.0], [91.7, 7.0], [91.8, 7.0], [91.9, 7.0], [92.0, 7.0], [92.1, 7.0], [92.2, 7.0], [92.3, 7.0], [92.4, 7.0], [92.5, 7.0], [92.6, 7.0], [92.7, 7.0], [92.8, 7.0], [92.9, 7.0], [93.0, 7.0], [93.1, 7.0], [93.2, 7.0], [93.3, 7.0], [93.4, 8.0], [93.5, 8.0], [93.6, 8.0], [93.7, 8.0], [93.8, 8.0], [93.9, 8.0], [94.0, 8.0], [94.1, 8.0], [94.2, 8.0], [94.3, 8.0], [94.4, 8.0], [94.5, 8.0], [94.6, 8.0], [94.7, 8.0], [94.8, 8.0], [94.9, 8.0], [95.0, 8.0], [95.1, 8.0], [95.2, 8.0], [95.3, 8.0], [95.4, 8.0], [95.5, 8.0], [95.6, 8.0], [95.7, 8.0], [95.8, 8.0], [95.9, 8.0], [96.0, 8.0], [96.1, 8.0], [96.2, 8.0], [96.3, 8.0], [96.4, 8.0], [96.5, 8.0], [96.6, 8.0], [96.7, 8.0], [96.8, 8.0], [96.9, 9.0], [97.0, 9.0], [97.1, 9.0], [97.2, 9.0], [97.3, 9.0], [97.4, 9.0], [97.5, 9.0], [97.6, 9.0], [97.7, 9.0], [97.8, 9.0], [97.9, 9.0], [98.0, 9.0], [98.1, 9.0], [98.2, 9.0], [98.3, 9.0], [98.4, 9.0], [98.5, 9.0], [98.6, 10.0], [98.7, 10.0], [98.8, 10.0], [98.9, 10.0], [99.0, 10.0], [99.1, 10.0], [99.2, 11.0], [99.3, 11.0], [99.4, 12.0], [99.5, 12.0], [99.6, 13.0], [99.7, 13.0], [99.8, 15.0], [99.9, 16.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 35.0], [0.1, 35.0], [0.2, 35.0], [0.3, 35.0], [0.4, 36.0], [0.5, 36.0], [0.6, 36.0], [0.7, 36.0], [0.8, 36.0], [0.9, 36.0], [1.0, 37.0], [1.1, 37.0], [1.2, 37.0], [1.3, 37.0], [1.4, 37.0], [1.5, 37.0], [1.6, 37.0], [1.7, 37.0], [1.8, 37.0], [1.9, 37.0], [2.0, 37.0], [2.1, 37.0], [2.2, 37.0], [2.3, 37.0], [2.4, 37.0], [2.5, 37.0], [2.6, 37.0], [2.7, 37.0], [2.8, 37.0], [2.9, 37.0], [3.0, 37.0], [3.1, 37.0], [3.2, 37.0], [3.3, 37.0], [3.4, 37.0], [3.5, 37.0], [3.6, 38.0], [3.7, 38.0], [3.8, 38.0], [3.9, 38.0], [4.0, 38.0], [4.1, 38.0], [4.2, 38.0], [4.3, 38.0], [4.4, 38.0], [4.5, 38.0], [4.6, 38.0], [4.7, 38.0], [4.8, 38.0], [4.9, 38.0], [5.0, 38.0], [5.1, 38.0], [5.2, 38.0], [5.3, 38.0], [5.4, 38.0], [5.5, 38.0], [5.6, 38.0], [5.7, 38.0], [5.8, 38.0], [5.9, 38.0], [6.0, 38.0], [6.1, 38.0], [6.2, 38.0], [6.3, 38.0], [6.4, 38.0], [6.5, 38.0], [6.6, 38.0], [6.7, 38.0], [6.8, 38.0], [6.9, 38.0], [7.0, 38.0], [7.1, 38.0], [7.2, 38.0], [7.3, 38.0], [7.4, 38.0], [7.5, 38.0], [7.6, 38.0], [7.7, 38.0], [7.8, 38.0], [7.9, 38.0], [8.0, 38.0], [8.1, 38.0], [8.2, 38.0], [8.3, 38.0], [8.4, 38.0], [8.5, 38.0], [8.6, 38.0], [8.7, 38.0], [8.8, 38.0], [8.9, 38.0], [9.0, 38.0], [9.1, 38.0], [9.2, 38.0], [9.3, 38.0], [9.4, 38.0], [9.5, 38.0], [9.6, 39.0], [9.7, 39.0], [9.8, 39.0], [9.9, 39.0], [10.0, 39.0], [10.1, 39.0], [10.2, 39.0], [10.3, 39.0], [10.4, 39.0], [10.5, 39.0], [10.6, 39.0], [10.7, 39.0], [10.8, 39.0], [10.9, 39.0], [11.0, 39.0], [11.1, 39.0], [11.2, 39.0], [11.3, 39.0], [11.4, 39.0], [11.5, 39.0], [11.6, 39.0], [11.7, 39.0], [11.8, 39.0], [11.9, 39.0], [12.0, 39.0], [12.1, 39.0], [12.2, 39.0], [12.3, 39.0], [12.4, 39.0], [12.5, 39.0], [12.6, 39.0], [12.7, 39.0], [12.8, 39.0], [12.9, 39.0], [13.0, 39.0], [13.1, 39.0], [13.2, 39.0], [13.3, 39.0], [13.4, 39.0], [13.5, 39.0], [13.6, 39.0], [13.7, 39.0], [13.8, 39.0], [13.9, 39.0], [14.0, 39.0], [14.1, 39.0], [14.2, 39.0], [14.3, 39.0], [14.4, 39.0], [14.5, 39.0], [14.6, 39.0], [14.7, 39.0], [14.8, 39.0], [14.9, 39.0], [15.0, 39.0], [15.1, 39.0], [15.2, 39.0], [15.3, 39.0], [15.4, 39.0], [15.5, 39.0], [15.6, 39.0], [15.7, 39.0], [15.8, 39.0], [15.9, 39.0], [16.0, 39.0], [16.1, 39.0], [16.2, 39.0], [16.3, 39.0], [16.4, 39.0], [16.5, 39.0], [16.6, 39.0], [16.7, 39.0], [16.8, 39.0], [16.9, 39.0], [17.0, 39.0], [17.1, 39.0], [17.2, 39.0], [17.3, 39.0], [17.4, 39.0], [17.5, 39.0], [17.6, 39.0], [17.7, 39.0], [17.8, 39.0], [17.9, 39.0], [18.0, 39.0], [18.1, 39.0], [18.2, 39.0], [18.3, 39.0], [18.4, 39.0], [18.5, 39.0], [18.6, 39.0], [18.7, 39.0], [18.8, 39.0], [18.9, 39.0], [19.0, 39.0], [19.1, 39.0], [19.2, 39.0], [19.3, 39.0], [19.4, 39.0], [19.5, 39.0], [19.6, 39.0], [19.7, 39.0], [19.8, 39.0], [19.9, 39.0], [20.0, 39.0], [20.1, 39.0], [20.2, 39.0], [20.3, 39.0], [20.4, 39.0], [20.5, 39.0], [20.6, 39.0], [20.7, 39.0], [20.8, 39.0], [20.9, 39.0], [21.0, 39.0], [21.1, 39.0], [21.2, 39.0], [21.3, 39.0], [21.4, 39.0], [21.5, 39.0], [21.6, 39.0], [21.7, 39.0], [21.8, 39.0], [21.9, 39.0], [22.0, 39.0], [22.1, 39.0], [22.2, 39.0], [22.3, 39.0], [22.4, 39.0], [22.5, 39.0], [22.6, 39.0], [22.7, 39.0], [22.8, 39.0], [22.9, 39.0], [23.0, 39.0], [23.1, 39.0], [23.2, 39.0], [23.3, 39.0], [23.4, 39.0], [23.5, 39.0], [23.6, 39.0], [23.7, 39.0], [23.8, 39.0], [23.9, 39.0], [24.0, 39.0], [24.1, 39.0], [24.2, 39.0], [24.3, 39.0], [24.4, 39.0], [24.5, 39.0], [24.6, 39.0], [24.7, 39.0], [24.8, 39.0], [24.9, 39.0], [25.0, 39.0], [25.1, 39.0], [25.2, 39.0], [25.3, 39.0], [25.4, 39.0], [25.5, 39.0], [25.6, 39.0], [25.7, 39.0], [25.8, 39.0], [25.9, 39.0], [26.0, 39.0], [26.1, 39.0], [26.2, 39.0], [26.3, 39.0], [26.4, 39.0], [26.5, 39.0], [26.6, 39.0], [26.7, 39.0], [26.8, 39.0], [26.9, 39.0], [27.0, 39.0], [27.1, 39.0], [27.2, 39.0], [27.3, 39.0], [27.4, 39.0], [27.5, 39.0], [27.6, 39.0], [27.7, 39.0], [27.8, 39.0], [27.9, 39.0], [28.0, 39.0], [28.1, 39.0], [28.2, 39.0], [28.3, 39.0], [28.4, 39.0], [28.5, 39.0], [28.6, 39.0], [28.7, 39.0], [28.8, 39.0], [28.9, 39.0], [29.0, 39.0], [29.1, 39.0], [29.2, 39.0], [29.3, 39.0], [29.4, 39.0], [29.5, 39.0], [29.6, 39.0], [29.7, 39.0], [29.8, 39.0], [29.9, 39.0], [30.0, 39.0], [30.1, 39.0], [30.2, 39.0], [30.3, 39.0], [30.4, 40.0], [30.5, 40.0], [30.6, 40.0], [30.7, 40.0], [30.8, 40.0], [30.9, 40.0], [31.0, 40.0], [31.1, 40.0], [31.2, 40.0], [31.3, 40.0], [31.4, 40.0], [31.5, 40.0], [31.6, 40.0], [31.7, 40.0], [31.8, 40.0], [31.9, 40.0], [32.0, 40.0], [32.1, 40.0], [32.2, 40.0], [32.3, 40.0], [32.4, 40.0], [32.5, 40.0], [32.6, 40.0], [32.7, 40.0], [32.8, 40.0], [32.9, 40.0], [33.0, 40.0], [33.1, 40.0], [33.2, 40.0], [33.3, 40.0], [33.4, 40.0], [33.5, 40.0], [33.6, 40.0], [33.7, 40.0], [33.8, 40.0], [33.9, 40.0], [34.0, 40.0], [34.1, 40.0], [34.2, 40.0], [34.3, 40.0], [34.4, 40.0], [34.5, 40.0], [34.6, 40.0], [34.7, 40.0], [34.8, 40.0], [34.9, 40.0], [35.0, 40.0], [35.1, 40.0], [35.2, 40.0], [35.3, 40.0], [35.4, 40.0], [35.5, 40.0], [35.6, 40.0], [35.7, 40.0], [35.8, 40.0], [35.9, 40.0], [36.0, 40.0], [36.1, 40.0], [36.2, 40.0], [36.3, 40.0], [36.4, 40.0], [36.5, 40.0], [36.6, 40.0], [36.7, 40.0], [36.8, 40.0], [36.9, 40.0], [37.0, 40.0], [37.1, 40.0], [37.2, 40.0], [37.3, 40.0], [37.4, 40.0], [37.5, 40.0], [37.6, 40.0], [37.7, 40.0], [37.8, 40.0], [37.9, 40.0], [38.0, 40.0], [38.1, 40.0], [38.2, 40.0], [38.3, 40.0], [38.4, 40.0], [38.5, 40.0], [38.6, 40.0], [38.7, 40.0], [38.8, 40.0], [38.9, 40.0], [39.0, 40.0], [39.1, 40.0], [39.2, 40.0], [39.3, 40.0], [39.4, 40.0], [39.5, 40.0], [39.6, 40.0], [39.7, 40.0], [39.8, 40.0], [39.9, 40.0], [40.0, 40.0], [40.1, 40.0], [40.2, 40.0], [40.3, 40.0], [40.4, 40.0], [40.5, 40.0], [40.6, 40.0], [40.7, 40.0], [40.8, 40.0], [40.9, 40.0], [41.0, 40.0], [41.1, 40.0], [41.2, 40.0], [41.3, 40.0], [41.4, 40.0], [41.5, 40.0], [41.6, 40.0], [41.7, 40.0], [41.8, 40.0], [41.9, 40.0], [42.0, 40.0], [42.1, 40.0], [42.2, 40.0], [42.3, 40.0], [42.4, 40.0], [42.5, 40.0], [42.6, 40.0], [42.7, 40.0], [42.8, 40.0], [42.9, 40.0], [43.0, 40.0], [43.1, 40.0], [43.2, 40.0], [43.3, 40.0], [43.4, 40.0], [43.5, 40.0], [43.6, 40.0], [43.7, 40.0], [43.8, 40.0], [43.9, 40.0], [44.0, 40.0], [44.1, 40.0], [44.2, 40.0], [44.3, 40.0], [44.4, 40.0], [44.5, 40.0], [44.6, 40.0], [44.7, 40.0], [44.8, 40.0], [44.9, 40.0], [45.0, 40.0], [45.1, 40.0], [45.2, 40.0], [45.3, 40.0], [45.4, 40.0], [45.5, 40.0], [45.6, 40.0], [45.7, 40.0], [45.8, 40.0], [45.9, 40.0], [46.0, 40.0], [46.1, 40.0], [46.2, 40.0], [46.3, 40.0], [46.4, 40.0], [46.5, 40.0], [46.6, 40.0], [46.7, 40.0], [46.8, 40.0], [46.9, 40.0], [47.0, 40.0], [47.1, 40.0], [47.2, 40.0], [47.3, 40.0], [47.4, 40.0], [47.5, 40.0], [47.6, 40.0], [47.7, 40.0], [47.8, 40.0], [47.9, 40.0], [48.0, 40.0], [48.1, 40.0], [48.2, 40.0], [48.3, 40.0], [48.4, 40.0], [48.5, 40.0], [48.6, 40.0], [48.7, 40.0], [48.8, 40.0], [48.9, 40.0], [49.0, 40.0], [49.1, 40.0], [49.2, 40.0], [49.3, 40.0], [49.4, 40.0], [49.5, 40.0], [49.6, 40.0], [49.7, 40.0], [49.8, 40.0], [49.9, 40.0], [50.0, 40.0], [50.1, 40.0], [50.2, 40.0], [50.3, 40.0], [50.4, 40.0], [50.5, 40.0], [50.6, 40.0], [50.7, 40.0], [50.8, 40.0], [50.9, 40.0], [51.0, 40.0], [51.1, 40.0], [51.2, 40.0], [51.3, 40.0], [51.4, 40.0], [51.5, 40.0], [51.6, 40.0], [51.7, 40.0], [51.8, 40.0], [51.9, 40.0], [52.0, 40.0], [52.1, 40.0], [52.2, 40.0], [52.3, 40.0], [52.4, 40.0], [52.5, 40.0], [52.6, 40.0], [52.7, 40.0], [52.8, 40.0], [52.9, 40.0], [53.0, 40.0], [53.1, 40.0], [53.2, 40.0], [53.3, 40.0], [53.4, 40.0], [53.5, 40.0], [53.6, 40.0], [53.7, 40.0], [53.8, 40.0], [53.9, 40.0], [54.0, 40.0], [54.1, 40.0], [54.2, 40.0], [54.3, 40.0], [54.4, 40.0], [54.5, 40.0], [54.6, 40.0], [54.7, 40.0], [54.8, 40.0], [54.9, 40.0], [55.0, 40.0], [55.1, 40.0], [55.2, 40.0], [55.3, 40.0], [55.4, 40.0], [55.5, 40.0], [55.6, 40.0], [55.7, 40.0], [55.8, 40.0], [55.9, 40.0], [56.0, 40.0], [56.1, 40.0], [56.2, 40.0], [56.3, 40.0], [56.4, 40.0], [56.5, 40.0], [56.6, 41.0], [56.7, 41.0], [56.8, 41.0], [56.9, 41.0], [57.0, 41.0], [57.1, 41.0], [57.2, 41.0], [57.3, 41.0], [57.4, 41.0], [57.5, 41.0], [57.6, 41.0], [57.7, 41.0], [57.8, 41.0], [57.9, 41.0], [58.0, 41.0], [58.1, 41.0], [58.2, 41.0], [58.3, 41.0], [58.4, 41.0], [58.5, 41.0], [58.6, 41.0], [58.7, 41.0], [58.8, 41.0], [58.9, 41.0], [59.0, 41.0], [59.1, 41.0], [59.2, 41.0], [59.3, 41.0], [59.4, 41.0], [59.5, 41.0], [59.6, 41.0], [59.7, 41.0], [59.8, 41.0], [59.9, 41.0], [60.0, 41.0], [60.1, 41.0], [60.2, 41.0], [60.3, 41.0], [60.4, 41.0], [60.5, 41.0], [60.6, 41.0], [60.7, 41.0], [60.8, 41.0], [60.9, 41.0], [61.0, 41.0], [61.1, 41.0], [61.2, 41.0], [61.3, 41.0], [61.4, 41.0], [61.5, 41.0], [61.6, 41.0], [61.7, 41.0], [61.8, 41.0], [61.9, 41.0], [62.0, 41.0], [62.1, 41.0], [62.2, 41.0], [62.3, 41.0], [62.4, 41.0], [62.5, 41.0], [62.6, 41.0], [62.7, 41.0], [62.8, 41.0], [62.9, 41.0], [63.0, 41.0], [63.1, 41.0], [63.2, 41.0], [63.3, 41.0], [63.4, 41.0], [63.5, 41.0], [63.6, 41.0], [63.7, 41.0], [63.8, 41.0], [63.9, 41.0], [64.0, 41.0], [64.1, 41.0], [64.2, 41.0], [64.3, 41.0], [64.4, 41.0], [64.5, 41.0], [64.6, 41.0], [64.7, 41.0], [64.8, 41.0], [64.9, 41.0], [65.0, 41.0], [65.1, 41.0], [65.2, 41.0], [65.3, 41.0], [65.4, 41.0], [65.5, 41.0], [65.6, 41.0], [65.7, 41.0], [65.8, 41.0], [65.9, 41.0], [66.0, 41.0], [66.1, 41.0], [66.2, 41.0], [66.3, 41.0], [66.4, 41.0], [66.5, 41.0], [66.6, 41.0], [66.7, 41.0], [66.8, 41.0], [66.9, 41.0], [67.0, 41.0], [67.1, 41.0], [67.2, 41.0], [67.3, 41.0], [67.4, 41.0], [67.5, 41.0], [67.6, 41.0], [67.7, 41.0], [67.8, 41.0], [67.9, 41.0], [68.0, 41.0], [68.1, 41.0], [68.2, 41.0], [68.3, 41.0], [68.4, 41.0], [68.5, 41.0], [68.6, 41.0], [68.7, 41.0], [68.8, 41.0], [68.9, 41.0], [69.0, 41.0], [69.1, 41.0], [69.2, 41.0], [69.3, 41.0], [69.4, 41.0], [69.5, 41.0], [69.6, 41.0], [69.7, 41.0], [69.8, 41.0], [69.9, 41.0], [70.0, 41.0], [70.1, 41.0], [70.2, 41.0], [70.3, 41.0], [70.4, 41.0], [70.5, 41.0], [70.6, 41.0], [70.7, 41.0], [70.8, 41.0], [70.9, 41.0], [71.0, 41.0], [71.1, 41.0], [71.2, 41.0], [71.3, 41.0], [71.4, 41.0], [71.5, 41.0], [71.6, 41.0], [71.7, 41.0], [71.8, 41.0], [71.9, 41.0], [72.0, 41.0], [72.1, 41.0], [72.2, 41.0], [72.3, 41.0], [72.4, 41.0], [72.5, 41.0], [72.6, 41.0], [72.7, 41.0], [72.8, 41.0], [72.9, 41.0], [73.0, 41.0], [73.1, 41.0], [73.2, 42.0], [73.3, 42.0], [73.4, 42.0], [73.5, 42.0], [73.6, 42.0], [73.7, 42.0], [73.8, 42.0], [73.9, 42.0], [74.0, 42.0], [74.1, 42.0], [74.2, 42.0], [74.3, 42.0], [74.4, 42.0], [74.5, 42.0], [74.6, 42.0], [74.7, 42.0], [74.8, 42.0], [74.9, 42.0], [75.0, 42.0], [75.1, 42.0], [75.2, 42.0], [75.3, 42.0], [75.4, 42.0], [75.5, 42.0], [75.6, 42.0], [75.7, 42.0], [75.8, 42.0], [75.9, 42.0], [76.0, 42.0], [76.1, 42.0], [76.2, 42.0], [76.3, 42.0], [76.4, 42.0], [76.5, 42.0], [76.6, 42.0], [76.7, 42.0], [76.8, 42.0], [76.9, 42.0], [77.0, 42.0], [77.1, 42.0], [77.2, 42.0], [77.3, 42.0], [77.4, 42.0], [77.5, 42.0], [77.6, 42.0], [77.7, 42.0], [77.8, 42.0], [77.9, 42.0], [78.0, 42.0], [78.1, 42.0], [78.2, 42.0], [78.3, 42.0], [78.4, 42.0], [78.5, 42.0], [78.6, 42.0], [78.7, 42.0], [78.8, 42.0], [78.9, 42.0], [79.0, 42.0], [79.1, 42.0], [79.2, 42.0], [79.3, 42.0], [79.4, 42.0], [79.5, 42.0], [79.6, 42.0], [79.7, 42.0], [79.8, 42.0], [79.9, 42.0], [80.0, 42.0], [80.1, 42.0], [80.2, 42.0], [80.3, 42.0], [80.4, 42.0], [80.5, 42.0], [80.6, 42.0], [80.7, 42.0], [80.8, 42.0], [80.9, 42.0], [81.0, 42.0], [81.1, 42.0], [81.2, 42.0], [81.3, 42.0], [81.4, 42.0], [81.5, 42.0], [81.6, 42.0], [81.7, 42.0], [81.8, 42.0], [81.9, 42.0], [82.0, 43.0], [82.1, 43.0], [82.2, 43.0], [82.3, 43.0], [82.4, 43.0], [82.5, 43.0], [82.6, 43.0], [82.7, 43.0], [82.8, 43.0], [82.9, 43.0], [83.0, 43.0], [83.1, 43.0], [83.2, 43.0], [83.3, 43.0], [83.4, 43.0], [83.5, 43.0], [83.6, 43.0], [83.7, 43.0], [83.8, 43.0], [83.9, 43.0], [84.0, 43.0], [84.1, 43.0], [84.2, 43.0], [84.3, 43.0], [84.4, 43.0], [84.5, 43.0], [84.6, 43.0], [84.7, 43.0], [84.8, 43.0], [84.9, 43.0], [85.0, 43.0], [85.1, 43.0], [85.2, 43.0], [85.3, 43.0], [85.4, 43.0], [85.5, 43.0], [85.6, 43.0], [85.7, 43.0], [85.8, 43.0], [85.9, 43.0], [86.0, 43.0], [86.1, 43.0], [86.2, 43.0], [86.3, 43.0], [86.4, 43.0], [86.5, 43.0], [86.6, 43.0], [86.7, 43.0], [86.8, 43.0], [86.9, 43.0], [87.0, 43.0], [87.1, 43.0], [87.2, 43.0], [87.3, 43.0], [87.4, 43.0], [87.5, 43.0], [87.6, 43.0], [87.7, 43.0], [87.8, 43.0], [87.9, 43.0], [88.0, 43.0], [88.1, 43.0], [88.2, 44.0], [88.3, 44.0], [88.4, 44.0], [88.5, 44.0], [88.6, 44.0], [88.7, 44.0], [88.8, 44.0], [88.9, 44.0], [89.0, 44.0], [89.1, 44.0], [89.2, 44.0], [89.3, 44.0], [89.4, 44.0], [89.5, 44.0], [89.6, 44.0], [89.7, 44.0], [89.8, 44.0], [89.9, 44.0], [90.0, 44.0], [90.1, 44.0], [90.2, 44.0], [90.3, 44.0], [90.4, 44.0], [90.5, 44.0], [90.6, 45.0], [90.7, 45.0], [90.8, 45.0], [90.9, 45.0], [91.0, 45.0], [91.1, 45.0], [91.2, 45.0], [91.3, 45.0], [91.4, 45.0], [91.5, 45.0], [91.6, 45.0], [91.7, 45.0], [91.8, 45.0], [91.9, 45.0], [92.0, 46.0], [92.1, 46.0], [92.2, 46.0], [92.3, 46.0], [92.4, 46.0], [92.5, 46.0], [92.6, 46.0], [92.7, 46.0], [92.8, 46.0], [92.9, 46.0], [93.0, 46.0], [93.1, 46.0], [93.2, 47.0], [93.3, 47.0], [93.4, 47.0], [93.5, 47.0], [93.6, 47.0], [93.7, 47.0], [93.8, 47.0], [93.9, 47.0], [94.0, 48.0], [94.1, 48.0], [94.2, 48.0], [94.3, 48.0], [94.4, 48.0], [94.5, 48.0], [94.6, 48.0], [94.7, 48.0], [94.8, 49.0], [94.9, 49.0], [95.0, 49.0], [95.1, 49.0], [95.2, 49.0], [95.3, 49.0], [95.4, 49.0], [95.5, 49.0], [95.6, 49.0], [95.7, 49.0], [95.8, 49.0], [95.9, 49.0], [96.0, 50.0], [96.1, 50.0], [96.2, 52.0], [96.3, 52.0], [96.4, 53.0], [96.5, 53.0], [96.6, 53.0], [96.7, 54.0], [96.8, 54.0], [96.9, 57.0], [97.0, 57.0], [97.1, 57.0], [97.2, 57.0], [97.3, 60.0], [97.4, 60.0], [97.5, 61.0], [97.6, 61.0], [97.7, 63.0], [97.8, 63.0], [97.9, 63.0], [98.0, 63.0], [98.1, 65.0], [98.2, 65.0], [98.3, 65.0], [98.4, 65.0], [98.5, 65.0], [98.6, 65.0], [98.7, 65.0], [98.8, 65.0], [98.9, 67.0], [99.0, 67.0], [99.1, 68.0], [99.2, 68.0], [99.3, 68.0], [99.4, 68.0], [99.5, 69.0], [99.6, 69.0], [99.7, 70.0], [99.8, 70.0], [99.9, 72.0], [100.0, 72.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 2.0], [0.1, 2.0], [0.2, 2.0], [0.3, 2.0], [0.4, 2.0], [0.5, 2.0], [0.6, 2.0], [0.7, 2.0], [0.8, 2.0], [0.9, 2.0], [1.0, 2.0], [1.1, 2.0], [1.2, 2.0], [1.3, 2.0], [1.4, 2.0], [1.5, 2.0], [1.6, 2.0], [1.7, 2.0], [1.8, 2.0], [1.9, 2.0], [2.0, 2.0], [2.1, 2.0], [2.2, 2.0], [2.3, 2.0], [2.4, 2.0], [2.5, 2.0], [2.6, 2.0], [2.7, 2.0], [2.8, 2.0], [2.9, 2.0], [3.0, 2.0], [3.1, 2.0], [3.2, 2.0], [3.3, 2.0], [3.4, 2.0], [3.5, 2.0], [3.6, 2.0], [3.7, 2.0], [3.8, 2.0], [3.9, 2.0], [4.0, 2.0], [4.1, 2.0], [4.2, 2.0], [4.3, 2.0], [4.4, 2.0], [4.5, 2.0], [4.6, 2.0], [4.7, 2.0], [4.8, 2.0], [4.9, 2.0], [5.0, 2.0], [5.1, 2.0], [5.2, 2.0], [5.3, 2.0], [5.4, 2.0], [5.5, 2.0], [5.6, 2.0], [5.7, 2.0], [5.8, 2.0], [5.9, 2.0], [6.0, 2.0], [6.1, 2.0], [6.2, 2.0], [6.3, 2.0], [6.4, 2.0], [6.5, 2.0], [6.6, 2.0], [6.7, 2.0], [6.8, 2.0], [6.9, 2.0], [7.0, 2.0], [7.1, 2.0], [7.2, 2.0], [7.3, 2.0], [7.4, 2.0], [7.5, 2.0], [7.6, 2.0], [7.7, 2.0], [7.8, 2.0], [7.9, 2.0], [8.0, 2.0], [8.1, 2.0], [8.2, 2.0], [8.3, 2.0], [8.4, 2.0], [8.5, 2.0], [8.6, 2.0], [8.7, 2.0], [8.8, 2.0], [8.9, 2.0], [9.0, 2.0], [9.1, 2.0], [9.2, 2.0], [9.3, 2.0], [9.4, 2.0], [9.5, 2.0], [9.6, 2.0], [9.7, 2.0], [9.8, 2.0], [9.9, 2.0], [10.0, 2.0], [10.1, 2.0], [10.2, 2.0], [10.3, 2.0], [10.4, 2.0], [10.5, 2.0], [10.6, 2.0], [10.7, 2.0], [10.8, 2.0], [10.9, 2.0], [11.0, 2.0], [11.1, 2.0], [11.2, 2.0], [11.3, 2.0], [11.4, 2.0], [11.5, 2.0], [11.6, 2.0], [11.7, 2.0], [11.8, 2.0], [11.9, 2.0], [12.0, 2.0], [12.1, 2.0], [12.2, 2.0], [12.3, 2.0], [12.4, 2.0], [12.5, 2.0], [12.6, 2.0], [12.7, 2.0], [12.8, 2.0], [12.9, 2.0], [13.0, 2.0], [13.1, 2.0], [13.2, 2.0], [13.3, 2.0], [13.4, 2.0], [13.5, 2.0], [13.6, 3.0], [13.7, 3.0], [13.8, 3.0], [13.9, 3.0], [14.0, 3.0], [14.1, 3.0], [14.2, 3.0], [14.3, 3.0], [14.4, 3.0], [14.5, 3.0], [14.6, 3.0], [14.7, 3.0], [14.8, 3.0], [14.9, 3.0], [15.0, 3.0], [15.1, 3.0], [15.2, 3.0], [15.3, 3.0], [15.4, 3.0], [15.5, 3.0], [15.6, 3.0], [15.7, 3.0], [15.8, 3.0], [15.9, 3.0], [16.0, 3.0], [16.1, 3.0], [16.2, 3.0], [16.3, 3.0], [16.4, 3.0], [16.5, 3.0], [16.6, 3.0], [16.7, 3.0], [16.8, 3.0], [16.9, 3.0], [17.0, 3.0], [17.1, 3.0], [17.2, 3.0], [17.3, 3.0], [17.4, 3.0], [17.5, 3.0], [17.6, 3.0], [17.7, 3.0], [17.8, 3.0], [17.9, 3.0], [18.0, 3.0], [18.1, 3.0], [18.2, 3.0], [18.3, 3.0], [18.4, 3.0], [18.5, 3.0], [18.6, 3.0], [18.7, 3.0], [18.8, 3.0], [18.9, 3.0], [19.0, 3.0], [19.1, 3.0], [19.2, 3.0], [19.3, 3.0], [19.4, 3.0], [19.5, 3.0], [19.6, 3.0], [19.7, 3.0], [19.8, 3.0], [19.9, 3.0], [20.0, 3.0], [20.1, 3.0], [20.2, 3.0], [20.3, 3.0], [20.4, 3.0], [20.5, 3.0], [20.6, 3.0], [20.7, 3.0], [20.8, 3.0], [20.9, 3.0], [21.0, 3.0], [21.1, 3.0], [21.2, 3.0], [21.3, 3.0], [21.4, 3.0], [21.5, 3.0], [21.6, 3.0], [21.7, 3.0], [21.8, 3.0], [21.9, 3.0], [22.0, 3.0], [22.1, 3.0], [22.2, 3.0], [22.3, 3.0], [22.4, 3.0], [22.5, 3.0], [22.6, 3.0], [22.7, 3.0], [22.8, 3.0], [22.9, 3.0], [23.0, 3.0], [23.1, 3.0], [23.2, 3.0], [23.3, 3.0], [23.4, 3.0], [23.5, 3.0], [23.6, 3.0], [23.7, 3.0], [23.8, 3.0], [23.9, 3.0], [24.0, 3.0], [24.1, 3.0], [24.2, 3.0], [24.3, 3.0], [24.4, 3.0], [24.5, 3.0], [24.6, 3.0], [24.7, 3.0], [24.8, 3.0], [24.9, 3.0], [25.0, 3.0], [25.1, 3.0], [25.2, 3.0], [25.3, 3.0], [25.4, 3.0], [25.5, 3.0], [25.6, 3.0], [25.7, 3.0], [25.8, 3.0], [25.9, 3.0], [26.0, 3.0], [26.1, 3.0], [26.2, 3.0], [26.3, 3.0], [26.4, 3.0], [26.5, 3.0], [26.6, 3.0], [26.7, 3.0], [26.8, 3.0], [26.9, 3.0], [27.0, 3.0], [27.1, 3.0], [27.2, 3.0], [27.3, 3.0], [27.4, 3.0], [27.5, 3.0], [27.6, 3.0], [27.7, 3.0], [27.8, 3.0], [27.9, 3.0], [28.0, 3.0], [28.1, 3.0], [28.2, 3.0], [28.3, 3.0], [28.4, 3.0], [28.5, 3.0], [28.6, 3.0], [28.7, 3.0], [28.8, 3.0], [28.9, 3.0], [29.0, 3.0], [29.1, 3.0], [29.2, 3.0], [29.3, 3.0], [29.4, 3.0], [29.5, 3.0], [29.6, 3.0], [29.7, 3.0], [29.8, 3.0], [29.9, 3.0], [30.0, 3.0], [30.1, 3.0], [30.2, 3.0], [30.3, 3.0], [30.4, 3.0], [30.5, 3.0], [30.6, 3.0], [30.7, 3.0], [30.8, 3.0], [30.9, 3.0], [31.0, 3.0], [31.1, 3.0], [31.2, 3.0], [31.3, 3.0], [31.4, 3.0], [31.5, 3.0], [31.6, 3.0], [31.7, 3.0], [31.8, 3.0], [31.9, 3.0], [32.0, 3.0], [32.1, 3.0], [32.2, 3.0], [32.3, 3.0], [32.4, 3.0], [32.5, 3.0], [32.6, 3.0], [32.7, 3.0], [32.8, 3.0], [32.9, 3.0], [33.0, 3.0], [33.1, 3.0], [33.2, 3.0], [33.3, 3.0], [33.4, 3.0], [33.5, 3.0], [33.6, 3.0], [33.7, 3.0], [33.8, 3.0], [33.9, 3.0], [34.0, 3.0], [34.1, 3.0], [34.2, 3.0], [34.3, 3.0], [34.4, 3.0], [34.5, 3.0], [34.6, 3.0], [34.7, 3.0], [34.8, 3.0], [34.9, 3.0], [35.0, 3.0], [35.1, 3.0], [35.2, 3.0], [35.3, 3.0], [35.4, 3.0], [35.5, 3.0], [35.6, 3.0], [35.7, 3.0], [35.8, 3.0], [35.9, 3.0], [36.0, 3.0], [36.1, 3.0], [36.2, 3.0], [36.3, 3.0], [36.4, 3.0], [36.5, 3.0], [36.6, 3.0], [36.7, 3.0], [36.8, 3.0], [36.9, 3.0], [37.0, 3.0], [37.1, 3.0], [37.2, 3.0], [37.3, 3.0], [37.4, 3.0], [37.5, 3.0], [37.6, 3.0], [37.7, 3.0], [37.8, 3.0], [37.9, 3.0], [38.0, 3.0], [38.1, 3.0], [38.2, 3.0], [38.3, 3.0], [38.4, 3.0], [38.5, 3.0], [38.6, 3.0], [38.7, 3.0], [38.8, 3.0], [38.9, 3.0], [39.0, 3.0], [39.1, 3.0], [39.2, 3.0], [39.3, 3.0], [39.4, 3.0], [39.5, 3.0], [39.6, 3.0], [39.7, 3.0], [39.8, 3.0], [39.9, 3.0], [40.0, 3.0], [40.1, 3.0], [40.2, 3.0], [40.3, 3.0], [40.4, 3.0], [40.5, 3.0], [40.6, 3.0], [40.7, 3.0], [40.8, 3.0], [40.9, 3.0], [41.0, 3.0], [41.1, 3.0], [41.2, 3.0], [41.3, 3.0], [41.4, 3.0], [41.5, 3.0], [41.6, 3.0], [41.7, 3.0], [41.8, 3.0], [41.9, 3.0], [42.0, 3.0], [42.1, 3.0], [42.2, 3.0], [42.3, 3.0], [42.4, 3.0], [42.5, 3.0], [42.6, 3.0], [42.7, 3.0], [42.8, 3.0], [42.9, 3.0], [43.0, 3.0], [43.1, 3.0], [43.2, 3.0], [43.3, 3.0], [43.4, 3.0], [43.5, 3.0], [43.6, 3.0], [43.7, 3.0], [43.8, 3.0], [43.9, 3.0], [44.0, 3.0], [44.1, 3.0], [44.2, 3.0], [44.3, 3.0], [44.4, 3.0], [44.5, 3.0], [44.6, 3.0], [44.7, 3.0], [44.8, 3.0], [44.9, 3.0], [45.0, 3.0], [45.1, 3.0], [45.2, 3.0], [45.3, 3.0], [45.4, 3.0], [45.5, 3.0], [45.6, 3.0], [45.7, 3.0], [45.8, 3.0], [45.9, 3.0], [46.0, 3.0], [46.1, 3.0], [46.2, 3.0], [46.3, 3.0], [46.4, 3.0], [46.5, 3.0], [46.6, 3.0], [46.7, 3.0], [46.8, 3.0], [46.9, 3.0], [47.0, 3.0], [47.1, 3.0], [47.2, 3.0], [47.3, 3.0], [47.4, 3.0], [47.5, 3.0], [47.6, 3.0], [47.7, 3.0], [47.8, 3.0], [47.9, 3.0], [48.0, 3.0], [48.1, 3.0], [48.2, 3.0], [48.3, 3.0], [48.4, 3.0], [48.5, 3.0], [48.6, 3.0], [48.7, 3.0], [48.8, 3.0], [48.9, 3.0], [49.0, 3.0], [49.1, 3.0], [49.2, 3.0], [49.3, 3.0], [49.4, 3.0], [49.5, 3.0], [49.6, 3.0], [49.7, 3.0], [49.8, 3.0], [49.9, 3.0], [50.0, 3.0], [50.1, 3.0], [50.2, 3.0], [50.3, 3.0], [50.4, 3.0], [50.5, 3.0], [50.6, 3.0], [50.7, 3.0], [50.8, 3.0], [50.9, 3.0], [51.0, 3.0], [51.1, 3.0], [51.2, 3.0], [51.3, 3.0], [51.4, 3.0], [51.5, 3.0], [51.6, 3.0], [51.7, 3.0], [51.8, 3.0], [51.9, 3.0], [52.0, 3.0], [52.1, 3.0], [52.2, 3.0], [52.3, 3.0], [52.4, 3.0], [52.5, 3.0], [52.6, 3.0], [52.7, 3.0], [52.8, 3.0], [52.9, 3.0], [53.0, 3.0], [53.1, 3.0], [53.2, 3.0], [53.3, 3.0], [53.4, 3.0], [53.5, 3.0], [53.6, 3.0], [53.7, 3.0], [53.8, 3.0], [53.9, 3.0], [54.0, 3.0], [54.1, 3.0], [54.2, 3.0], [54.3, 3.0], [54.4, 3.0], [54.5, 3.0], [54.6, 3.0], [54.7, 3.0], [54.8, 3.0], [54.9, 3.0], [55.0, 3.0], [55.1, 3.0], [55.2, 3.0], [55.3, 3.0], [55.4, 3.0], [55.5, 3.0], [55.6, 3.0], [55.7, 3.0], [55.8, 3.0], [55.9, 3.0], [56.0, 3.0], [56.1, 3.0], [56.2, 3.0], [56.3, 3.0], [56.4, 3.0], [56.5, 3.0], [56.6, 3.0], [56.7, 3.0], [56.8, 3.0], [56.9, 3.0], [57.0, 3.0], [57.1, 3.0], [57.2, 3.0], [57.3, 3.0], [57.4, 3.0], [57.5, 3.0], [57.6, 3.0], [57.7, 3.0], [57.8, 3.0], [57.9, 3.0], [58.0, 3.0], [58.1, 3.0], [58.2, 3.0], [58.3, 3.0], [58.4, 3.0], [58.5, 3.0], [58.6, 3.0], [58.7, 3.0], [58.8, 3.0], [58.9, 3.0], [59.0, 3.0], [59.1, 3.0], [59.2, 3.0], [59.3, 3.0], [59.4, 3.0], [59.5, 3.0], [59.6, 3.0], [59.7, 3.0], [59.8, 3.0], [59.9, 3.0], [60.0, 3.0], [60.1, 3.0], [60.2, 3.0], [60.3, 3.0], [60.4, 3.0], [60.5, 3.0], [60.6, 3.0], [60.7, 3.0], [60.8, 3.0], [60.9, 3.0], [61.0, 3.0], [61.1, 3.0], [61.2, 3.0], [61.3, 3.0], [61.4, 3.0], [61.5, 3.0], [61.6, 3.0], [61.7, 3.0], [61.8, 3.0], [61.9, 3.0], [62.0, 3.0], [62.1, 3.0], [62.2, 3.0], [62.3, 3.0], [62.4, 3.0], [62.5, 3.0], [62.6, 3.0], [62.7, 3.0], [62.8, 3.0], [62.9, 3.0], [63.0, 3.0], [63.1, 3.0], [63.2, 3.0], [63.3, 3.0], [63.4, 3.0], [63.5, 3.0], [63.6, 3.0], [63.7, 3.0], [63.8, 3.0], [63.9, 3.0], [64.0, 3.0], [64.1, 3.0], [64.2, 3.0], [64.3, 3.0], [64.4, 3.0], [64.5, 3.0], [64.6, 3.0], [64.7, 3.0], [64.8, 3.0], [64.9, 3.0], [65.0, 3.0], [65.1, 3.0], [65.2, 3.0], [65.3, 3.0], [65.4, 3.0], [65.5, 3.0], [65.6, 3.0], [65.7, 3.0], [65.8, 3.0], [65.9, 3.0], [66.0, 3.0], [66.1, 3.0], [66.2, 3.0], [66.3, 3.0], [66.4, 3.0], [66.5, 3.0], [66.6, 3.0], [66.7, 3.0], [66.8, 3.0], [66.9, 3.0], [67.0, 3.0], [67.1, 3.0], [67.2, 3.0], [67.3, 3.0], [67.4, 3.0], [67.5, 3.0], [67.6, 3.0], [67.7, 3.0], [67.8, 3.0], [67.9, 3.0], [68.0, 3.0], [68.1, 3.0], [68.2, 3.0], [68.3, 3.0], [68.4, 3.0], [68.5, 3.0], [68.6, 3.0], [68.7, 3.0], [68.8, 3.0], [68.9, 3.0], [69.0, 3.0], [69.1, 3.0], [69.2, 3.0], [69.3, 3.0], [69.4, 3.0], [69.5, 3.0], [69.6, 3.0], [69.7, 3.0], [69.8, 3.0], [69.9, 3.0], [70.0, 3.0], [70.1, 3.0], [70.2, 3.0], [70.3, 3.0], [70.4, 3.0], [70.5, 3.0], [70.6, 3.0], [70.7, 3.0], [70.8, 3.0], [70.9, 3.0], [71.0, 3.0], [71.1, 3.0], [71.2, 3.0], [71.3, 3.0], [71.4, 3.0], [71.5, 3.0], [71.6, 3.0], [71.7, 3.0], [71.8, 3.0], [71.9, 3.0], [72.0, 3.0], [72.1, 3.0], [72.2, 3.0], [72.3, 3.0], [72.4, 3.0], [72.5, 3.0], [72.6, 3.0], [72.7, 3.0], [72.8, 3.0], [72.9, 3.0], [73.0, 3.0], [73.1, 3.0], [73.2, 3.0], [73.3, 3.0], [73.4, 3.0], [73.5, 3.0], [73.6, 3.0], [73.7, 3.0], [73.8, 3.0], [73.9, 3.0], [74.0, 3.0], [74.1, 3.0], [74.2, 3.0], [74.3, 3.0], [74.4, 3.0], [74.5, 3.0], [74.6, 3.0], [74.7, 3.0], [74.8, 3.0], [74.9, 3.0], [75.0, 3.0], [75.1, 3.0], [75.2, 3.0], [75.3, 3.0], [75.4, 3.0], [75.5, 3.0], [75.6, 3.0], [75.7, 3.0], [75.8, 3.0], [75.9, 3.0], [76.0, 3.0], [76.1, 3.0], [76.2, 3.0], [76.3, 3.0], [76.4, 3.0], [76.5, 3.0], [76.6, 3.0], [76.7, 3.0], [76.8, 3.0], [76.9, 3.0], [77.0, 3.0], [77.1, 3.0], [77.2, 3.0], [77.3, 3.0], [77.4, 4.0], [77.5, 4.0], [77.6, 4.0], [77.7, 4.0], [77.8, 4.0], [77.9, 4.0], [78.0, 4.0], [78.1, 4.0], [78.2, 4.0], [78.3, 4.0], [78.4, 4.0], [78.5, 4.0], [78.6, 4.0], [78.7, 4.0], [78.8, 4.0], [78.9, 4.0], [79.0, 4.0], [79.1, 4.0], [79.2, 4.0], [79.3, 4.0], [79.4, 4.0], [79.5, 4.0], [79.6, 4.0], [79.7, 4.0], [79.8, 4.0], [79.9, 4.0], [80.0, 4.0], [80.1, 4.0], [80.2, 4.0], [80.3, 4.0], [80.4, 4.0], [80.5, 4.0], [80.6, 4.0], [80.7, 4.0], [80.8, 4.0], [80.9, 4.0], [81.0, 4.0], [81.1, 4.0], [81.2, 4.0], [81.3, 4.0], [81.4, 4.0], [81.5, 4.0], [81.6, 4.0], [81.7, 4.0], [81.8, 4.0], [81.9, 4.0], [82.0, 4.0], [82.1, 4.0], [82.2, 4.0], [82.3, 4.0], [82.4, 4.0], [82.5, 4.0], [82.6, 4.0], [82.7, 4.0], [82.8, 4.0], [82.9, 4.0], [83.0, 4.0], [83.1, 4.0], [83.2, 4.0], [83.3, 4.0], [83.4, 4.0], [83.5, 4.0], [83.6, 4.0], [83.7, 4.0], [83.8, 4.0], [83.9, 4.0], [84.0, 4.0], [84.1, 4.0], [84.2, 4.0], [84.3, 4.0], [84.4, 4.0], [84.5, 4.0], [84.6, 4.0], [84.7, 4.0], [84.8, 4.0], [84.9, 4.0], [85.0, 4.0], [85.1, 4.0], [85.2, 4.0], [85.3, 4.0], [85.4, 4.0], [85.5, 4.0], [85.6, 4.0], [85.7, 4.0], [85.8, 4.0], [85.9, 4.0], [86.0, 4.0], [86.1, 4.0], [86.2, 4.0], [86.3, 4.0], [86.4, 4.0], [86.5, 4.0], [86.6, 4.0], [86.7, 4.0], [86.8, 4.0], [86.9, 4.0], [87.0, 4.0], [87.1, 4.0], [87.2, 4.0], [87.3, 4.0], [87.4, 4.0], [87.5, 4.0], [87.6, 4.0], [87.7, 4.0], [87.8, 4.0], [87.9, 4.0], [88.0, 4.0], [88.1, 4.0], [88.2, 4.0], [88.3, 4.0], [88.4, 4.0], [88.5, 4.0], [88.6, 4.0], [88.7, 4.0], [88.8, 4.0], [88.9, 4.0], [89.0, 4.0], [89.1, 4.0], [89.2, 4.0], [89.3, 4.0], [89.4, 4.0], [89.5, 4.0], [89.6, 4.0], [89.7, 4.0], [89.8, 4.0], [89.9, 4.0], [90.0, 4.0], [90.1, 4.0], [90.2, 4.0], [90.3, 4.0], [90.4, 4.0], [90.5, 4.0], [90.6, 4.0], [90.7, 4.0], [90.8, 4.0], [90.9, 4.0], [91.0, 4.0], [91.1, 4.0], [91.2, 4.0], [91.3, 4.0], [91.4, 4.0], [91.5, 4.0], [91.6, 4.0], [91.7, 4.0], [91.8, 4.0], [91.9, 4.0], [92.0, 4.0], [92.1, 4.0], [92.2, 4.0], [92.3, 4.0], [92.4, 4.0], [92.5, 4.0], [92.6, 5.0], [92.7, 5.0], [92.8, 5.0], [92.9, 5.0], [93.0, 5.0], [93.1, 5.0], [93.2, 5.0], [93.3, 5.0], [93.4, 5.0], [93.5, 5.0], [93.6, 5.0], [93.7, 5.0], [93.8, 5.0], [93.9, 5.0], [94.0, 5.0], [94.1, 5.0], [94.2, 5.0], [94.3, 5.0], [94.4, 5.0], [94.5, 5.0], [94.6, 5.0], [94.7, 5.0], [94.8, 5.0], [94.9, 5.0], [95.0, 5.0], [95.1, 5.0], [95.2, 5.0], [95.3, 5.0], [95.4, 5.0], [95.5, 5.0], [95.6, 5.0], [95.7, 5.0], [95.8, 5.0], [95.9, 5.0], [96.0, 5.0], [96.1, 5.0], [96.2, 5.0], [96.3, 5.0], [96.4, 5.0], [96.5, 5.0], [96.6, 6.0], [96.7, 6.0], [96.8, 6.0], [96.9, 6.0], [97.0, 6.0], [97.1, 6.0], [97.2, 6.0], [97.3, 6.0], [97.4, 6.0], [97.5, 6.0], [97.6, 6.0], [97.7, 6.0], [97.8, 6.0], [97.9, 6.0], [98.0, 6.0], [98.1, 6.0], [98.2, 6.0], [98.3, 6.0], [98.4, 6.0], [98.5, 6.0], [98.6, 6.0], [98.7, 7.0], [98.8, 7.0], [98.9, 7.0], [99.0, 7.0], [99.1, 7.0], [99.2, 7.0], [99.3, 7.0], [99.4, 7.0], [99.5, 7.0], [99.6, 8.0], [99.7, 8.0], [99.8, 8.0], [99.9, 10.0], [100.0, 16.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 100.0, "title": "Response Time Percentiles"}},
        getOptions: function() {
            return {
                series: {
                    points: { show: false }
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentiles'
                },
                xaxis: {
                    tickDecimals: 1,
                    axisLabel: "Percentiles",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Percentile value in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : %x.2 percentile was %y ms"
                },
                selection: { mode: "xy" },
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentiles"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesPercentiles"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesPercentiles"), dataset, prepareOverviewOptions(options));
        }
};

/**
 * @param elementId Id of element where we display message
 */
function setEmptyGraph(elementId) {
    $(function() {
        $(elementId).text("No graph series with filter="+seriesFilter);
    });
}

// Response times percentiles
function refreshResponseTimePercentiles() {
    var infos = responseTimePercentilesInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimePercentiles");
        return;
    }
    if (isGraph($("#flotResponseTimesPercentiles"))){
        infos.createGraph();
    } else {
        var choiceContainer = $("#choicesResponseTimePercentiles");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesPercentiles", "#overviewResponseTimesPercentiles");
        $('#bodyResponseTimePercentiles .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimeDistributionInfos = {
        data: {"result": {"minY": 3.0, "minX": 0.0, "maxY": 2500.0, "series": [{"data": [[0.0, 500.0]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[0.0, 496.0], [100.0, 4.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[0.0, 1497.0], [100.0, 3.0]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[0.0, 1000.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[0.0, 1500.0]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[0.0, 500.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[0.0, 2500.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 100, "maxX": 100.0, "title": "Response Time Distribution"}},
        getOptions: function() {
            var granularity = this.data.result.granularity;
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    barWidth: this.data.result.granularity
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " responses for " + label + " were between " + xval + " and " + (xval + granularity) + " ms";
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimeDistribution"), prepareData(data.result.series, $("#choicesResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshResponseTimeDistribution() {
    var infos = responseTimeDistributionInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeDistribution");
        return;
    }
    if (isGraph($("#flotResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var syntheticResponseTimeDistributionInfos = {
        data: {"result": {"minY": 10500.0, "minX": 0.0, "ticks": [[0, "Requests having \nresponse time <= 500ms"], [1, "Requests having \nresponse time > 500ms and <= 2 000ms"], [2, "Requests having \nresponse time > 2 000ms"], [3, "Requests in error"]], "maxY": 10500.0, "series": [{"data": [[0.0, 10500.0]], "color": "#9ACD32", "isOverall": false, "label": "Requests having \nresponse time <= 500ms", "isController": false}, {"data": [], "color": "yellow", "isOverall": false, "label": "Requests having \nresponse time > 500ms and <= 2 000ms", "isController": false}, {"data": [], "color": "orange", "isOverall": false, "label": "Requests having \nresponse time > 2 000ms", "isController": false}, {"data": [], "color": "#FF6347", "isOverall": false, "label": "Requests in error", "isController": false}], "supportsControllersDiscrimination": false, "maxX": 4.9E-324, "title": "Synthetic Response Times Distribution"}},
        getOptions: function() {
            return {
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendSyntheticResponseTimeDistribution'
                },
                xaxis:{
                    axisLabel: "Response times ranges",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                    tickLength:0,
                    min:-0.5,
                    max:3.5
                },
                yaxis: {
                    axisLabel: "Number of responses",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                bars : {
                    show: true,
                    align: "center",
                    barWidth: 0.25,
                    fill:.75
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: function(label, xval, yval, flotItem){
                        return yval + " " + label;
                    }
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var options = this.getOptions();
            prepareOptions(options, data);
            options.xaxis.ticks = data.result.ticks;
            $.plot($("#flotSyntheticResponseTimeDistribution"), prepareData(data.result.series, $("#choicesSyntheticResponseTimeDistribution")), options);
        }

};

// Response time distribution
function refreshSyntheticResponseTimeDistribution() {
    var infos = syntheticResponseTimeDistributionInfos;
    prepareSeries(infos.data, true);
    if (isGraph($("#flotSyntheticResponseTimeDistribution"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        $('#footerSyntheticResponseTimeDistribution .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var activeThreadsOverTimeInfos = {
        data: {"result": {"minY": 4.85, "minX": 1.74706182E12, "maxY": 60.48529411764707, "series": [{"data": [[1.74706344E12, 51.78260869565217], [1.74706506E12, 54.12499999999997], [1.74706284E12, 60.48529411764707], [1.74706446E12, 54.54497354497356], [1.74706242E12, 55.42021276595745], [1.74706464E12, 54.05641025641025], [1.74706566E12, 17.28], [1.74706404E12, 57.821989528795804], [1.74706182E12, 4.85], [1.74706266E12, 57.509345794392516], [1.74706488E12, 54.937142857142874], [1.74706206E12, 37.30379746835443], [1.74706428E12, 54.01704545454547], [1.74706224E12, 57.30769230769228], [1.74706386E12, 53.06880733944954], [1.74706326E12, 53.68811881188121], [1.74706548E12, 41.779069767441854], [1.74706218E12, 52.544999999999995], [1.7470644E12, 54.59782608695653], [1.7470638E12, 52.91666666666669], [1.74706542E12, 50.65094339622642], [1.74706338E12, 53.165714285714294], [1.7470656E12, 23.93023255813953], [1.747065E12, 53.64088397790056], [1.74706278E12, 59.69938650306746], [1.747062E12, 28.913043478260885], [1.74706362E12, 55.75000000000001], [1.74706302E12, 54.61666666666667], [1.74706524E12, 55.715053763440864], [1.7470632E12, 53.717821782178206], [1.74706482E12, 55.06467661691544], [1.7470626E12, 57.71296296296296], [1.74706422E12, 52.84408602150536], [1.74706314E12, 52.65463917525772], [1.74706536E12, 55.15217391304346], [1.74706254E12, 54.91111111111111], [1.74706476E12, 54.08074534161491], [1.74706272E12, 59.190243902439015], [1.74706434E12, 52.15121951219513], [1.74706212E12, 45.720812182741135], [1.74706374E12, 56.09580838323356], [1.74706296E12, 55.651933701657455], [1.74706458E12, 56.42076502732238], [1.74706236E12, 56.743842364532014], [1.74706398E12, 58.76100628930816], [1.74706578E12, 8.0], [1.74706194E12, 20.372727272727268], [1.74706416E12, 53.15422885572139], [1.74706356E12, 51.61570247933885], [1.74706518E12, 56.62234042553192], [1.74706248E12, 54.134831460674185], [1.7470641E12, 52.68085106382977], [1.74706188E12, 12.206185567010317], [1.7470635E12, 50.87209302325579], [1.74706572E12, 11.9375], [1.74706368E12, 57.19540229885056], [1.7470653E12, 53.186813186813175], [1.74706308E12, 53.364532019704434], [1.7470647E12, 53.500000000000014], [1.74706392E12, 54.91099476439793], [1.74706554E12, 34.230769230769226], [1.74706332E12, 53.51123595505616], [1.74706494E12, 53.45685279187816], [1.7470629E12, 55.39303482587065], [1.74706512E12, 56.167464114832505], [1.7470623E12, 57.13333333333334], [1.74706452E12, 54.264999999999986]], "isOverall": false, "label": "Thread Group", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74706578E12, "title": "Active Threads Over Time"}},
        getOptions: function() {
            return {
                series: {
                    stack: true,
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 6,
                    show: true,
                    container: '#legendActiveThreadsOverTime'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                selection: {
                    mode: 'xy'
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : At %x there were %y active threads"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesActiveThreadsOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotActiveThreadsOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewActiveThreadsOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Active Threads Over Time
function refreshActiveThreadsOverTime(fixTimestamps) {
    var infos = activeThreadsOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotActiveThreadsOverTime"))) {
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesActiveThreadsOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotActiveThreadsOverTime", "#overviewActiveThreadsOverTime");
        $('#footerActiveThreadsOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var timeVsThreadsInfos = {
        data: {"result": {"minY": 2.0, "minX": 1.0, "maxY": 139.0, "series": [{"data": [[32.0, 5.0], [35.0, 4.5], [36.0, 4.0], [38.0, 4.5], [41.0, 5.0], [40.0, 4.333333333333334], [43.0, 3.0], [44.0, 4.0], [45.0, 3.6666666666666665], [47.0, 4.75], [46.0, 9.0], [48.0, 4.0], [49.0, 4.666666666666667], [50.0, 3.5999999999999996], [51.0, 3.9411764705882355], [52.0, 3.7857142857142865], [53.0, 3.768115942028985], [54.0, 3.9908256880733926], [55.0, 4.017857142857144], [57.0, 3.888888888888889], [56.0, 3.704545454545454], [58.0, 3.846153846153846], [59.0, 3.9999999999999996], [61.0, 3.5], [60.0, 3.75], [62.0, 3.3333333333333335], [12.0, 3.5], [17.0, 3.0], [19.0, 3.0], [20.0, 3.0], [25.0, 4.0], [26.0, 4.0], [27.0, 5.0], [29.0, 3.0], [30.0, 3.0], [31.0, 4.5]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[53.01400000000001, 3.906]], "isOverall": false, "label": "Get all albums-Aggregated", "isController": false}, {"data": [[2.0, 69.0], [3.0, 70.0], [4.0, 73.0], [5.0, 70.0], [6.0, 72.0], [7.0, 69.0], [8.0, 71.0], [9.0, 80.0], [10.0, 70.0], [11.0, 65.0], [12.0, 60.0], [13.0, 70.0], [14.0, 75.0], [15.0, 72.0], [16.0, 64.0], [17.0, 66.0], [18.0, 65.0], [19.0, 69.0], [20.0, 139.0], [21.0, 65.0], [22.0, 64.0], [23.0, 65.0], [24.0, 66.0], [25.0, 94.0], [26.0, 73.0], [27.0, 62.0], [28.0, 65.0], [29.0, 72.0], [30.0, 80.0], [31.0, 62.0], [32.0, 61.0], [33.0, 65.0], [34.0, 62.0], [35.0, 68.0], [37.0, 93.0], [36.0, 61.0], [39.0, 71.0], [38.0, 68.0], [40.0, 67.0], [41.0, 70.0], [42.0, 63.0], [43.0, 64.0], [44.0, 61.0], [45.0, 68.0], [46.0, 64.0], [47.0, 62.0], [48.0, 69.0], [49.0, 66.0], [50.0, 66.37500000000001], [51.0, 64.25000000000001], [52.0, 64.60869565217394], [53.0, 65.69090909090912], [54.0, 65.3837209302326], [55.0, 65.07865168539325], [56.0, 64.7272727272727], [57.0, 64.54761904761904], [58.0, 63.55555555555554], [59.0, 66.46153846153845], [60.0, 63.222222222222214], [61.0, 71.2], [62.0, 65.75], [1.0, 112.0]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[52.24999999999994, 65.658]], "isOverall": false, "label": "Home page-Aggregated", "isController": true}, {"data": [[7.0, 43.0], [8.0, 40.0], [9.0, 57.0], [10.0, 45.0], [12.0, 41.0], [13.0, 49.75], [15.0, 51.5], [16.0, 44.0], [17.0, 47.5], [18.0, 50.0], [19.0, 38.0], [20.0, 43.666666666666664], [21.0, 49.5], [22.0, 51.5], [23.0, 54.0], [24.0, 48.0], [25.0, 47.5], [26.0, 46.5], [27.0, 44.0], [28.0, 45.833333333333336], [29.0, 38.5], [31.0, 41.5], [32.0, 40.666666666666664], [33.0, 45.0], [34.0, 43.4], [35.0, 50.0], [36.0, 43.0], [37.0, 43.125], [38.0, 41.333333333333336], [39.0, 41.0], [40.0, 43.75], [41.0, 41.4], [42.0, 47.25], [43.0, 41.0], [44.0, 44.0], [45.0, 42.8], [46.0, 48.77777777777778], [47.0, 121.0], [48.0, 43.0], [49.0, 48.727272727272734], [50.0, 44.395348837209305], [51.0, 45.296296296296305], [52.0, 43.69642857142858], [53.0, 43.46766169154227], [54.0, 43.87323943661974], [55.0, 44.06896551724141], [56.0, 44.089552238805965], [57.0, 43.489999999999995], [58.0, 43.650485436893206], [59.0, 44.699999999999996], [60.0, 43.00000000000001], [61.0, 51.06666666666666], [62.0, 41.4]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[52.774666666666626, 44.35133333333333]], "isOverall": false, "label": "Play songs from home page-Aggregated", "isController": false}, {"data": [[2.0, 7.0], [3.0, 8.0], [4.0, 7.0], [5.0, 7.0], [6.0, 7.0], [7.0, 7.0], [8.0, 5.0], [9.0, 5.0], [10.0, 8.0], [11.0, 7.0], [12.0, 5.0], [13.0, 6.0], [14.0, 6.0], [15.0, 5.0], [16.0, 5.0], [17.0, 5.0], [18.0, 6.0], [19.0, 7.0], [20.0, 77.0], [21.0, 7.0], [22.0, 7.0], [23.0, 6.0], [24.0, 6.0], [25.0, 10.0], [26.0, 14.0], [27.0, 5.0], [28.0, 7.0], [29.0, 6.0], [30.0, 6.0], [31.0, 6.0], [32.0, 5.0], [33.0, 7.0], [34.0, 5.0], [35.0, 7.0], [36.0, 8.0], [37.0, 6.0], [38.0, 10.0], [39.0, 8.0], [40.0, 7.0], [41.0, 9.0], [42.0, 6.0], [43.0, 6.0], [44.0, 6.0], [45.0, 10.0], [46.0, 6.0], [47.0, 6.0], [48.0, 10.0], [49.0, 5.0], [50.0, 6.875], [51.0, 6.5], [52.0, 6.173913043478262], [53.0, 6.363636363636364], [54.0, 6.662790697674418], [55.0, 6.2921348314606735], [56.0, 6.4727272727272736], [57.0, 6.0], [58.0, 6.0], [59.0, 6.384615384615385], [60.0, 6.625], [61.0, 6.666666666666666], [62.0, 5.25], [1.0, 36.0]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[52.25199999999998, 6.600000000000001]], "isOverall": false, "label": "Trending playlist-Aggregated", "isController": false}, {"data": [[32.0, 22.0], [35.0, 21.0], [36.0, 20.0], [38.0, 21.5], [41.0, 22.0], [40.0, 19.166666666666664], [43.0, 23.0], [45.0, 21.666666666666668], [44.0, 22.0], [47.0, 31.0], [46.0, 28.0], [48.0, 22.2], [49.0, 28.333333333333332], [50.0, 20.555555555555557], [51.0, 20.0], [52.0, 19.749999999999993], [53.0, 19.44927536231884], [54.0, 19.568807339449542], [55.0, 19.78571428571429], [57.0, 19.740740740740744], [56.0, 20.02272727272727], [58.0, 20.025641025641026], [59.0, 21.18181818181818], [61.0, 20.375], [60.0, 19.75], [62.0, 22.0], [12.0, 20.0], [17.0, 16.0], [19.0, 19.0], [20.0, 16.0], [25.0, 18.0], [26.0, 20.0], [27.0, 26.0], [29.0, 17.0], [30.0, 15.0], [31.0, 21.0]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[53.01599999999998, 20.02600000000001]], "isOverall": false, "label": "Albums page-Aggregated", "isController": true}, {"data": [[2.0, 4.0], [3.0, 6.0], [4.0, 5.0], [5.0, 6.0], [6.0, 6.0], [7.0, 4.0], [8.0, 10.0], [9.0, 4.0], [10.0, 4.0], [11.0, 5.0], [12.0, 3.0], [13.0, 5.0], [14.0, 4.0], [15.0, 4.0], [16.0, 4.0], [17.0, 3.0], [18.0, 4.0], [19.0, 3.0], [20.0, 4.0], [21.0, 4.0], [22.0, 3.0], [23.0, 3.0], [24.0, 3.0], [25.0, 8.0], [26.0, 4.0], [27.0, 3.0], [28.0, 6.0], [29.0, 6.0], [30.0, 5.0], [31.0, 3.0], [32.0, 3.0], [33.0, 5.0], [34.0, 3.0], [35.0, 4.0], [36.0, 3.0], [37.0, 4.0], [38.0, 4.0], [39.0, 4.0], [40.0, 6.0], [41.0, 4.0], [42.0, 3.0], [43.0, 3.0], [44.0, 3.0], [45.0, 3.0], [46.0, 5.0], [47.0, 3.0], [48.0, 5.0], [49.0, 4.0], [50.0, 3.0], [51.0, 4.5], [52.0, 3.9130434782608696], [53.0, 3.927272727272728], [54.0, 3.8837209302325584], [55.0, 3.955056179775283], [56.0, 4.018181818181819], [57.0, 4.047619047619048], [58.0, 3.861111111111111], [59.0, 3.615384615384616], [60.0, 4.666666666666667], [61.0, 4.2], [62.0, 4.25], [1.0, 4.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[52.24999999999995, 3.9740000000000006]], "isOverall": false, "label": "Cover 2-Aggregated", "isController": false}, {"data": [[2.0, 6.0], [3.0, 4.0], [4.0, 6.0], [5.0, 4.0], [6.0, 4.0], [7.0, 4.0], [8.0, 4.0], [9.0, 4.0], [10.0, 4.0], [11.0, 3.0], [12.0, 3.0], [13.0, 4.0], [14.0, 4.0], [15.0, 4.0], [16.0, 3.0], [17.0, 3.0], [18.0, 4.0], [19.0, 3.0], [20.0, 4.0], [21.0, 4.0], [22.0, 3.0], [23.0, 3.0], [24.0, 4.0], [25.0, 6.0], [26.0, 4.0], [27.0, 3.0], [28.0, 4.0], [29.0, 6.0], [30.0, 5.0], [31.0, 3.0], [32.0, 4.0], [33.0, 5.0], [34.0, 4.0], [35.0, 3.0], [36.0, 3.0], [37.0, 4.0], [38.0, 3.0], [39.0, 4.0], [40.0, 3.0], [41.0, 4.0], [42.0, 3.0], [43.0, 4.0], [44.0, 3.0], [45.0, 3.0], [46.0, 3.0], [47.0, 3.0], [48.0, 3.0], [49.0, 5.0], [50.0, 3.5], [51.0, 3.5000000000000004], [52.0, 3.7826086956521743], [53.0, 3.527272727272728], [54.0, 3.6860465116279073], [55.0, 3.719101123595506], [56.0, 3.6000000000000005], [57.0, 3.8333333333333326], [58.0, 3.7222222222222223], [59.0, 3.8461538461538467], [60.0, 3.25], [61.0, 3.833333333333333], [62.0, 4.0], [1.0, 4.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[52.25199999999998, 3.7000000000000006]], "isOverall": false, "label": "Cover 1-Aggregated", "isController": false}, {"data": [[2.0, 4.0], [3.0, 4.0], [4.0, 5.0], [5.0, 4.0], [6.0, 4.0], [7.0, 5.0], [8.0, 3.0], [9.0, 9.0], [10.0, 6.0], [11.0, 3.0], [12.0, 3.0], [13.0, 6.0], [14.0, 4.0], [15.0, 4.0], [16.0, 5.0], [17.0, 6.0], [18.0, 4.0], [19.0, 4.0], [20.0, 4.0], [21.0, 3.0], [22.0, 5.0], [23.0, 4.0], [24.0, 5.0], [25.0, 4.0], [26.0, 3.0], [27.0, 4.0], [28.0, 3.0], [29.0, 5.0], [30.0, 3.0], [31.0, 3.0], [32.0, 3.0], [33.0, 3.0], [34.0, 4.0], [35.0, 7.0], [36.0, 3.0], [37.0, 4.0], [38.0, 3.0], [39.0, 3.0], [40.0, 5.0], [41.0, 4.0], [42.0, 3.0], [43.0, 3.0], [44.0, 3.0], [45.0, 4.0], [46.0, 3.0], [47.0, 4.0], [48.0, 4.0], [49.0, 4.5], [50.0, 3.0], [51.0, 3.6666666666666665], [52.0, 3.6956521739130435], [53.0, 3.5090909090909093], [54.0, 3.6511627906976742], [55.0, 3.3932584269662933], [56.0, 3.4545454545454555], [57.0, 3.6904761904761902], [58.0, 3.583333333333334], [59.0, 3.5384615384615388], [60.0, 3.4444444444444446], [61.0, 3.2], [62.0, 3.75], [1.0, 13.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[52.24999999999995, 3.607999999999999]], "isOverall": false, "label": "Cover 4-Aggregated", "isController": false}, {"data": [[2.0, 3.0], [3.0, 3.0], [4.0, 3.0], [5.0, 4.0], [6.0, 4.0], [7.0, 4.0], [8.0, 3.0], [9.0, 5.0], [10.0, 3.0], [11.0, 3.0], [12.0, 5.0], [13.0, 3.0], [14.0, 3.0], [15.0, 4.0], [16.0, 3.0], [17.0, 3.0], [18.0, 3.0], [19.0, 3.0], [20.0, 5.0], [21.0, 2.0], [22.0, 3.0], [23.0, 5.0], [24.0, 5.0], [25.0, 6.0], [26.0, 4.0], [27.0, 3.0], [28.0, 3.0], [29.0, 3.0], [30.0, 3.0], [31.0, 3.0], [32.0, 3.0], [33.0, 3.0], [34.0, 3.0], [35.0, 3.0], [36.0, 2.0], [37.0, 3.0], [38.0, 2.0], [39.0, 3.0], [40.0, 3.0], [41.0, 3.0], [42.0, 3.0], [43.0, 2.0], [44.0, 3.0], [45.0, 4.0], [46.0, 4.0], [47.0, 3.0], [48.0, 3.0], [49.0, 2.5], [50.0, 2.375], [51.0, 2.6666666666666665], [52.0, 2.9565217391304346], [53.0, 2.8181818181818183], [54.0, 2.872093023255814], [55.0, 2.7865168539325853], [56.0, 2.818181818181818], [57.0, 2.761904761904762], [58.0, 3.111111111111111], [59.0, 2.8461538461538467], [60.0, 2.6666666666666665], [61.0, 3.0], [62.0, 3.25], [1.0, 5.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[52.24999999999995, 2.8899999999999983]], "isOverall": false, "label": "Cover 3-Aggregated", "isController": false}, {"data": [[34.0, 44.666666666666664], [35.0, 38.0], [37.0, 38.0], [38.0, 52.0], [39.0, 39.8], [40.0, 42.6875], [42.0, 42.0], [43.0, 41.0], [45.0, 41.0], [44.0, 41.0], [47.0, 39.0], [46.0, 55.14285714285714], [48.0, 42.0], [49.0, 41.642857142857146], [50.0, 38.89473684210526], [51.0, 41.60869565217392], [52.0, 39.695121951219505], [53.0, 39.963768115942], [54.0, 40.09183673469386], [55.0, 40.277027027027046], [56.0, 39.999999999999986], [57.0, 39.77142857142858], [58.0, 40.61971830985916], [59.0, 39.977272727272734], [60.0, 38.73333333333333], [61.0, 40.28571428571428], [62.0, 40.0], [8.0, 44.666666666666664], [11.0, 37.0], [12.0, 40.0], [15.0, 36.0], [16.0, 39.0], [17.0, 41.0], [19.0, 42.0], [20.0, 38.5], [21.0, 36.5], [23.0, 40.0], [24.0, 38.333333333333336], [25.0, 38.5], [26.0, 35.0], [28.0, 35.0], [29.0, 43.0], [31.0, 40.0]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[53.033000000000015, 40.244]], "isOverall": false, "label": "Play songs from albums-Aggregated", "isController": false}, {"data": [[2.0, 3.0], [3.0, 4.0], [4.0, 3.0], [5.0, 3.0], [6.0, 3.0], [7.0, 3.0], [8.0, 3.0], [9.0, 4.0], [10.0, 3.0], [11.0, 4.0], [12.0, 2.0], [13.0, 4.0], [14.0, 7.0], [15.0, 6.0], [16.0, 3.0], [17.0, 4.0], [18.0, 3.0], [19.0, 8.0], [20.0, 3.0], [21.0, 3.0], [22.0, 4.0], [23.0, 4.0], [24.0, 3.0], [25.0, 3.0], [26.0, 3.0], [27.0, 5.0], [28.0, 3.0], [29.0, 3.0], [30.0, 4.0], [31.0, 3.0], [32.0, 3.0], [33.0, 2.0], [34.0, 3.0], [35.0, 5.0], [36.0, 3.0], [37.0, 4.0], [38.0, 6.0], [39.0, 2.0], [40.0, 4.0], [41.0, 3.0], [42.0, 4.0], [43.0, 5.0], [44.0, 3.0], [45.0, 4.0], [46.0, 3.0], [47.0, 3.0], [48.0, 4.0], [49.0, 3.5], [50.0, 3.5], [51.0, 3.416666666666667], [52.0, 3.565217391304348], [53.0, 3.6181818181818186], [54.0, 3.27906976744186], [55.0, 3.359550561797752], [56.0, 3.381818181818181], [57.0, 3.5238095238095233], [58.0, 3.5277777777777786], [59.0, 3.615384615384615], [60.0, 2.7777777777777777], [61.0, 4.2], [62.0, 3.0], [1.0, 4.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[52.24999999999995, 3.454000000000002]], "isOverall": false, "label": "Cover 5-Aggregated", "isController": false}, {"data": [[5.0, 8.0], [6.0, 5.5], [7.0, 6.5], [8.0, 5.0], [9.0, 5.5], [10.0, 5.0], [11.0, 5.0], [12.0, 5.0], [13.0, 6.0], [14.0, 5.666666666666667], [15.0, 5.0], [16.0, 5.0], [17.0, 5.333333333333333], [18.0, 5.0], [19.0, 5.6], [20.0, 5.0], [21.0, 6.0], [22.0, 6.666666666666667], [23.0, 4.833333333333333], [25.0, 5.5], [26.0, 5.5], [27.0, 5.0], [28.0, 6.0], [29.0, 5.0], [30.0, 5.0], [31.0, 5.0], [32.0, 7.333333333333333], [33.0, 5.8], [34.0, 5.5], [35.0, 4.0], [36.0, 5.75], [37.0, 5.5], [38.0, 5.0], [39.0, 6.25], [40.0, 5.25], [41.0, 6.0], [42.0, 5.2], [43.0, 5.0], [44.0, 4.666666666666667], [45.0, 6.0], [46.0, 6.0], [47.0, 5.0], [48.0, 6.0], [49.0, 6.538461538461538], [50.0, 5.068965517241379], [51.0, 5.0925925925925934], [52.0, 5.068702290076334], [53.0, 4.964824120603018], [54.0, 5.143911439114388], [55.0, 5.343478260869563], [56.0, 5.095238095238094], [57.0, 5.123809523809523], [58.0, 5.112068965517241], [59.0, 5.125000000000001], [60.0, 5.7142857142857135], [61.0, 5.1764705882352935], [62.0, 4.333333333333334]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[52.15466666666664, 5.18400000000001]], "isOverall": false, "label": "Search-Aggregated", "isController": false}, {"data": [[2.0, 42.0], [3.0, 41.0], [4.0, 44.0], [5.0, 42.0], [6.0, 44.0], [7.0, 42.0], [8.0, 43.0], [9.0, 49.0], [10.0, 42.0], [11.0, 40.0], [12.0, 39.0], [13.0, 42.0], [14.0, 47.0], [15.0, 45.0], [16.0, 41.0], [17.0, 42.0], [18.0, 41.0], [19.0, 41.0], [20.0, 42.0], [21.0, 42.0], [22.0, 39.0], [23.0, 40.0], [24.0, 40.0], [25.0, 57.0], [26.0, 41.0], [27.0, 39.0], [28.0, 39.0], [29.0, 43.0], [30.0, 54.0], [31.0, 41.0], [32.0, 40.0], [33.0, 40.0], [34.0, 40.0], [35.0, 39.0], [36.0, 39.0], [37.0, 68.0], [38.0, 40.0], [39.0, 47.0], [40.0, 39.0], [41.0, 43.0], [42.0, 41.0], [43.0, 41.0], [44.0, 40.0], [45.0, 40.0], [46.0, 40.0], [47.0, 40.0], [48.0, 40.0], [49.0, 41.5], [50.0, 44.125], [51.0, 40.0], [52.0, 40.52173913043479], [53.0, 41.92727272727272], [54.0, 41.348837209302324], [55.0, 41.573033707865164], [56.0, 40.98181818181818], [57.0, 40.6904761904762], [58.0, 39.75], [59.0, 42.61538461538462], [60.0, 39.625], [61.0, 45.16666666666667], [62.0, 42.25], [1.0, 46.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[52.25199999999998, 41.43199999999999]], "isOverall": false, "label": "Initial song-Aggregated", "isController": false}, {"data": [[32.0, 3.4], [35.0, 3.3000000000000003], [36.0, 3.2], [38.0, 3.4000000000000004], [41.0, 3.4], [40.0, 2.966666666666667], [43.0, 4.0], [44.0, 3.6], [45.0, 3.6], [47.0, 5.25], [46.0, 3.8], [48.0, 3.640000000000001], [49.0, 4.733333333333334], [50.0, 3.354166666666666], [51.0, 3.229885057471265], [52.0, 3.1928571428571426], [53.0, 3.1362318840579686], [54.0, 3.1155963302752285], [55.0, 3.15357142857143], [57.0, 3.17037037037037], [56.0, 3.263636363636363], [58.0, 3.235897435897436], [59.0, 3.4363636363636356], [61.0, 3.3749999999999996], [60.0, 3.1999999999999997], [62.0, 3.7333333333333334], [12.0, 3.3000000000000003], [17.0, 2.6], [19.0, 3.2], [20.0, 2.6], [25.0, 2.8], [26.0, 3.2], [27.0, 4.2], [29.0, 2.8], [30.0, 2.4], [31.0, 3.3000000000000003]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}, {"data": [[53.014799999999916, 3.223999999999997]], "isOverall": false, "label": "Get paths to songs from each album-Aggregated", "isController": false}], "supportsControllersDiscrimination": true, "maxX": 62.0, "title": "Time VS Threads"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    axisLabel: "Number of active threads",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response times in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: { noColumns: 2,show: true, container: '#legendTimeVsThreads' },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s: At %x.2 active threads, Average response time was %y.2 ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesTimeVsThreads"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotTimesVsThreads"), dataset, options);
            // setup overview
            $.plot($("#overviewTimesVsThreads"), dataset, prepareOverviewOptions(options));
        }
};

// Time vs threads
function refreshTimeVsThreads(){
    var infos = timeVsThreadsInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTimeVsThreads");
        return;
    }
    if(isGraph($("#flotTimesVsThreads"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTimeVsThreads");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTimesVsThreads", "#overviewTimesVsThreads");
        $('#footerTimeVsThreads .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var bytesThroughputOverTimeInfos = {
        data : {"result": {"minY": 8.5, "minX": 1.74706182E12, "maxY": 7120981.85, "series": [{"data": [[1.74706344E12, 5523242.833333333], [1.74706506E12, 6744370.7], [1.74706284E12, 6001537.5], [1.74706446E12, 6181573.166666667], [1.74706242E12, 6257113.35], [1.74706464E12, 5531057.933333334], [1.74706566E12, 1179022.85], [1.74706404E12, 5630648.85], [1.74706182E12, 1508393.5166666666], [1.74706266E12, 6250636.316666666], [1.74706488E12, 6348930.55], [1.74706206E12, 4945091.85], [1.74706428E12, 5418620.233333333], [1.74706224E12, 5429255.15], [1.74706386E12, 5170929.05], [1.74706326E12, 5735238.116666666], [1.74706548E12, 3327351.15], [1.74706218E12, 7120981.85], [1.7470644E12, 6500046.4], [1.7470638E12, 6653316.55], [1.74706542E12, 4114859.7666666666], [1.74706338E12, 5330657.666666667], [1.7470656E12, 1591011.6166666667], [1.747065E12, 5209094.5], [1.74706278E12, 6233485.266666667], [1.747062E12, 3958117.433333333], [1.74706362E12, 6169425.216666667], [1.74706302E12, 6098495.783333333], [1.74706524E12, 6591633.633333334], [1.7470632E12, 5586108.15], [1.74706482E12, 5575374.483333333], [1.7470626E12, 6400138.85], [1.74706422E12, 5911651.516666667], [1.74706314E12, 5777114.166666667], [1.74706536E12, 6627546.116666666], [1.74706254E12, 5430590.616666666], [1.74706476E12, 5475263.95], [1.74706272E12, 6303793.566666666], [1.74706434E12, 5740149.983333333], [1.74706212E12, 4930333.183333334], [1.74706374E12, 5656224.05], [1.74706296E12, 4718495.75], [1.74706458E12, 6025069.683333334], [1.74706236E12, 5495421.066666666], [1.74706398E12, 6188246.633333334], [1.74706578E12, 318294.8], [1.74706194E12, 3250592.8], [1.74706416E12, 6555736.283333333], [1.74706356E12, 6568323.5], [1.74706518E12, 5918890.3], [1.74706248E12, 6335531.65], [1.7470641E12, 6469661.466666667], [1.74706188E12, 2581659.9833333334], [1.7470635E12, 6156076.466666667], [1.74706572E12, 213132.15], [1.74706368E12, 5826199.15], [1.7470653E12, 5522808.3], [1.74706308E12, 6909071.133333334], [1.7470647E12, 6258345.566666666], [1.74706392E12, 5863493.966666667], [1.74706554E12, 1844958.1333333333], [1.74706332E12, 6733216.216666667], [1.74706494E12, 5561835.933333334], [1.7470629E12, 6869995.366666666], [1.74706512E12, 6180622.633333334], [1.7470623E12, 5869275.033333333], [1.74706452E12, 5889234.05]], "isOverall": false, "label": "Bytes received per second", "isController": false}, {"data": [[1.74706344E12, 420.18333333333334], [1.74706506E12, 477.56666666666666], [1.74706284E12, 467.6333333333333], [1.74706446E12, 435.8333333333333], [1.74706242E12, 434.43333333333334], [1.74706464E12, 444.6], [1.74706566E12, 61.63333333333333], [1.74706404E12, 437.5833333333333], [1.74706182E12, 171.61666666666667], [1.74706266E12, 491.56666666666666], [1.74706488E12, 402.55], [1.74706206E12, 362.45], [1.74706428E12, 405.4166666666667], [1.74706224E12, 417.1666666666667], [1.74706386E12, 494.0], [1.74706326E12, 460.01666666666665], [1.74706548E12, 205.86666666666667], [1.74706218E12, 466.85], [1.7470644E12, 425.8666666666667], [1.7470638E12, 441.23333333333335], [1.74706542E12, 255.3], [1.74706338E12, 400.3666666666667], [1.7470656E12, 103.76666666666667], [1.747065E12, 413.98333333333335], [1.74706278E12, 380.81666666666666], [1.747062E12, 309.45], [1.74706362E12, 450.5], [1.74706302E12, 415.31666666666666], [1.74706524E12, 431.0833333333333], [1.7470632E12, 460.0833333333333], [1.74706482E12, 458.51666666666665], [1.7470626E12, 494.6333333333333], [1.74706422E12, 427.48333333333335], [1.74706314E12, 445.68333333333334], [1.74706536E12, 528.85], [1.74706254E12, 408.7], [1.74706476E12, 372.7], [1.74706272E12, 467.6333333333333], [1.74706434E12, 466.1166666666667], [1.74706212E12, 445.3833333333333], [1.74706374E12, 383.43333333333334], [1.74706296E12, 409.46666666666664], [1.74706458E12, 423.4], [1.74706236E12, 459.8833333333333], [1.74706398E12, 367.51666666666665], [1.74706578E12, 8.5], [1.74706194E12, 246.1], [1.74706416E12, 462.9], [1.74706356E12, 553.15], [1.74706518E12, 433.1166666666667], [1.74706248E12, 412.8], [1.7470641E12, 538.7333333333333], [1.74706188E12, 214.83333333333334], [1.7470635E12, 397.6333333333333], [1.74706572E12, 35.93333333333333], [1.74706368E12, 401.4], [1.7470653E12, 416.6166666666667], [1.74706308E12, 469.3], [1.7470647E12, 463.53333333333336], [1.74706392E12, 437.26666666666665], [1.74706554E12, 124.38333333333334], [1.74706332E12, 415.8], [1.74706494E12, 450.81666666666666], [1.7470629E12, 465.51666666666665], [1.74706512E12, 479.3333333333333], [1.7470623E12, 416.68333333333334], [1.74706452E12, 456.7]], "isOverall": false, "label": "Bytes sent per second", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74706578E12, "title": "Bytes Throughput Over Time"}},
        getOptions : function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity) ,
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Bytes / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendBytesThroughputOverTime'
                },
                selection: {
                    mode: "xy"
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y"
                }
            };
        },
        createGraph : function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesBytesThroughputOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotBytesThroughputOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewBytesThroughputOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Bytes throughput Over Time
function refreshBytesThroughputOverTime(fixTimestamps) {
    var infos = bytesThroughputOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotBytesThroughputOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesBytesThroughputOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotBytesThroughputOverTime", "#overviewBytesThroughputOverTime");
        $('#footerBytesThroughputOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var responseTimesOverTimeInfos = {
        data: {"result": {"minY": 2.25, "minX": 1.74706182E12, "maxY": 75.74999999999999, "series": [{"data": [[1.74706344E12, 3.857142857142857], [1.74706506E12, 3.888888888888889], [1.74706284E12, 3.3000000000000003], [1.74706446E12, 3.625], [1.74706242E12, 4.25], [1.74706464E12, 4.1], [1.74706566E12, 3.0], [1.74706404E12, 3.7777777777777777], [1.74706266E12, 4.09090909090909], [1.74706488E12, 4.0], [1.74706206E12, 4.6], [1.74706428E12, 3.857142857142857], [1.74706224E12, 4.125], [1.74706386E12, 3.9230769230769225], [1.74706326E12, 3.4444444444444446], [1.74706548E12, 3.8749999999999996], [1.74706218E12, 3.857142857142857], [1.7470644E12, 3.5714285714285716], [1.7470638E12, 4.571428571428571], [1.74706542E12, 6.0], [1.74706338E12, 4.142857142857143], [1.7470656E12, 3.75], [1.747065E12, 3.6249999999999996], [1.74706278E12, 3.75], [1.747062E12, 5.0], [1.74706362E12, 4.374999999999999], [1.74706302E12, 4.285714285714286], [1.74706524E12, 4.0], [1.7470632E12, 4.636363636363637], [1.74706482E12, 3.5], [1.7470626E12, 4.083333333333333], [1.74706422E12, 3.6249999999999996], [1.74706314E12, 3.7777777777777772], [1.74706536E12, 3.692307692307692], [1.74706254E12, 4.571428571428571], [1.74706476E12, 4.0], [1.74706272E12, 4.1], [1.74706434E12, 3.6999999999999997], [1.74706212E12, 4.181818181818182], [1.74706374E12, 4.0], [1.74706296E12, 4.111111111111111], [1.74706458E12, 3.571428571428571], [1.74706236E12, 3.5999999999999996], [1.74706398E12, 4.333333333333333], [1.74706416E12, 3.375], [1.74706356E12, 3.466666666666667], [1.74706518E12, 3.25], [1.74706248E12, 4.166666666666667], [1.7470641E12, 3.714285714285714], [1.7470635E12, 4.333333333333334], [1.74706572E12, 3.5], [1.74706368E12, 4.0], [1.7470653E12, 3.5], [1.74706308E12, 3.7499999999999996], [1.7470647E12, 4.111111111111111], [1.74706392E12, 3.3333333333333335], [1.74706554E12, 3.6], [1.74706332E12, 3.5], [1.74706494E12, 3.6999999999999993], [1.7470629E12, 4.375], [1.74706512E12, 3.6363636363636362], [1.7470623E12, 3.4285714285714284], [1.74706452E12, 4.333333333333333]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74706344E12, 62.333333333333336], [1.74706506E12, 64.0], [1.74706284E12, 68.49999999999999], [1.74706446E12, 62.375], [1.74706242E12, 67.125], [1.74706464E12, 62.875], [1.74706404E12, 62.50000000000001], [1.74706182E12, 75.74999999999999], [1.74706266E12, 63.25], [1.74706488E12, 62.333333333333336], [1.74706206E12, 70.0], [1.74706428E12, 66.74999999999999], [1.74706224E12, 68.625], [1.74706386E12, 63.75], [1.74706326E12, 70.0], [1.74706218E12, 68.77777777777777], [1.7470644E12, 63.75], [1.7470638E12, 67.66666666666667], [1.74706542E12, 69.0], [1.74706338E12, 64.25], [1.747065E12, 62.25], [1.74706278E12, 65.0], [1.747062E12, 70.44444444444444], [1.74706362E12, 64.11111111111111], [1.74706302E12, 63.125], [1.74706524E12, 62.44444444444444], [1.7470632E12, 66.62499999999999], [1.74706482E12, 64.75], [1.7470626E12, 67.75], [1.74706422E12, 65.25], [1.74706314E12, 65.125], [1.74706536E12, 67.375], [1.74706254E12, 66.55555555555556], [1.74706476E12, 63.87499999999999], [1.74706272E12, 62.111111111111114], [1.74706434E12, 68.66666666666669], [1.74706212E12, 64.875], [1.74706374E12, 63.75], [1.74706296E12, 68.5], [1.74706458E12, 64.5], [1.74706236E12, 66.66666666666667], [1.74706398E12, 63.0], [1.74706194E12, 74.875], [1.74706416E12, 64.33333333333333], [1.74706356E12, 69.875], [1.74706518E12, 61.5], [1.74706248E12, 63.87499999999999], [1.7470641E12, 66.375], [1.74706188E12, 69.5], [1.7470635E12, 67.25], [1.74706368E12, 61.12500000000001], [1.7470653E12, 65.0], [1.74706308E12, 67.66666666666667], [1.7470647E12, 65.77777777777777], [1.74706392E12, 65.625], [1.74706332E12, 62.0], [1.74706494E12, 64.625], [1.7470629E12, 64.77777777777777], [1.74706512E12, 63.5], [1.7470623E12, 67.75], [1.74706452E12, 62.666666666666664]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74706344E12, 48.1], [1.74706506E12, 42.896551724137936], [1.74706284E12, 48.47826086956521], [1.74706446E12, 44.64], [1.74706242E12, 44.76923076923077], [1.74706464E12, 43.59090909090909], [1.74706404E12, 42.95833333333333], [1.74706182E12, 43.0], [1.74706266E12, 47.185185185185176], [1.74706488E12, 43.46666666666667], [1.74706206E12, 42.629629629629626], [1.74706428E12, 41.086956521739125], [1.74706224E12, 44.2], [1.74706386E12, 41.0], [1.74706326E12, 45.31818181818181], [1.74706548E12, 45.16666666666667], [1.74706218E12, 42.909090909090914], [1.7470644E12, 44.4074074074074], [1.7470638E12, 45.81250000000001], [1.74706542E12, 68.2], [1.74706338E12, 42.285714285714285], [1.7470656E12, 42.0], [1.747065E12, 41.47826086956522], [1.74706278E12, 42.08695652173913], [1.747062E12, 44.19047619047618], [1.74706362E12, 43.238095238095234], [1.74706302E12, 44.81481481481482], [1.74706524E12, 43.06666666666666], [1.7470632E12, 42.599999999999994], [1.74706482E12, 41.54545454545454], [1.7470626E12, 43.517241379310335], [1.74706422E12, 47.692307692307686], [1.74706314E12, 43.04761904761904], [1.74706536E12, 44.20833333333334], [1.74706254E12, 45.57894736842105], [1.74706476E12, 43.70833333333334], [1.74706272E12, 44.96000000000001], [1.74706434E12, 43.2], [1.74706212E12, 46.18181818181818], [1.74706374E12, 43.87999999999999], [1.74706296E12, 42.58823529411764], [1.74706458E12, 41.67857142857143], [1.74706236E12, 48.21739130434782], [1.74706398E12, 44.07692307692307], [1.74706194E12, 49.05882352941177], [1.74706416E12, 41.959999999999994], [1.74706356E12, 45.44], [1.74706518E12, 42.47826086956521], [1.74706248E12, 43.4], [1.7470641E12, 45.0], [1.74706188E12, 48.833333333333336], [1.7470635E12, 44.411764705882355], [1.74706368E12, 42.12], [1.7470653E12, 43.46428571428572], [1.74706308E12, 43.733333333333334], [1.7470647E12, 44.62499999999999], [1.74706392E12, 44.90909090909091], [1.74706554E12, 41.4], [1.74706332E12, 44.20000000000001], [1.74706494E12, 41.16666666666668], [1.7470629E12, 44.11764705882353], [1.74706512E12, 41.869565217391305], [1.7470623E12, 45.24999999999999], [1.74706452E12, 44.47826086956521]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74706344E12, 5.888888888888888], [1.74706506E12, 6.444444444444445], [1.74706284E12, 6.0], [1.74706446E12, 6.250000000000001], [1.74706242E12, 6.75], [1.74706464E12, 6.375000000000001], [1.74706404E12, 6.125], [1.74706182E12, 10.5], [1.74706266E12, 6.375], [1.74706488E12, 6.0], [1.74706206E12, 7.500000000000001], [1.74706428E12, 6.25], [1.74706224E12, 6.125], [1.74706386E12, 6.625], [1.74706326E12, 6.222222222222222], [1.74706218E12, 6.333333333333333], [1.7470644E12, 6.625], [1.7470638E12, 7.000000000000001], [1.74706542E12, 10.0], [1.74706338E12, 6.5], [1.747065E12, 6.374999999999999], [1.74706278E12, 6.0], [1.747062E12, 7.333333333333333], [1.74706362E12, 5.0], [1.74706302E12, 6.0], [1.74706524E12, 6.333333333333334], [1.7470632E12, 6.125], [1.74706482E12, 5.875], [1.7470626E12, 6.125], [1.74706422E12, 6.875], [1.74706314E12, 6.0], [1.74706536E12, 6.375], [1.74706254E12, 6.0], [1.74706476E12, 6.375000000000001], [1.74706272E12, 6.111111111111111], [1.74706434E12, 6.333333333333333], [1.74706212E12, 6.875], [1.74706374E12, 7.5], [1.74706296E12, 7.125], [1.74706458E12, 6.5], [1.74706236E12, 5.777777777777778], [1.74706398E12, 6.444444444444445], [1.74706194E12, 15.124999999999998], [1.74706416E12, 6.888888888888889], [1.74706356E12, 6.375], [1.74706518E12, 6.0], [1.74706248E12, 6.0], [1.7470641E12, 5.875], [1.74706188E12, 5.875], [1.7470635E12, 7.125], [1.74706368E12, 6.0], [1.7470653E12, 6.625], [1.74706308E12, 6.555555555555555], [1.7470647E12, 6.333333333333332], [1.74706392E12, 7.374999999999999], [1.74706332E12, 6.125], [1.74706494E12, 8.0], [1.7470629E12, 5.555555555555555], [1.74706512E12, 6.625], [1.7470623E12, 6.875000000000001], [1.74706452E12, 5.777777777777778]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74706344E12, 19.28571428571429], [1.74706506E12, 18.333333333333336], [1.74706284E12, 21.700000000000003], [1.74706446E12, 18.125], [1.74706242E12, 22.875], [1.74706464E12, 19.8], [1.74706566E12, 17.5], [1.74706404E12, 18.22222222222222], [1.74706266E12, 20.545454545454547], [1.74706488E12, 19.2], [1.74706206E12, 22.0], [1.74706428E12, 19.28571428571429], [1.74706224E12, 22.5], [1.74706386E12, 18.923076923076923], [1.74706326E12, 21.11111111111111], [1.74706548E12, 20.000000000000004], [1.74706218E12, 21.0], [1.7470644E12, 17.285714285714285], [1.7470638E12, 22.857142857142858], [1.74706542E12, 30.285714285714285], [1.74706338E12, 19.42857142857143], [1.7470656E12, 18.5], [1.747065E12, 18.75], [1.74706278E12, 22.25], [1.747062E12, 23.666666666666668], [1.74706362E12, 22.5], [1.74706302E12, 21.285714285714285], [1.74706524E12, 20.166666666666664], [1.7470632E12, 21.0], [1.74706482E12, 18.6], [1.7470626E12, 20.416666666666664], [1.74706422E12, 18.125], [1.74706314E12, 20.77777777777778], [1.74706536E12, 18.461538461538463], [1.74706254E12, 20.714285714285715], [1.74706476E12, 19.8], [1.74706272E12, 20.0], [1.74706434E12, 18.9], [1.74706212E12, 23.81818181818182], [1.74706374E12, 20.333333333333332], [1.74706296E12, 19.77777777777778], [1.74706458E12, 17.42857142857143], [1.74706236E12, 19.9], [1.74706398E12, 20.333333333333332], [1.74706416E12, 19.375], [1.74706356E12, 19.2], [1.74706518E12, 18.125], [1.74706248E12, 21.333333333333336], [1.7470641E12, 21.214285714285715], [1.7470635E12, 21.833333333333336], [1.74706572E12, 20.0], [1.74706368E12, 20.833333333333332], [1.7470653E12, 17.75], [1.74706308E12, 19.75], [1.7470647E12, 20.11111111111111], [1.74706392E12, 18.0], [1.74706554E12, 17.2], [1.74706332E12, 18.5], [1.74706494E12, 17.7], [1.7470629E12, 20.375], [1.74706512E12, 18.454545454545457], [1.7470623E12, 21.000000000000004], [1.74706452E12, 18.66666666666667]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74706344E12, 4.333333333333333], [1.74706506E12, 4.222222222222223], [1.74706284E12, 4.0], [1.74706446E12, 3.25], [1.74706242E12, 3.375], [1.74706464E12, 3.5], [1.74706404E12, 4.500000000000001], [1.74706182E12, 5.625], [1.74706266E12, 3.6249999999999996], [1.74706488E12, 3.8888888888888893], [1.74706206E12, 4.0], [1.74706428E12, 4.125], [1.74706224E12, 4.0], [1.74706386E12, 3.625], [1.74706326E12, 4.444444444444445], [1.74706218E12, 4.444444444444445], [1.7470644E12, 4.0], [1.7470638E12, 4.444444444444445], [1.74706542E12, 4.0], [1.74706338E12, 3.875], [1.747065E12, 4.125], [1.74706278E12, 4.125], [1.747062E12, 4.777777777777779], [1.74706362E12, 4.0], [1.74706302E12, 4.125], [1.74706524E12, 3.2222222222222223], [1.7470632E12, 4.5], [1.74706482E12, 3.625], [1.7470626E12, 3.375], [1.74706422E12, 4.375], [1.74706314E12, 3.375], [1.74706536E12, 4.125], [1.74706254E12, 4.222222222222222], [1.74706476E12, 4.375], [1.74706272E12, 3.888888888888889], [1.74706434E12, 4.0], [1.74706212E12, 3.625], [1.74706374E12, 4.0], [1.74706296E12, 3.75], [1.74706458E12, 4.0], [1.74706236E12, 3.7777777777777777], [1.74706398E12, 4.222222222222222], [1.74706194E12, 3.375], [1.74706416E12, 3.6666666666666665], [1.74706356E12, 4.5], [1.74706518E12, 2.875], [1.74706248E12, 3.3750000000000004], [1.7470641E12, 3.875], [1.74706188E12, 4.125], [1.7470635E12, 3.625], [1.74706368E12, 4.0], [1.7470653E12, 3.25], [1.74706308E12, 4.111111111111111], [1.7470647E12, 4.444444444444445], [1.74706392E12, 3.375], [1.74706332E12, 3.875], [1.74706494E12, 4.0], [1.7470629E12, 4.777777777777779], [1.74706512E12, 3.375], [1.7470623E12, 4.25], [1.74706452E12, 4.222222222222222]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74706344E12, 4.111111111111111], [1.74706506E12, 3.5555555555555554], [1.74706284E12, 3.6249999999999996], [1.74706446E12, 3.75], [1.74706242E12, 3.7499999999999996], [1.74706464E12, 3.2500000000000004], [1.74706404E12, 3.875], [1.74706182E12, 4.5], [1.74706266E12, 3.75], [1.74706488E12, 3.5555555555555554], [1.74706206E12, 3.5], [1.74706428E12, 3.9999999999999996], [1.74706224E12, 4.25], [1.74706386E12, 4.125], [1.74706326E12, 3.888888888888889], [1.74706218E12, 4.444444444444445], [1.7470644E12, 4.0], [1.7470638E12, 3.4444444444444446], [1.74706542E12, 3.0], [1.74706338E12, 3.6249999999999996], [1.747065E12, 3.0], [1.74706278E12, 3.625], [1.747062E12, 4.444444444444444], [1.74706362E12, 3.888888888888889], [1.74706302E12, 3.2500000000000004], [1.74706524E12, 3.666666666666666], [1.7470632E12, 3.7499999999999996], [1.74706482E12, 3.2500000000000004], [1.7470626E12, 3.7499999999999996], [1.74706422E12, 3.625], [1.74706314E12, 3.2500000000000004], [1.74706536E12, 3.75], [1.74706254E12, 3.5555555555555554], [1.74706476E12, 3.875], [1.74706272E12, 3.3333333333333335], [1.74706434E12, 3.888888888888889], [1.74706212E12, 3.5000000000000004], [1.74706374E12, 3.375], [1.74706296E12, 3.5], [1.74706458E12, 3.875], [1.74706236E12, 3.8888888888888884], [1.74706398E12, 3.6666666666666665], [1.74706194E12, 3.5], [1.74706416E12, 3.4444444444444446], [1.74706356E12, 3.75], [1.74706518E12, 3.875], [1.74706248E12, 3.625], [1.7470641E12, 4.25], [1.74706188E12, 3.625], [1.7470635E12, 3.25], [1.74706368E12, 3.5], [1.7470653E12, 3.625], [1.74706308E12, 3.7777777777777777], [1.7470647E12, 3.4444444444444446], [1.74706392E12, 3.5], [1.74706332E12, 3.5], [1.74706494E12, 3.5000000000000004], [1.7470629E12, 3.7777777777777777], [1.74706512E12, 3.625], [1.7470623E12, 4.25], [1.74706452E12, 3.4444444444444446]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74706344E12, 3.3333333333333335], [1.74706506E12, 3.2222222222222223], [1.74706284E12, 3.75], [1.74706446E12, 3.5], [1.74706242E12, 3.625], [1.74706464E12, 3.25], [1.74706404E12, 3.125], [1.74706182E12, 5.250000000000001], [1.74706266E12, 3.625], [1.74706488E12, 3.2222222222222228], [1.74706206E12, 4.125], [1.74706428E12, 4.0], [1.74706224E12, 4.0], [1.74706386E12, 3.5], [1.74706326E12, 3.5555555555555554], [1.74706218E12, 4.111111111111111], [1.7470644E12, 3.1250000000000004], [1.7470638E12, 3.1111111111111116], [1.74706542E12, 3.0], [1.74706338E12, 3.25], [1.747065E12, 3.625], [1.74706278E12, 3.375], [1.747062E12, 3.4444444444444446], [1.74706362E12, 3.6666666666666665], [1.74706302E12, 3.2500000000000004], [1.74706524E12, 3.1111111111111116], [1.7470632E12, 4.0], [1.74706482E12, 3.1250000000000004], [1.7470626E12, 3.75], [1.74706422E12, 3.125], [1.74706314E12, 3.875], [1.74706536E12, 2.875], [1.74706254E12, 3.3333333333333335], [1.74706476E12, 3.375], [1.74706272E12, 3.3333333333333335], [1.74706434E12, 4.222222222222223], [1.74706212E12, 3.625], [1.74706374E12, 3.6250000000000004], [1.74706296E12, 3.625], [1.74706458E12, 4.0], [1.74706236E12, 3.3333333333333335], [1.74706398E12, 3.4444444444444446], [1.74706194E12, 4.375000000000001], [1.74706416E12, 3.333333333333334], [1.74706356E12, 4.125], [1.74706518E12, 3.25], [1.74706248E12, 3.75], [1.7470641E12, 4.75], [1.74706188E12, 5.0], [1.7470635E12, 3.1250000000000004], [1.74706368E12, 3.125], [1.7470653E12, 3.625], [1.74706308E12, 4.0], [1.7470647E12, 3.5555555555555554], [1.74706392E12, 3.75], [1.74706332E12, 2.875], [1.74706494E12, 3.125], [1.7470629E12, 4.222222222222222], [1.74706512E12, 4.25], [1.7470623E12, 3.75], [1.74706452E12, 3.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74706344E12, 3.0], [1.74706506E12, 2.7777777777777777], [1.74706284E12, 3.1250000000000004], [1.74706446E12, 2.25], [1.74706242E12, 2.625], [1.74706464E12, 2.625], [1.74706404E12, 2.7499999999999996], [1.74706182E12, 3.625], [1.74706266E12, 2.5], [1.74706488E12, 2.4444444444444446], [1.74706206E12, 2.75], [1.74706428E12, 3.1250000000000004], [1.74706224E12, 3.5], [1.74706386E12, 2.5], [1.74706326E12, 3.333333333333333], [1.74706218E12, 4.111111111111111], [1.7470644E12, 2.25], [1.7470638E12, 2.7777777777777772], [1.74706542E12, 3.0], [1.74706338E12, 2.5], [1.747065E12, 2.5], [1.74706278E12, 2.875], [1.747062E12, 3.4444444444444446], [1.74706362E12, 3.4444444444444446], [1.74706302E12, 3.1250000000000004], [1.74706524E12, 2.6666666666666665], [1.7470632E12, 3.25], [1.74706482E12, 2.5], [1.7470626E12, 2.8750000000000004], [1.74706422E12, 2.75], [1.74706314E12, 2.875], [1.74706536E12, 3.125], [1.74706254E12, 2.5555555555555554], [1.74706476E12, 2.75], [1.74706272E12, 2.4444444444444446], [1.74706434E12, 2.7777777777777777], [1.74706212E12, 3.0000000000000004], [1.74706374E12, 2.6249999999999996], [1.74706296E12, 3.125], [1.74706458E12, 2.5], [1.74706236E12, 3.7777777777777777], [1.74706398E12, 3.222222222222222], [1.74706194E12, 3.625], [1.74706416E12, 2.7777777777777777], [1.74706356E12, 3.125], [1.74706518E12, 2.875], [1.74706248E12, 2.625], [1.7470641E12, 3.125], [1.74706188E12, 3.6249999999999996], [1.7470635E12, 2.625], [1.74706368E12, 2.8749999999999996], [1.7470653E12, 2.8749999999999996], [1.74706308E12, 2.777777777777778], [1.7470647E12, 2.666666666666666], [1.74706392E12, 2.625], [1.74706332E12, 2.75], [1.74706494E12, 2.2500000000000004], [1.7470629E12, 2.6666666666666665], [1.74706512E12, 2.2500000000000004], [1.7470623E12, 3.25], [1.74706452E12, 3.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74706344E12, 38.625], [1.74706506E12, 40.05555555555556], [1.74706284E12, 40.315789473684205], [1.74706446E12, 39.166666666666664], [1.74706242E12, 40.16666666666667], [1.74706464E12, 40.68749999999999], [1.74706566E12, 39.36363636363637], [1.74706404E12, 38.6], [1.74706266E12, 40.41176470588236], [1.74706488E12, 39.0], [1.74706206E12, 43.714285714285715], [1.74706428E12, 43.06666666666667], [1.74706224E12, 42.94444444444445], [1.74706386E12, 39.58823529411764], [1.74706326E12, 40.1764705882353], [1.74706548E12, 44.16666666666667], [1.74706218E12, 42.473684210526315], [1.7470644E12, 38.50000000000001], [1.7470638E12, 38.07692307692308], [1.74706542E12, 48.06249999999999], [1.74706338E12, 39.375], [1.7470656E12, 37.666666666666664], [1.747065E12, 38.0], [1.74706278E12, 39.27272727272727], [1.747062E12, 43.0], [1.74706362E12, 39.36363636363635], [1.74706302E12, 40.0], [1.74706524E12, 40.18750000000001], [1.7470632E12, 41.642857142857146], [1.74706482E12, 39.88235294117647], [1.7470626E12, 41.1875], [1.74706422E12, 42.93333333333332], [1.74706314E12, 39.29999999999999], [1.74706536E12, 41.6], [1.74706254E12, 40.74999999999999], [1.74706476E12, 41.5], [1.74706272E12, 40.88888888888889], [1.74706434E12, 38.64285714285714], [1.74706212E12, 43.7], [1.74706374E12, 38.285714285714285], [1.74706296E12, 40.07142857142857], [1.74706458E12, 39.466666666666654], [1.74706236E12, 42.333333333333336], [1.74706398E12, 39.5625], [1.74706578E12, 44.666666666666664], [1.74706416E12, 39.18181818181818], [1.74706356E12, 38.04545454545455], [1.74706518E12, 38.42105263157894], [1.74706248E12, 39.6], [1.7470641E12, 39.99999999999999], [1.7470635E12, 38.666666666666664], [1.74706572E12, 38.5], [1.74706368E12, 39.18750000000001], [1.7470653E12, 38.4], [1.74706308E12, 44.78947368421053], [1.7470647E12, 40.49999999999999], [1.74706392E12, 39.10526315789475], [1.74706554E12, 39.33333333333333], [1.74706332E12, 40.55555555555556], [1.74706494E12, 38.266666666666666], [1.7470629E12, 39.13333333333333], [1.74706512E12, 37.90909090909092], [1.7470623E12, 41.111111111111114], [1.74706452E12, 40.23529411764706]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74706344E12, 2.3333333333333335], [1.74706506E12, 3.3333333333333335], [1.74706284E12, 3.75], [1.74706446E12, 3.375], [1.74706242E12, 4.375], [1.74706464E12, 3.5], [1.74706404E12, 2.875], [1.74706182E12, 3.25], [1.74706266E12, 3.6249999999999996], [1.74706488E12, 2.9999999999999996], [1.74706206E12, 3.75], [1.74706428E12, 4.25], [1.74706224E12, 5.374999999999999], [1.74706386E12, 3.0], [1.74706326E12, 3.6666666666666665], [1.74706218E12, 4.333333333333333], [1.7470644E12, 2.75], [1.7470638E12, 2.888888888888889], [1.74706542E12, 3.0], [1.74706338E12, 3.25], [1.747065E12, 3.5000000000000004], [1.74706278E12, 3.5], [1.747062E12, 3.2222222222222223], [1.74706362E12, 3.3333333333333335], [1.74706302E12, 3.375], [1.74706524E12, 3.111111111111111], [1.7470632E12, 3.125], [1.74706482E12, 3.0], [1.7470626E12, 3.6250000000000004], [1.74706422E12, 3.75], [1.74706314E12, 3.875], [1.74706536E12, 2.7499999999999996], [1.74706254E12, 3.6666666666666665], [1.74706476E12, 3.0000000000000004], [1.74706272E12, 3.4444444444444446], [1.74706434E12, 4.0], [1.74706212E12, 3.6249999999999996], [1.74706374E12, 2.7499999999999996], [1.74706296E12, 4.75], [1.74706458E12, 3.25], [1.74706236E12, 3.6666666666666665], [1.74706398E12, 3.2222222222222223], [1.74706194E12, 4.0], [1.74706416E12, 3.4444444444444446], [1.74706356E12, 4.125], [1.74706518E12, 3.375], [1.74706248E12, 4.125], [1.7470641E12, 3.1250000000000004], [1.74706188E12, 4.125], [1.7470635E12, 3.875], [1.74706368E12, 2.7499999999999996], [1.7470653E12, 3.125], [1.74706308E12, 3.2222222222222228], [1.7470647E12, 3.2222222222222223], [1.74706392E12, 3.0], [1.74706332E12, 3.0], [1.74706494E12, 3.0], [1.7470629E12, 3.1111111111111116], [1.74706512E12, 3.75], [1.7470623E12, 3.25], [1.74706452E12, 3.6666666666666665]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74706344E12, 4.629629629629629], [1.74706506E12, 5.384615384615387], [1.74706284E12, 4.821428571428571], [1.74706446E12, 5.115384615384617], [1.74706242E12, 5.375000000000001], [1.74706464E12, 5.521739130434783], [1.74706404E12, 4.84], [1.74706182E12, 6.384615384615384], [1.74706266E12, 4.862068965517242], [1.74706488E12, 5.36], [1.74706206E12, 5.52], [1.74706428E12, 5.120000000000001], [1.74706224E12, 4.833333333333334], [1.74706386E12, 5.259259259259259], [1.74706326E12, 4.785714285714285], [1.74706218E12, 5.333333333333337], [1.7470644E12, 5.249999999999999], [1.7470638E12, 5.846153846153847], [1.74706542E12, 5.307692307692308], [1.74706338E12, 5.0], [1.747065E12, 5.279999999999999], [1.74706278E12, 5.384615384615386], [1.747062E12, 5.608695652173913], [1.74706362E12, 5.08], [1.74706302E12, 5.04], [1.74706524E12, 5.076923076923076], [1.7470632E12, 4.909090909090907], [1.74706482E12, 5.4642857142857135], [1.7470626E12, 5.565217391304349], [1.74706422E12, 5.200000000000001], [1.74706314E12, 4.923076923076923], [1.74706536E12, 4.653846153846154], [1.74706254E12, 5.458333333333334], [1.74706476E12, 4.916666666666666], [1.74706272E12, 5.200000000000001], [1.74706434E12, 5.166666666666666], [1.74706212E12, 5.875000000000002], [1.74706374E12, 5.272727272727273], [1.74706296E12, 5.086956521739131], [1.74706458E12, 5.370370370370369], [1.74706236E12, 5.000000000000002], [1.74706398E12, 5.333333333333333], [1.74706194E12, 5.3103448275862055], [1.74706416E12, 5.3076923076923075], [1.74706356E12, 4.961538461538463], [1.74706518E12, 5.0769230769230775], [1.74706248E12, 5.2592592592592595], [1.7470641E12, 4.846153846153845], [1.74706188E12, 5.238095238095237], [1.7470635E12, 5.217391304347826], [1.74706368E12, 4.962962962962964], [1.7470653E12, 5.083333333333334], [1.74706308E12, 4.846153846153847], [1.7470647E12, 4.956521739130435], [1.74706392E12, 4.826086956521739], [1.74706332E12, 5.249999999999999], [1.74706494E12, 5.208333333333332], [1.7470629E12, 4.625], [1.74706512E12, 5.608695652173913], [1.7470623E12, 6.3999999999999995], [1.74706452E12, 4.680000000000001]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74706344E12, 39.33333333333333], [1.74706506E12, 40.44444444444444], [1.74706284E12, 44.25], [1.74706446E12, 40.0], [1.74706242E12, 42.625], [1.74706464E12, 40.375], [1.74706404E12, 39.25], [1.74706182E12, 43.0], [1.74706266E12, 39.75], [1.74706488E12, 40.22222222222222], [1.74706206E12, 44.375], [1.74706428E12, 41.0], [1.74706224E12, 41.37499999999999], [1.74706386E12, 40.37499999999999], [1.74706326E12, 44.888888888888886], [1.74706218E12, 40.99999999999999], [1.7470644E12, 41.0], [1.7470638E12, 44.0], [1.74706542E12, 43.0], [1.74706338E12, 41.25], [1.747065E12, 39.125], [1.74706278E12, 41.5], [1.747062E12, 43.77777777777778], [1.74706362E12, 40.77777777777778], [1.74706302E12, 40.0], [1.74706524E12, 40.333333333333336], [1.7470632E12, 41.875], [1.74706482E12, 43.375], [1.7470626E12, 44.25], [1.74706422E12, 40.75], [1.74706314E12, 41.875], [1.74706536E12, 44.375], [1.74706254E12, 43.22222222222222], [1.74706476E12, 40.125], [1.74706272E12, 39.55555555555556], [1.74706434E12, 43.44444444444444], [1.74706212E12, 40.625], [1.74706374E12, 39.875], [1.74706296E12, 42.625], [1.74706458E12, 40.375], [1.74706236E12, 42.44444444444444], [1.74706398E12, 38.77777777777778], [1.74706194E12, 40.87500000000001], [1.74706416E12, 40.77777777777778], [1.74706356E12, 43.875], [1.74706518E12, 39.25000000000001], [1.74706248E12, 40.375], [1.7470641E12, 41.375], [1.74706188E12, 43.12499999999999], [1.7470635E12, 43.625], [1.74706368E12, 38.875], [1.7470653E12, 41.875], [1.74706308E12, 43.22222222222222], [1.7470647E12, 42.111111111111114], [1.74706392E12, 42.0], [1.74706332E12, 39.875], [1.74706494E12, 40.75], [1.7470629E12, 40.666666666666664], [1.74706512E12, 39.625], [1.7470623E12, 42.12500000000001], [1.74706452E12, 39.55555555555556]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74706344E12, 3.0857142857142854], [1.74706506E12, 2.888888888888889], [1.74706284E12, 3.6799999999999993], [1.74706446E12, 2.9000000000000004], [1.74706242E12, 3.724999999999999], [1.74706464E12, 3.14], [1.74706566E12, 2.9000000000000004], [1.74706404E12, 2.888888888888889], [1.74706266E12, 3.2909090909090915], [1.74706488E12, 3.04], [1.74706206E12, 3.48], [1.74706428E12, 3.085714285714286], [1.74706224E12, 3.6749999999999994], [1.74706386E12, 2.9999999999999996], [1.74706326E12, 3.533333333333334], [1.74706548E12, 3.225], [1.74706218E12, 3.428571428571429], [1.7470644E12, 2.7428571428571424], [1.7470638E12, 3.657142857142857], [1.74706542E12, 4.857142857142857], [1.74706338E12, 3.057142857142857], [1.7470656E12, 2.95], [1.747065E12, 3.025], [1.74706278E12, 3.6999999999999997], [1.747062E12, 3.7333333333333334], [1.74706362E12, 3.6249999999999996], [1.74706302E12, 3.4], [1.74706524E12, 3.2333333333333334], [1.7470632E12, 3.2727272727272725], [1.74706482E12, 3.0200000000000014], [1.7470626E12, 3.2666666666666675], [1.74706422E12, 2.9000000000000004], [1.74706314E12, 3.4], [1.74706536E12, 2.953846153846153], [1.74706254E12, 3.228571428571428], [1.74706476E12, 3.16], [1.74706272E12, 3.1799999999999993], [1.74706434E12, 3.0399999999999996], [1.74706212E12, 3.9272727272727264], [1.74706374E12, 3.266666666666666], [1.74706296E12, 3.133333333333333], [1.74706458E12, 2.7714285714285714], [1.74706236E12, 3.2600000000000002], [1.74706398E12, 3.1999999999999997], [1.74706416E12, 3.2], [1.74706356E12, 3.146666666666665], [1.74706518E12, 2.975], [1.74706248E12, 3.433333333333333], [1.7470641E12, 3.499999999999999], [1.7470635E12, 3.5], [1.74706572E12, 3.3000000000000003], [1.74706368E12, 3.366666666666667], [1.7470653E12, 2.8500000000000005], [1.74706308E12, 3.2], [1.7470647E12, 3.1999999999999997], [1.74706392E12, 2.9333333333333336], [1.74706554E12, 2.72], [1.74706332E12, 3.0], [1.74706494E12, 2.8], [1.7470629E12, 3.1999999999999997], [1.74706512E12, 2.963636363636363], [1.7470623E12, 3.514285714285715], [1.74706452E12, 2.8666666666666667]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74706578E12, "title": "Response Time Over Time"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average response time was %y ms"
                }
            };
        },
        createGraph: function() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Times Over Time
function refreshResponseTimeOverTime(fixTimestamps) {
    var infos = responseTimesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyResponseTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimesOverTime"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimesOverTime", "#overviewResponseTimesOverTime");
        $('#footerResponseTimesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var latenciesOverTimeInfos = {
        data: {"result": {"minY": 1.0, "minX": 1.74706182E12, "maxY": 30.285714285714285, "series": [{"data": [[1.74706344E12, 3.857142857142857], [1.74706506E12, 3.888888888888889], [1.74706284E12, 3.3000000000000003], [1.74706446E12, 3.625], [1.74706242E12, 4.25], [1.74706464E12, 4.1], [1.74706566E12, 3.0], [1.74706404E12, 3.7777777777777777], [1.74706266E12, 4.09090909090909], [1.74706488E12, 4.0], [1.74706206E12, 4.6], [1.74706428E12, 3.857142857142857], [1.74706224E12, 4.125], [1.74706386E12, 3.9230769230769225], [1.74706326E12, 3.4444444444444446], [1.74706548E12, 3.8749999999999996], [1.74706218E12, 3.714285714285714], [1.7470644E12, 3.5714285714285716], [1.7470638E12, 4.571428571428571], [1.74706542E12, 6.0], [1.74706338E12, 4.142857142857143], [1.7470656E12, 3.75], [1.747065E12, 3.6249999999999996], [1.74706278E12, 3.75], [1.747062E12, 4.666666666666667], [1.74706362E12, 4.374999999999999], [1.74706302E12, 4.0], [1.74706524E12, 4.0], [1.7470632E12, 4.636363636363637], [1.74706482E12, 3.5], [1.7470626E12, 4.083333333333333], [1.74706422E12, 3.6249999999999996], [1.74706314E12, 3.7777777777777772], [1.74706536E12, 3.692307692307692], [1.74706254E12, 4.571428571428571], [1.74706476E12, 4.0], [1.74706272E12, 3.9999999999999996], [1.74706434E12, 3.6999999999999997], [1.74706212E12, 4.181818181818182], [1.74706374E12, 4.0], [1.74706296E12, 4.111111111111111], [1.74706458E12, 3.571428571428571], [1.74706236E12, 3.5999999999999996], [1.74706398E12, 4.333333333333333], [1.74706416E12, 3.375], [1.74706356E12, 3.466666666666667], [1.74706518E12, 3.25], [1.74706248E12, 4.166666666666667], [1.7470641E12, 3.714285714285714], [1.7470635E12, 4.333333333333334], [1.74706572E12, 3.5], [1.74706368E12, 4.0], [1.7470653E12, 3.5], [1.74706308E12, 3.7499999999999996], [1.7470647E12, 4.111111111111111], [1.74706392E12, 3.3333333333333335], [1.74706554E12, 3.6], [1.74706332E12, 3.5], [1.74706494E12, 3.6999999999999993], [1.7470629E12, 4.375], [1.74706512E12, 3.6363636363636362], [1.7470623E12, 3.4285714285714284], [1.74706452E12, 4.333333333333333]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74706344E12, 16.66666666666667], [1.74706506E12, 16.777777777777782], [1.74706284E12, 18.0], [1.74706446E12, 15.875], [1.74706242E12, 17.75], [1.74706464E12, 16.875], [1.74706404E12, 16.5], [1.74706182E12, 26.0], [1.74706266E12, 16.499999999999996], [1.74706488E12, 17.11111111111111], [1.74706206E12, 20.249999999999996], [1.74706428E12, 17.75], [1.74706224E12, 20.25], [1.74706386E12, 17.0], [1.74706326E12, 17.88888888888889], [1.74706218E12, 20.333333333333336], [1.7470644E12, 17.25], [1.7470638E12, 17.555555555555557], [1.74706542E12, 19.0], [1.74706338E12, 16.375], [1.747065E12, 15.5], [1.74706278E12, 17.75], [1.747062E12, 22.444444444444443], [1.74706362E12, 16.333333333333332], [1.74706302E12, 16.875], [1.74706524E12, 15.11111111111111], [1.7470632E12, 17.5], [1.74706482E12, 15.75], [1.7470626E12, 17.5], [1.74706422E12, 17.0], [1.74706314E12, 16.375], [1.74706536E12, 17.0], [1.74706254E12, 17.333333333333332], [1.74706476E12, 16.625], [1.74706272E12, 16.333333333333332], [1.74706434E12, 18.111111111111114], [1.74706212E12, 18.5], [1.74706374E12, 17.25], [1.74706296E12, 18.999999999999996], [1.74706458E12, 17.75], [1.74706236E12, 18.222222222222218], [1.74706398E12, 17.22222222222222], [1.74706194E12, 28.999999999999996], [1.74706416E12, 16.77777777777778], [1.74706356E12, 18.25], [1.74706518E12, 15.499999999999998], [1.74706248E12, 16.875], [1.7470641E12, 17.000000000000004], [1.74706188E12, 19.375], [1.7470635E12, 17.499999999999996], [1.74706368E12, 17.0], [1.7470653E12, 16.875], [1.74706308E12, 17.444444444444443], [1.7470647E12, 17.666666666666668], [1.74706392E12, 17.5], [1.74706332E12, 16.125], [1.74706494E12, 18.0], [1.7470629E12, 17.11111111111111], [1.74706512E12, 16.5], [1.7470623E12, 19.75], [1.74706452E12, 16.333333333333332]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74706344E12, 3.5500000000000003], [1.74706506E12, 3.0689655172413794], [1.74706284E12, 3.0000000000000004], [1.74706446E12, 3.08], [1.74706242E12, 3.4230769230769234], [1.74706464E12, 3.272727272727273], [1.74706404E12, 3.25], [1.74706182E12, 8.0], [1.74706266E12, 3.5185185185185186], [1.74706488E12, 2.9333333333333336], [1.74706206E12, 3.6666666666666665], [1.74706428E12, 3.0434782608695654], [1.74706224E12, 3.5500000000000003], [1.74706386E12, 3.368421052631579], [1.74706326E12, 2.8636363636363638], [1.74706548E12, 3.0], [1.74706218E12, 3.454545454545455], [1.7470644E12, 3.185185185185185], [1.7470638E12, 3.1875000000000004], [1.74706542E12, 3.75], [1.74706338E12, 3.1904761904761907], [1.7470656E12, 3.3333333333333335], [1.747065E12, 3.2173913043478257], [1.74706278E12, 3.1739130434782608], [1.747062E12, 3.619047619047619], [1.74706362E12, 2.904761904761905], [1.74706302E12, 3.2592592592592586], [1.74706524E12, 2.999999999999999], [1.7470632E12, 3.24], [1.74706482E12, 2.954545454545455], [1.7470626E12, 3.275862068965517], [1.74706422E12, 3.3846153846153846], [1.74706314E12, 3.428571428571429], [1.74706536E12, 3.249999999999999], [1.74706254E12, 3.4210526315789473], [1.74706476E12, 3.416666666666667], [1.74706272E12, 3.3600000000000003], [1.74706434E12, 3.1999999999999993], [1.74706212E12, 3.6363636363636362], [1.74706374E12, 3.0400000000000005], [1.74706296E12, 3.0000000000000004], [1.74706458E12, 3.25], [1.74706236E12, 3.5217391304347827], [1.74706398E12, 3.307692307692308], [1.74706194E12, 6.588235294117647], [1.74706416E12, 3.16], [1.74706356E12, 3.44], [1.74706518E12, 3.2173913043478253], [1.74706248E12, 3.5666666666666664], [1.7470641E12, 3.2857142857142856], [1.74706188E12, 7.0], [1.7470635E12, 3.3823529411764715], [1.74706368E12, 3.0000000000000004], [1.7470653E12, 2.999999999999999], [1.74706308E12, 3.3666666666666667], [1.7470647E12, 3.3333333333333335], [1.74706392E12, 3.2272727272727275], [1.74706554E12, 3.6], [1.74706332E12, 3.333333333333333], [1.74706494E12, 2.9999999999999996], [1.7470629E12, 3.1764705882352944], [1.74706512E12, 3.1739130434782608], [1.7470623E12, 3.9166666666666665], [1.74706452E12, 3.521739130434783]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74706344E12, 5.888888888888888], [1.74706506E12, 6.444444444444445], [1.74706284E12, 6.0], [1.74706446E12, 6.250000000000001], [1.74706242E12, 6.75], [1.74706464E12, 6.375000000000001], [1.74706404E12, 6.125], [1.74706182E12, 10.5], [1.74706266E12, 6.375], [1.74706488E12, 6.0], [1.74706206E12, 7.500000000000001], [1.74706428E12, 6.25], [1.74706224E12, 6.125], [1.74706386E12, 6.625], [1.74706326E12, 6.222222222222222], [1.74706218E12, 6.333333333333333], [1.7470644E12, 6.625], [1.7470638E12, 7.000000000000001], [1.74706542E12, 10.0], [1.74706338E12, 6.5], [1.747065E12, 6.374999999999999], [1.74706278E12, 5.875], [1.747062E12, 7.333333333333333], [1.74706362E12, 5.0], [1.74706302E12, 6.0], [1.74706524E12, 6.333333333333334], [1.7470632E12, 6.125], [1.74706482E12, 5.875], [1.7470626E12, 6.125], [1.74706422E12, 6.875], [1.74706314E12, 6.0], [1.74706536E12, 6.375], [1.74706254E12, 6.0], [1.74706476E12, 6.375000000000001], [1.74706272E12, 6.0], [1.74706434E12, 6.333333333333333], [1.74706212E12, 6.875], [1.74706374E12, 7.5], [1.74706296E12, 7.125], [1.74706458E12, 6.5], [1.74706236E12, 5.777777777777778], [1.74706398E12, 6.444444444444445], [1.74706194E12, 15.124999999999998], [1.74706416E12, 6.888888888888889], [1.74706356E12, 6.375], [1.74706518E12, 6.0], [1.74706248E12, 6.0], [1.7470641E12, 5.875], [1.74706188E12, 5.75], [1.7470635E12, 7.125], [1.74706368E12, 6.0], [1.7470653E12, 6.625], [1.74706308E12, 6.555555555555555], [1.7470647E12, 6.333333333333332], [1.74706392E12, 7.374999999999999], [1.74706332E12, 6.125], [1.74706494E12, 8.0], [1.7470629E12, 5.555555555555555], [1.74706512E12, 6.625], [1.7470623E12, 6.875000000000001], [1.74706452E12, 5.777777777777778]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74706344E12, 19.28571428571429], [1.74706506E12, 18.333333333333336], [1.74706284E12, 21.700000000000003], [1.74706446E12, 18.0], [1.74706242E12, 22.875], [1.74706464E12, 19.8], [1.74706566E12, 17.5], [1.74706404E12, 18.22222222222222], [1.74706266E12, 20.454545454545453], [1.74706488E12, 19.2], [1.74706206E12, 21.8], [1.74706428E12, 19.28571428571429], [1.74706224E12, 22.375], [1.74706386E12, 18.923076923076923], [1.74706326E12, 21.11111111111111], [1.74706548E12, 19.875], [1.74706218E12, 20.857142857142858], [1.7470644E12, 17.285714285714285], [1.7470638E12, 22.857142857142858], [1.74706542E12, 30.285714285714285], [1.74706338E12, 19.42857142857143], [1.7470656E12, 18.5], [1.747065E12, 18.75], [1.74706278E12, 22.25], [1.747062E12, 23.333333333333332], [1.74706362E12, 22.5], [1.74706302E12, 20.857142857142858], [1.74706524E12, 20.166666666666664], [1.7470632E12, 21.0], [1.74706482E12, 18.6], [1.7470626E12, 20.416666666666664], [1.74706422E12, 18.125], [1.74706314E12, 20.666666666666668], [1.74706536E12, 18.461538461538463], [1.74706254E12, 20.714285714285715], [1.74706476E12, 19.8], [1.74706272E12, 19.900000000000002], [1.74706434E12, 18.9], [1.74706212E12, 23.81818181818182], [1.74706374E12, 20.333333333333332], [1.74706296E12, 19.666666666666668], [1.74706458E12, 17.42857142857143], [1.74706236E12, 19.8], [1.74706398E12, 20.333333333333332], [1.74706416E12, 19.375], [1.74706356E12, 19.2], [1.74706518E12, 18.125], [1.74706248E12, 21.166666666666668], [1.7470641E12, 21.214285714285715], [1.7470635E12, 21.833333333333336], [1.74706572E12, 20.0], [1.74706368E12, 20.833333333333332], [1.7470653E12, 17.75], [1.74706308E12, 19.75], [1.7470647E12, 20.11111111111111], [1.74706392E12, 18.0], [1.74706554E12, 17.2], [1.74706332E12, 18.166666666666664], [1.74706494E12, 17.7], [1.7470629E12, 20.375], [1.74706512E12, 18.454545454545457], [1.7470623E12, 21.000000000000004], [1.74706452E12, 18.66666666666667]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74706344E12, 2.0], [1.74706506E12, 2.2222222222222228], [1.74706284E12, 2.1250000000000004], [1.74706446E12, 1.25], [1.74706242E12, 1.625], [1.74706464E12, 1.375], [1.74706404E12, 1.875], [1.74706182E12, 2.625], [1.74706266E12, 1.625], [1.74706488E12, 2.111111111111111], [1.74706206E12, 2.0], [1.74706428E12, 1.75], [1.74706224E12, 2.0], [1.74706386E12, 1.75], [1.74706326E12, 1.8888888888888888], [1.74706218E12, 2.3333333333333335], [1.7470644E12, 1.625], [1.7470638E12, 2.1111111111111107], [1.74706542E12, 2.0], [1.74706338E12, 1.5], [1.747065E12, 1.375], [1.74706278E12, 2.25], [1.747062E12, 2.777777777777778], [1.74706362E12, 1.8888888888888888], [1.74706302E12, 2.0000000000000004], [1.74706524E12, 1.1111111111111112], [1.7470632E12, 1.75], [1.74706482E12, 1.75], [1.7470626E12, 1.375], [1.74706422E12, 1.25], [1.74706314E12, 1.375], [1.74706536E12, 2.0], [1.74706254E12, 2.111111111111111], [1.74706476E12, 2.0000000000000004], [1.74706272E12, 1.5555555555555556], [1.74706434E12, 1.7777777777777777], [1.74706212E12, 1.75], [1.74706374E12, 1.7499999999999998], [1.74706296E12, 1.625], [1.74706458E12, 2.0], [1.74706236E12, 1.8888888888888886], [1.74706398E12, 2.2222222222222223], [1.74706194E12, 1.75], [1.74706416E12, 1.3333333333333333], [1.74706356E12, 1.75], [1.74706518E12, 1.375], [1.74706248E12, 1.625], [1.7470641E12, 1.75], [1.74706188E12, 2.0], [1.7470635E12, 1.625], [1.74706368E12, 2.5], [1.7470653E12, 1.5], [1.74706308E12, 2.0], [1.7470647E12, 2.3333333333333335], [1.74706392E12, 1.5], [1.74706332E12, 1.625], [1.74706494E12, 2.0], [1.7470629E12, 2.4444444444444446], [1.74706512E12, 1.375], [1.7470623E12, 2.0], [1.74706452E12, 1.8888888888888888]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74706344E12, 2.2222222222222223], [1.74706506E12, 2.0], [1.74706284E12, 2.25], [1.74706446E12, 1.75], [1.74706242E12, 2.2500000000000004], [1.74706464E12, 2.1250000000000004], [1.74706404E12, 2.2500000000000004], [1.74706182E12, 2.8749999999999996], [1.74706266E12, 2.1250000000000004], [1.74706488E12, 2.5555555555555554], [1.74706206E12, 2.3750000000000004], [1.74706428E12, 2.25], [1.74706224E12, 2.6249999999999996], [1.74706386E12, 1.75], [1.74706326E12, 2.0], [1.74706218E12, 2.4444444444444446], [1.7470644E12, 2.5], [1.7470638E12, 2.3333333333333335], [1.74706542E12, 1.0], [1.74706338E12, 2.0], [1.747065E12, 1.375], [1.74706278E12, 2.5], [1.747062E12, 2.7777777777777772], [1.74706362E12, 2.1111111111111116], [1.74706302E12, 2.1250000000000004], [1.74706524E12, 1.7777777777777777], [1.7470632E12, 2.25], [1.74706482E12, 2.0], [1.7470626E12, 2.5], [1.74706422E12, 1.875], [1.74706314E12, 1.875], [1.74706536E12, 2.125], [1.74706254E12, 2.3333333333333335], [1.74706476E12, 2.375], [1.74706272E12, 2.1111111111111116], [1.74706434E12, 2.4444444444444446], [1.74706212E12, 2.2500000000000004], [1.74706374E12, 2.125], [1.74706296E12, 2.5], [1.74706458E12, 2.375], [1.74706236E12, 2.333333333333334], [1.74706398E12, 1.7777777777777777], [1.74706194E12, 2.375], [1.74706416E12, 2.111111111111111], [1.74706356E12, 2.0], [1.74706518E12, 1.875], [1.74706248E12, 2.1250000000000004], [1.7470641E12, 2.25], [1.74706188E12, 2.5], [1.7470635E12, 2.0], [1.74706368E12, 2.125], [1.7470653E12, 2.0], [1.74706308E12, 2.0], [1.7470647E12, 2.111111111111111], [1.74706392E12, 2.1250000000000004], [1.74706332E12, 2.0], [1.74706494E12, 1.875], [1.7470629E12, 2.3333333333333335], [1.74706512E12, 1.625], [1.7470623E12, 2.7499999999999996], [1.74706452E12, 2.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74706344E12, 1.3333333333333333], [1.74706506E12, 1.2222222222222223], [1.74706284E12, 1.75], [1.74706446E12, 1.75], [1.74706242E12, 1.5], [1.74706464E12, 1.875], [1.74706404E12, 1.375], [1.74706182E12, 2.5], [1.74706266E12, 1.5], [1.74706488E12, 1.5555555555555556], [1.74706206E12, 2.0], [1.74706428E12, 1.875], [1.74706224E12, 2.0], [1.74706386E12, 1.875], [1.74706326E12, 1.6666666666666667], [1.74706218E12, 2.0], [1.7470644E12, 1.625], [1.7470638E12, 1.2222222222222223], [1.74706542E12, 1.0], [1.74706338E12, 1.5], [1.747065E12, 1.75], [1.74706278E12, 1.5], [1.747062E12, 1.8888888888888888], [1.74706362E12, 1.8888888888888888], [1.74706302E12, 1.625], [1.74706524E12, 1.3333333333333333], [1.7470632E12, 1.75], [1.74706482E12, 1.375], [1.7470626E12, 1.75], [1.74706422E12, 1.5], [1.74706314E12, 1.625], [1.74706536E12, 1.5], [1.74706254E12, 1.2222222222222223], [1.74706476E12, 1.125], [1.74706272E12, 1.2222222222222223], [1.74706434E12, 1.6666666666666667], [1.74706212E12, 1.5], [1.74706374E12, 1.5], [1.74706296E12, 1.625], [1.74706458E12, 1.75], [1.74706236E12, 1.6666666666666667], [1.74706398E12, 1.4444444444444444], [1.74706194E12, 2.5000000000000004], [1.74706416E12, 1.6666666666666667], [1.74706356E12, 2.125], [1.74706518E12, 1.375], [1.74706248E12, 2.0], [1.7470641E12, 2.1250000000000004], [1.74706188E12, 2.2500000000000004], [1.7470635E12, 1.5], [1.74706368E12, 1.375], [1.7470653E12, 1.625], [1.74706308E12, 1.6666666666666667], [1.7470647E12, 1.5555555555555556], [1.74706392E12, 1.75], [1.74706332E12, 1.1250000000000002], [1.74706494E12, 1.375], [1.7470629E12, 1.8888888888888888], [1.74706512E12, 1.875], [1.7470623E12, 2.0], [1.74706452E12, 1.3333333333333333]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74706344E12, 2.0], [1.74706506E12, 1.5555555555555556], [1.74706284E12, 2.0], [1.74706446E12, 1.5], [1.74706242E12, 1.375], [1.74706464E12, 1.5], [1.74706404E12, 1.75], [1.74706182E12, 2.625], [1.74706266E12, 1.5], [1.74706488E12, 1.4444444444444444], [1.74706206E12, 1.875], [1.74706428E12, 1.7499999999999998], [1.74706224E12, 2.5], [1.74706386E12, 1.5], [1.74706326E12, 1.8888888888888893], [1.74706218E12, 2.888888888888889], [1.7470644E12, 1.25], [1.7470638E12, 1.5555555555555556], [1.74706542E12, 1.0], [1.74706338E12, 1.5], [1.747065E12, 1.375], [1.74706278E12, 1.8749999999999998], [1.747062E12, 2.3333333333333335], [1.74706362E12, 2.111111111111111], [1.74706302E12, 1.875], [1.74706524E12, 1.5555555555555556], [1.7470632E12, 1.9999999999999998], [1.74706482E12, 1.5], [1.7470626E12, 1.625], [1.74706422E12, 1.75], [1.74706314E12, 1.5], [1.74706536E12, 1.75], [1.74706254E12, 1.4444444444444444], [1.74706476E12, 1.75], [1.74706272E12, 1.4444444444444444], [1.74706434E12, 1.5555555555555556], [1.74706212E12, 1.875], [1.74706374E12, 1.375], [1.74706296E12, 1.625], [1.74706458E12, 1.375], [1.74706236E12, 2.4444444444444446], [1.74706398E12, 2.111111111111111], [1.74706194E12, 2.625], [1.74706416E12, 1.5555555555555556], [1.74706356E12, 1.75], [1.74706518E12, 1.625], [1.74706248E12, 1.25], [1.7470641E12, 1.75], [1.74706188E12, 2.375], [1.7470635E12, 1.75], [1.74706368E12, 1.875], [1.7470653E12, 1.5], [1.74706308E12, 1.6666666666666667], [1.7470647E12, 1.5555555555555556], [1.74706392E12, 1.5], [1.74706332E12, 1.7499999999999998], [1.74706494E12, 1.125], [1.7470629E12, 1.3333333333333333], [1.74706512E12, 1.25], [1.7470623E12, 1.875], [1.74706452E12, 1.7777777777777777]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74706344E12, 3.3749999999999996], [1.74706506E12, 3.3888888888888893], [1.74706284E12, 3.0], [1.74706446E12, 3.1666666666666665], [1.74706242E12, 3.5], [1.74706464E12, 3.125], [1.74706566E12, 3.3636363636363638], [1.74706404E12, 2.933333333333333], [1.74706266E12, 3.2352941176470593], [1.74706488E12, 3.230769230769231], [1.74706206E12, 5.571428571428572], [1.74706428E12, 3.466666666666667], [1.74706224E12, 3.5], [1.74706386E12, 3.0588235294117645], [1.74706326E12, 3.1176470588235294], [1.74706548E12, 3.3888888888888893], [1.74706218E12, 3.7368421052631584], [1.7470644E12, 3.35], [1.7470638E12, 2.9230769230769234], [1.74706542E12, 3.8124999999999996], [1.74706338E12, 3.312500000000001], [1.7470656E12, 3.083333333333333], [1.747065E12, 3.230769230769231], [1.74706278E12, 2.9999999999999996], [1.747062E12, 4.0], [1.74706362E12, 3.1363636363636362], [1.74706302E12, 3.2], [1.74706524E12, 3.0625], [1.7470632E12, 3.5714285714285716], [1.74706482E12, 3.0], [1.7470626E12, 3.437500000000001], [1.74706422E12, 3.2000000000000006], [1.74706314E12, 3.3499999999999996], [1.74706536E12, 3.4], [1.74706254E12, 3.375], [1.74706476E12, 3.5000000000000004], [1.74706272E12, 3.333333333333333], [1.74706434E12, 2.7857142857142856], [1.74706212E12, 5.1], [1.74706374E12, 2.928571428571428], [1.74706296E12, 3.285714285714286], [1.74706458E12, 3.4], [1.74706236E12, 3.583333333333334], [1.74706398E12, 3.3125000000000004], [1.74706578E12, 3.3333333333333335], [1.74706416E12, 3.1363636363636362], [1.74706356E12, 3.3181818181818183], [1.74706518E12, 3.0526315789473686], [1.74706248E12, 3.333333333333333], [1.7470641E12, 3.3157894736842106], [1.7470635E12, 2.9999999999999996], [1.74706572E12, 3.0], [1.74706368E12, 3.3750000000000004], [1.7470653E12, 3.0], [1.74706308E12, 3.4210526315789473], [1.7470647E12, 3.3000000000000003], [1.74706392E12, 3.1578947368421053], [1.74706554E12, 3.3333333333333335], [1.74706332E12, 3.277777777777778], [1.74706494E12, 3.2], [1.7470629E12, 3.1999999999999997], [1.74706512E12, 3.1363636363636362], [1.7470623E12, 3.3888888888888897], [1.74706452E12, 2.9411764705882355]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74706344E12, 1.3333333333333333], [1.74706506E12, 1.7777777777777777], [1.74706284E12, 2.125], [1.74706446E12, 1.375], [1.74706242E12, 2.25], [1.74706464E12, 1.875], [1.74706404E12, 1.5], [1.74706182E12, 2.25], [1.74706266E12, 1.5], [1.74706488E12, 1.8888888888888888], [1.74706206E12, 2.375], [1.74706428E12, 2.0], [1.74706224E12, 3.0], [1.74706386E12, 1.75], [1.74706326E12, 2.111111111111111], [1.74706218E12, 2.111111111111111], [1.7470644E12, 1.625], [1.7470638E12, 1.6666666666666667], [1.74706542E12, 2.0], [1.74706338E12, 1.625], [1.747065E12, 1.875], [1.74706278E12, 1.75], [1.747062E12, 1.6666666666666667], [1.74706362E12, 1.8888888888888888], [1.74706302E12, 1.625], [1.74706524E12, 1.4444444444444444], [1.7470632E12, 1.75], [1.74706482E12, 1.375], [1.7470626E12, 2.5], [1.74706422E12, 2.125], [1.74706314E12, 2.125], [1.74706536E12, 1.75], [1.74706254E12, 2.111111111111111], [1.74706476E12, 1.375], [1.74706272E12, 1.8888888888888888], [1.74706434E12, 1.8888888888888886], [1.74706212E12, 2.1250000000000004], [1.74706374E12, 1.5], [1.74706296E12, 2.375], [1.74706458E12, 2.0], [1.74706236E12, 2.0], [1.74706398E12, 1.7777777777777777], [1.74706194E12, 2.375], [1.74706416E12, 1.3333333333333333], [1.74706356E12, 2.625], [1.74706518E12, 1.75], [1.74706248E12, 2.0], [1.7470641E12, 2.0], [1.74706188E12, 2.25], [1.7470635E12, 1.75], [1.74706368E12, 1.5], [1.7470653E12, 1.875], [1.74706308E12, 1.6666666666666665], [1.7470647E12, 1.8888888888888888], [1.74706392E12, 1.5], [1.74706332E12, 1.625], [1.74706494E12, 1.625], [1.7470629E12, 1.7777777777777777], [1.74706512E12, 2.125], [1.7470623E12, 1.875], [1.74706452E12, 1.7777777777777777]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74706344E12, 4.629629629629629], [1.74706506E12, 5.384615384615387], [1.74706284E12, 4.821428571428571], [1.74706446E12, 5.115384615384617], [1.74706242E12, 5.333333333333335], [1.74706464E12, 5.521739130434783], [1.74706404E12, 4.84], [1.74706182E12, 6.384615384615384], [1.74706266E12, 4.8275862068965525], [1.74706488E12, 5.36], [1.74706206E12, 5.52], [1.74706428E12, 5.120000000000001], [1.74706224E12, 4.833333333333334], [1.74706386E12, 5.259259259259259], [1.74706326E12, 4.785714285714285], [1.74706218E12, 5.333333333333337], [1.7470644E12, 5.249999999999999], [1.7470638E12, 5.846153846153847], [1.74706542E12, 5.307692307692308], [1.74706338E12, 5.0], [1.747065E12, 5.279999999999999], [1.74706278E12, 5.346153846153846], [1.747062E12, 5.521739130434782], [1.74706362E12, 5.040000000000001], [1.74706302E12, 5.04], [1.74706524E12, 5.076923076923076], [1.7470632E12, 4.909090909090907], [1.74706482E12, 5.4642857142857135], [1.7470626E12, 5.565217391304349], [1.74706422E12, 5.200000000000001], [1.74706314E12, 4.923076923076923], [1.74706536E12, 4.653846153846154], [1.74706254E12, 5.416666666666666], [1.74706476E12, 4.916666666666666], [1.74706272E12, 5.200000000000001], [1.74706434E12, 5.166666666666666], [1.74706212E12, 5.875000000000002], [1.74706374E12, 5.272727272727273], [1.74706296E12, 5.086956521739131], [1.74706458E12, 5.370370370370369], [1.74706236E12, 5.000000000000002], [1.74706398E12, 5.333333333333333], [1.74706194E12, 5.241379310344827], [1.74706416E12, 5.3076923076923075], [1.74706356E12, 4.961538461538463], [1.74706518E12, 5.0769230769230775], [1.74706248E12, 5.2592592592592595], [1.7470641E12, 4.846153846153845], [1.74706188E12, 5.238095238095237], [1.7470635E12, 5.217391304347826], [1.74706368E12, 4.888888888888889], [1.7470653E12, 5.083333333333334], [1.74706308E12, 4.846153846153847], [1.7470647E12, 4.956521739130435], [1.74706392E12, 4.782608695652174], [1.74706332E12, 5.249999999999999], [1.74706494E12, 5.208333333333332], [1.7470629E12, 4.625], [1.74706512E12, 5.608695652173913], [1.7470623E12, 6.3999999999999995], [1.74706452E12, 4.680000000000001]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74706344E12, 1.8888888888888888], [1.74706506E12, 1.5555555555555556], [1.74706284E12, 1.75], [1.74706446E12, 2.0], [1.74706242E12, 2.0], [1.74706464E12, 1.75], [1.74706404E12, 1.625], [1.74706182E12, 2.625], [1.74706266E12, 1.875], [1.74706488E12, 1.5555555555555556], [1.74706206E12, 2.125], [1.74706428E12, 1.875], [1.74706224E12, 2.0], [1.74706386E12, 1.75], [1.74706326E12, 2.1111111111111116], [1.74706218E12, 2.2222222222222228], [1.7470644E12, 2.0], [1.7470638E12, 1.6666666666666667], [1.74706542E12, 2.0], [1.74706338E12, 1.75], [1.747065E12, 1.375], [1.74706278E12, 2.0], [1.747062E12, 3.666666666666667], [1.74706362E12, 1.4444444444444444], [1.74706302E12, 1.625], [1.74706524E12, 1.5555555555555556], [1.7470632E12, 1.875], [1.74706482E12, 1.875], [1.7470626E12, 1.625], [1.74706422E12, 1.625], [1.74706314E12, 1.875], [1.74706536E12, 1.5], [1.74706254E12, 2.1111111111111116], [1.74706476E12, 1.625], [1.74706272E12, 2.1111111111111116], [1.74706434E12, 2.4444444444444446], [1.74706212E12, 2.125], [1.74706374E12, 1.5], [1.74706296E12, 2.1250000000000004], [1.74706458E12, 1.75], [1.74706236E12, 2.111111111111111], [1.74706398E12, 1.4444444444444444], [1.74706194E12, 2.2500000000000004], [1.74706416E12, 1.8888888888888886], [1.74706356E12, 1.625], [1.74706518E12, 1.5], [1.74706248E12, 1.8749999999999998], [1.7470641E12, 1.25], [1.74706188E12, 2.25], [1.7470635E12, 1.75], [1.74706368E12, 1.625], [1.7470653E12, 1.75], [1.74706308E12, 1.8888888888888888], [1.7470647E12, 1.8888888888888888], [1.74706392E12, 1.75], [1.74706332E12, 1.875], [1.74706494E12, 2.0], [1.7470629E12, 1.7777777777777777], [1.74706512E12, 1.625], [1.7470623E12, 2.3750000000000004], [1.74706452E12, 1.7777777777777777]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74706344E12, 3.0857142857142854], [1.74706506E12, 2.888888888888889], [1.74706284E12, 3.6799999999999993], [1.74706446E12, 2.8750000000000004], [1.74706242E12, 3.724999999999999], [1.74706464E12, 3.14], [1.74706566E12, 2.9000000000000004], [1.74706404E12, 2.888888888888889], [1.74706266E12, 3.2727272727272725], [1.74706488E12, 3.04], [1.74706206E12, 3.4399999999999995], [1.74706428E12, 3.085714285714286], [1.74706224E12, 3.6499999999999995], [1.74706386E12, 2.9999999999999996], [1.74706326E12, 3.533333333333334], [1.74706548E12, 3.2], [1.74706218E12, 3.428571428571429], [1.7470644E12, 2.7428571428571424], [1.7470638E12, 3.657142857142857], [1.74706542E12, 4.857142857142857], [1.74706338E12, 3.057142857142857], [1.7470656E12, 2.95], [1.747065E12, 3.025], [1.74706278E12, 3.6999999999999997], [1.747062E12, 3.7333333333333334], [1.74706362E12, 3.6249999999999996], [1.74706302E12, 3.3714285714285714], [1.74706524E12, 3.2333333333333334], [1.7470632E12, 3.2727272727272725], [1.74706482E12, 3.0200000000000014], [1.7470626E12, 3.2666666666666675], [1.74706422E12, 2.9000000000000004], [1.74706314E12, 3.3777777777777773], [1.74706536E12, 2.953846153846153], [1.74706254E12, 3.228571428571428], [1.74706476E12, 3.16], [1.74706272E12, 3.1799999999999993], [1.74706434E12, 3.0399999999999996], [1.74706212E12, 3.9272727272727264], [1.74706374E12, 3.266666666666666], [1.74706296E12, 3.1111111111111116], [1.74706458E12, 2.7714285714285714], [1.74706236E12, 3.2399999999999998], [1.74706398E12, 3.1999999999999997], [1.74706416E12, 3.2], [1.74706356E12, 3.146666666666665], [1.74706518E12, 2.975], [1.74706248E12, 3.4], [1.7470641E12, 3.499999999999999], [1.7470635E12, 3.5], [1.74706572E12, 3.3000000000000003], [1.74706368E12, 3.366666666666667], [1.7470653E12, 2.8500000000000005], [1.74706308E12, 3.2], [1.7470647E12, 3.1999999999999997], [1.74706392E12, 2.9333333333333336], [1.74706554E12, 2.72], [1.74706332E12, 2.933333333333334], [1.74706494E12, 2.8], [1.7470629E12, 3.1999999999999997], [1.74706512E12, 2.963636363636363], [1.7470623E12, 3.514285714285715], [1.74706452E12, 2.8666666666666667]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74706578E12, "title": "Latencies Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average response latencies in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendLatenciesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average latency was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesLatenciesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotLatenciesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewLatenciesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Latencies Over Time
function refreshLatenciesOverTime(fixTimestamps) {
    var infos = latenciesOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyLatenciesOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotLatenciesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesLatenciesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotLatenciesOverTime", "#overviewLatenciesOverTime");
        $('#footerLatenciesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var connectTimeOverTimeInfos = {
        data: {"result": {"minY": 0.0, "minX": 1.74706182E12, "maxY": 3.5, "series": [{"data": [[1.74706344E12, 0.8571428571428572], [1.74706506E12, 0.8888888888888888], [1.74706284E12, 0.8], [1.74706446E12, 0.75], [1.74706242E12, 1.0], [1.74706464E12, 0.7], [1.74706566E12, 0.5], [1.74706404E12, 0.7777777777777778], [1.74706266E12, 0.9090909090909091], [1.74706488E12, 0.6], [1.74706206E12, 1.0], [1.74706428E12, 0.7142857142857143], [1.74706224E12, 0.75], [1.74706386E12, 0.9999999999999999], [1.74706326E12, 0.7777777777777778], [1.74706548E12, 0.625], [1.74706218E12, 0.8571428571428572], [1.7470644E12, 0.7142857142857143], [1.7470638E12, 0.7142857142857143], [1.74706542E12, 1.0], [1.74706338E12, 0.8571428571428572], [1.7470656E12, 0.5], [1.747065E12, 0.5], [1.74706278E12, 0.75], [1.747062E12, 1.0], [1.74706362E12, 0.75], [1.74706302E12, 0.8571428571428572], [1.74706524E12, 0.6666666666666666], [1.7470632E12, 0.9090909090909092], [1.74706482E12, 0.7], [1.7470626E12, 0.8333333333333333], [1.74706422E12, 0.75], [1.74706314E12, 0.4444444444444444], [1.74706536E12, 1.0], [1.74706254E12, 0.8571428571428572], [1.74706476E12, 1.0], [1.74706272E12, 0.8999999999999999], [1.74706434E12, 0.8], [1.74706212E12, 0.9090909090909091], [1.74706374E12, 0.6666666666666666], [1.74706296E12, 1.1111111111111112], [1.74706458E12, 0.7142857142857143], [1.74706236E12, 1.0], [1.74706398E12, 1.0], [1.74706416E12, 0.75], [1.74706356E12, 0.8], [1.74706518E12, 0.5], [1.74706248E12, 1.1666666666666665], [1.7470641E12, 0.7142857142857143], [1.7470635E12, 1.0], [1.74706572E12, 0.5], [1.74706368E12, 1.0], [1.7470653E12, 0.875], [1.74706308E12, 0.875], [1.7470647E12, 0.8888888888888888], [1.74706392E12, 0.7777777777777778], [1.74706554E12, 0.8], [1.74706332E12, 0.8333333333333334], [1.74706494E12, 0.8], [1.7470629E12, 0.75], [1.74706512E12, 0.8181818181818181], [1.7470623E12, 0.5714285714285714], [1.74706452E12, 0.8888888888888888]], "isOverall": false, "label": "Get all albums", "isController": false}, {"data": [[1.74706344E12, 0.4444444444444444], [1.74706506E12, 0.7777777777777778], [1.74706284E12, 0.75], [1.74706446E12, 0.375], [1.74706242E12, 0.6250000000000001], [1.74706464E12, 0.75], [1.74706404E12, 0.5], [1.74706182E12, 3.5], [1.74706266E12, 0.5], [1.74706488E12, 0.6666666666666666], [1.74706206E12, 0.875], [1.74706428E12, 0.5], [1.74706224E12, 0.75], [1.74706386E12, 0.625], [1.74706326E12, 0.6666666666666666], [1.74706218E12, 0.7777777777777778], [1.7470644E12, 0.75], [1.7470638E12, 0.4444444444444444], [1.74706542E12, 1.0], [1.74706338E12, 0.5], [1.747065E12, 0.625], [1.74706278E12, 0.625], [1.747062E12, 0.6666666666666666], [1.74706362E12, 0.3333333333333333], [1.74706302E12, 0.6250000000000001], [1.74706524E12, 0.7777777777777778], [1.7470632E12, 0.375], [1.74706482E12, 0.5], [1.7470626E12, 0.75], [1.74706422E12, 0.75], [1.74706314E12, 0.5], [1.74706536E12, 0.625], [1.74706254E12, 0.6666666666666666], [1.74706476E12, 0.625], [1.74706272E12, 0.6666666666666666], [1.74706434E12, 0.6666666666666666], [1.74706212E12, 0.625], [1.74706374E12, 0.625], [1.74706296E12, 0.625], [1.74706458E12, 0.625], [1.74706236E12, 0.5555555555555556], [1.74706398E12, 0.7777777777777778], [1.74706194E12, 1.0], [1.74706416E12, 0.7777777777777778], [1.74706356E12, 0.625], [1.74706518E12, 0.5], [1.74706248E12, 0.5], [1.7470641E12, 0.375], [1.74706188E12, 0.5], [1.7470635E12, 0.375], [1.74706368E12, 0.625], [1.7470653E12, 0.5], [1.74706308E12, 0.7777777777777778], [1.7470647E12, 0.7777777777777778], [1.74706392E12, 0.75], [1.74706332E12, 0.375], [1.74706494E12, 0.625], [1.7470629E12, 0.4444444444444444], [1.74706512E12, 0.875], [1.7470623E12, 0.625], [1.74706452E12, 0.5555555555555556]], "isOverall": false, "label": "Home page", "isController": true}, {"data": [[1.74706344E12, 0.65], [1.74706506E12, 0.7586206896551725], [1.74706284E12, 0.4782608695652174], [1.74706446E12, 0.68], [1.74706242E12, 0.7307692307692308], [1.74706464E12, 0.6363636363636365], [1.74706404E12, 0.5833333333333334], [1.74706182E12, 0.0], [1.74706266E12, 0.48148148148148145], [1.74706488E12, 0.6333333333333334], [1.74706206E12, 0.8888888888888888], [1.74706428E12, 0.7826086956521741], [1.74706224E12, 0.7000000000000002], [1.74706386E12, 0.736842105263158], [1.74706326E12, 0.36363636363636365], [1.74706548E12, 0.6666666666666666], [1.74706218E12, 0.8787878787878786], [1.7470644E12, 0.6296296296296298], [1.7470638E12, 0.6250000000000001], [1.74706542E12, 0.5], [1.74706338E12, 0.7142857142857143], [1.7470656E12, 0.33333333333333337], [1.747065E12, 0.565217391304348], [1.74706278E12, 0.6956521739130436], [1.747062E12, 0.9047619047619048], [1.74706362E12, 0.5238095238095238], [1.74706302E12, 0.7037037037037038], [1.74706524E12, 0.6000000000000001], [1.7470632E12, 0.6000000000000001], [1.74706482E12, 0.6818181818181819], [1.7470626E12, 0.7241379310344829], [1.74706422E12, 0.7692307692307692], [1.74706314E12, 0.8571428571428569], [1.74706536E12, 0.7083333333333334], [1.74706254E12, 0.7368421052631577], [1.74706476E12, 0.5833333333333334], [1.74706272E12, 0.7200000000000002], [1.74706434E12, 0.64], [1.74706212E12, 0.8636363636363634], [1.74706374E12, 0.48], [1.74706296E12, 0.5294117647058824], [1.74706458E12, 0.7500000000000002], [1.74706236E12, 0.6956521739130436], [1.74706398E12, 0.769230769230769], [1.74706194E12, 0.8823529411764705], [1.74706416E12, 0.6799999999999999], [1.74706356E12, 0.7999999999999998], [1.74706518E12, 0.6956521739130436], [1.74706248E12, 0.8999999999999999], [1.7470641E12, 0.7142857142857143], [1.74706188E12, 0.8333333333333334], [1.7470635E12, 0.7352941176470589], [1.74706368E12, 0.64], [1.7470653E12, 0.7142857142857143], [1.74706308E12, 0.9666666666666667], [1.7470647E12, 0.6666666666666667], [1.74706392E12, 0.6363636363636364], [1.74706554E12, 1.0], [1.74706332E12, 0.6666666666666666], [1.74706494E12, 0.5833333333333335], [1.7470629E12, 0.6176470588235295], [1.74706512E12, 0.6956521739130435], [1.7470623E12, 0.8333333333333331], [1.74706452E12, 0.7826086956521737]], "isOverall": false, "label": "Play songs from home page", "isController": false}, {"data": [[1.74706344E12, 0.4444444444444444], [1.74706506E12, 0.7777777777777778], [1.74706284E12, 0.75], [1.74706446E12, 0.375], [1.74706242E12, 0.625], [1.74706464E12, 0.75], [1.74706404E12, 0.5], [1.74706182E12, 3.5], [1.74706266E12, 0.5], [1.74706488E12, 0.6666666666666666], [1.74706206E12, 0.875], [1.74706428E12, 0.5], [1.74706224E12, 0.75], [1.74706386E12, 0.625], [1.74706326E12, 0.6666666666666666], [1.74706218E12, 0.7777777777777778], [1.7470644E12, 0.75], [1.7470638E12, 0.4444444444444444], [1.74706542E12, 1.0], [1.74706338E12, 0.5], [1.747065E12, 0.6250000000000001], [1.74706278E12, 0.625], [1.747062E12, 0.6666666666666666], [1.74706362E12, 0.3333333333333333], [1.74706302E12, 0.6250000000000001], [1.74706524E12, 0.7777777777777778], [1.7470632E12, 0.375], [1.74706482E12, 0.5], [1.7470626E12, 0.75], [1.74706422E12, 0.75], [1.74706314E12, 0.5], [1.74706536E12, 0.625], [1.74706254E12, 0.6666666666666666], [1.74706476E12, 0.625], [1.74706272E12, 0.6666666666666666], [1.74706434E12, 0.6666666666666666], [1.74706212E12, 0.625], [1.74706374E12, 0.625], [1.74706296E12, 0.625], [1.74706458E12, 0.625], [1.74706236E12, 0.5555555555555556], [1.74706398E12, 0.7777777777777778], [1.74706194E12, 1.0], [1.74706416E12, 0.7777777777777778], [1.74706356E12, 0.625], [1.74706518E12, 0.5], [1.74706248E12, 0.5], [1.7470641E12, 0.375], [1.74706188E12, 0.5], [1.7470635E12, 0.375], [1.74706368E12, 0.625], [1.7470653E12, 0.5], [1.74706308E12, 0.7777777777777778], [1.7470647E12, 0.7777777777777778], [1.74706392E12, 0.75], [1.74706332E12, 0.375], [1.74706494E12, 0.625], [1.7470629E12, 0.4444444444444444], [1.74706512E12, 0.875], [1.7470623E12, 0.625], [1.74706452E12, 0.5555555555555556]], "isOverall": false, "label": "Trending playlist", "isController": false}, {"data": [[1.74706344E12, 0.8571428571428572], [1.74706506E12, 0.8888888888888888], [1.74706284E12, 0.8], [1.74706446E12, 0.75], [1.74706242E12, 1.0], [1.74706464E12, 0.7], [1.74706566E12, 0.5], [1.74706404E12, 0.7777777777777778], [1.74706266E12, 0.9090909090909091], [1.74706488E12, 0.6], [1.74706206E12, 1.0], [1.74706428E12, 0.7142857142857143], [1.74706224E12, 0.75], [1.74706386E12, 1.0], [1.74706326E12, 0.7777777777777778], [1.74706548E12, 0.625], [1.74706218E12, 0.8571428571428572], [1.7470644E12, 0.7142857142857143], [1.7470638E12, 0.7142857142857143], [1.74706542E12, 1.0], [1.74706338E12, 0.8571428571428572], [1.7470656E12, 0.5], [1.747065E12, 0.5], [1.74706278E12, 0.75], [1.747062E12, 1.0], [1.74706362E12, 0.75], [1.74706302E12, 0.8571428571428572], [1.74706524E12, 0.6666666666666666], [1.7470632E12, 0.9090909090909091], [1.74706482E12, 0.7], [1.7470626E12, 0.8333333333333334], [1.74706422E12, 0.75], [1.74706314E12, 0.4444444444444444], [1.74706536E12, 1.0], [1.74706254E12, 0.8571428571428572], [1.74706476E12, 1.0], [1.74706272E12, 0.8999999999999999], [1.74706434E12, 0.8], [1.74706212E12, 0.9090909090909091], [1.74706374E12, 0.6666666666666666], [1.74706296E12, 1.1111111111111112], [1.74706458E12, 0.7142857142857143], [1.74706236E12, 1.0], [1.74706398E12, 1.0], [1.74706416E12, 0.75], [1.74706356E12, 0.8], [1.74706518E12, 0.5], [1.74706248E12, 1.1666666666666665], [1.7470641E12, 0.7142857142857143], [1.7470635E12, 1.0], [1.74706572E12, 0.5], [1.74706368E12, 1.0], [1.7470653E12, 0.875], [1.74706308E12, 0.875], [1.7470647E12, 0.8888888888888888], [1.74706392E12, 0.7777777777777778], [1.74706554E12, 0.8], [1.74706332E12, 0.8333333333333334], [1.74706494E12, 0.8], [1.7470629E12, 0.75], [1.74706512E12, 0.8181818181818181], [1.7470623E12, 0.5714285714285714], [1.74706452E12, 0.8888888888888888]], "isOverall": false, "label": "Albums page", "isController": true}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706404E12, 0.0], [1.74706182E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706194E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.74706188E12, 0.0], [1.7470635E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Cover 2", "isController": false}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706404E12, 0.0], [1.74706182E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706194E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.74706188E12, 0.0], [1.7470635E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Cover 1", "isController": false}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706404E12, 0.0], [1.74706182E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706194E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.74706188E12, 0.0], [1.7470635E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Cover 4", "isController": false}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706404E12, 0.0], [1.74706182E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706194E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.74706188E12, 0.0], [1.7470635E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Cover 3", "isController": false}, {"data": [[1.74706344E12, 0.9375], [1.74706506E12, 0.8888888888888886], [1.74706284E12, 0.894736842105263], [1.74706446E12, 0.8333333333333331], [1.74706242E12, 0.8333333333333331], [1.74706464E12, 0.8125], [1.74706566E12, 0.8181818181818182], [1.74706404E12, 0.6000000000000001], [1.74706266E12, 0.8823529411764703], [1.74706488E12, 0.7692307692307693], [1.74706206E12, 1.0], [1.74706428E12, 0.7333333333333334], [1.74706224E12, 0.7777777777777777], [1.74706386E12, 0.8235294117647058], [1.74706326E12, 0.8235294117647057], [1.74706548E12, 0.9444444444444444], [1.74706218E12, 0.9473684210526314], [1.7470644E12, 0.9500000000000002], [1.7470638E12, 0.8461538461538461], [1.74706542E12, 0.9374999999999999], [1.74706338E12, 0.9999999999999999], [1.7470656E12, 0.6666666666666666], [1.747065E12, 0.923076923076923], [1.74706278E12, 0.7727272727272727], [1.747062E12, 1.0], [1.74706362E12, 0.7272727272727272], [1.74706302E12, 1.0], [1.74706524E12, 0.875], [1.7470632E12, 1.0000000000000002], [1.74706482E12, 0.8235294117647057], [1.7470626E12, 0.75], [1.74706422E12, 0.7333333333333333], [1.74706314E12, 0.8499999999999999], [1.74706536E12, 0.8399999999999999], [1.74706254E12, 0.9999999999999999], [1.74706476E12, 0.9285714285714288], [1.74706272E12, 0.8333333333333331], [1.74706434E12, 0.5714285714285714], [1.74706212E12, 1.1], [1.74706374E12, 0.6428571428571428], [1.74706296E12, 0.7857142857142857], [1.74706458E12, 0.7999999999999999], [1.74706236E12, 0.9166666666666666], [1.74706398E12, 0.8125], [1.74706578E12, 1.0], [1.74706416E12, 0.8181818181818181], [1.74706356E12, 0.9999999999999999], [1.74706518E12, 0.7368421052631577], [1.74706248E12, 0.7333333333333334], [1.7470641E12, 0.894736842105263], [1.7470635E12, 1.0], [1.74706572E12, 0.5], [1.74706368E12, 0.75], [1.7470653E12, 0.6], [1.74706308E12, 0.8421052631578946], [1.7470647E12, 0.8499999999999998], [1.74706392E12, 0.7894736842105262], [1.74706554E12, 1.0833333333333333], [1.74706332E12, 0.8333333333333331], [1.74706494E12, 0.8666666666666665], [1.7470629E12, 0.7333333333333334], [1.74706512E12, 0.8181818181818181], [1.7470623E12, 1.0], [1.74706452E12, 0.7647058823529411]], "isOverall": false, "label": "Play songs from albums", "isController": false}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706404E12, 0.0], [1.74706182E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706194E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.74706188E12, 0.0], [1.7470635E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Cover 5", "isController": false}, {"data": [[1.74706344E12, 0.4444444444444445], [1.74706506E12, 0.4230769230769231], [1.74706284E12, 0.3928571428571429], [1.74706446E12, 0.5769230769230769], [1.74706242E12, 0.6666666666666669], [1.74706464E12, 0.391304347826087], [1.74706404E12, 0.44], [1.74706182E12, 0.8461538461538461], [1.74706266E12, 0.31034482758620696], [1.74706488E12, 0.6000000000000001], [1.74706206E12, 0.7599999999999998], [1.74706428E12, 0.43999999999999995], [1.74706224E12, 0.37500000000000006], [1.74706386E12, 0.6296296296296295], [1.74706326E12, 0.5357142857142856], [1.74706218E12, 0.5925925925925927], [1.7470644E12, 0.3749999999999999], [1.7470638E12, 0.423076923076923], [1.74706542E12, 0.5384615384615384], [1.74706338E12, 0.6], [1.747065E12, 0.44], [1.74706278E12, 0.6153846153846154], [1.747062E12, 0.7391304347826088], [1.74706362E12, 0.6], [1.74706302E12, 0.48], [1.74706524E12, 0.5], [1.7470632E12, 0.590909090909091], [1.74706482E12, 0.5], [1.7470626E12, 0.6956521739130435], [1.74706422E12, 0.5200000000000001], [1.74706314E12, 0.42307692307692313], [1.74706536E12, 0.6153846153846154], [1.74706254E12, 0.8749999999999999], [1.74706476E12, 0.5], [1.74706272E12, 0.5499999999999999], [1.74706434E12, 0.5833333333333334], [1.74706212E12, 0.7083333333333335], [1.74706374E12, 0.6363636363636364], [1.74706296E12, 0.7826086956521741], [1.74706458E12, 0.6296296296296297], [1.74706236E12, 0.6153846153846154], [1.74706398E12, 0.5833333333333334], [1.74706194E12, 0.6551724137931034], [1.74706416E12, 0.7307692307692306], [1.74706356E12, 0.6153846153846155], [1.74706518E12, 0.46153846153846156], [1.74706248E12, 0.6296296296296298], [1.7470641E12, 0.4230769230769231], [1.74706188E12, 0.7142857142857144], [1.7470635E12, 0.6086956521739131], [1.74706368E12, 0.5555555555555557], [1.7470653E12, 0.5833333333333334], [1.74706308E12, 0.6923076923076924], [1.7470647E12, 0.6086956521739131], [1.74706392E12, 0.5217391304347827], [1.74706332E12, 0.5833333333333334], [1.74706494E12, 0.5833333333333335], [1.7470629E12, 0.29166666666666674], [1.74706512E12, 0.3478260869565218], [1.7470623E12, 0.5600000000000002], [1.74706452E12, 0.4000000000000001]], "isOverall": false, "label": "Search", "isController": false}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706404E12, 0.0], [1.74706182E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706194E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.74706188E12, 0.0], [1.7470635E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Initial song", "isController": false}, {"data": [[1.74706344E12, 0.0], [1.74706506E12, 0.0], [1.74706284E12, 0.0], [1.74706446E12, 0.0], [1.74706242E12, 0.0], [1.74706464E12, 0.0], [1.74706566E12, 0.0], [1.74706404E12, 0.0], [1.74706266E12, 0.0], [1.74706488E12, 0.0], [1.74706206E12, 0.0], [1.74706428E12, 0.0], [1.74706224E12, 0.0], [1.74706386E12, 0.0], [1.74706326E12, 0.0], [1.74706548E12, 0.0], [1.74706218E12, 0.0], [1.7470644E12, 0.0], [1.7470638E12, 0.0], [1.74706542E12, 0.0], [1.74706338E12, 0.0], [1.7470656E12, 0.0], [1.747065E12, 0.0], [1.74706278E12, 0.0], [1.747062E12, 0.0], [1.74706362E12, 0.0], [1.74706302E12, 0.0], [1.74706524E12, 0.0], [1.7470632E12, 0.0], [1.74706482E12, 0.0], [1.7470626E12, 0.0], [1.74706422E12, 0.0], [1.74706314E12, 0.0], [1.74706536E12, 0.0], [1.74706254E12, 0.0], [1.74706476E12, 0.0], [1.74706272E12, 0.0], [1.74706434E12, 0.0], [1.74706212E12, 0.0], [1.74706374E12, 0.0], [1.74706296E12, 0.0], [1.74706458E12, 0.0], [1.74706236E12, 0.0], [1.74706398E12, 0.0], [1.74706416E12, 0.0], [1.74706356E12, 0.0], [1.74706518E12, 0.0], [1.74706248E12, 0.0], [1.7470641E12, 0.0], [1.7470635E12, 0.0], [1.74706572E12, 0.0], [1.74706368E12, 0.0], [1.7470653E12, 0.0], [1.74706308E12, 0.0], [1.7470647E12, 0.0], [1.74706392E12, 0.0], [1.74706554E12, 0.0], [1.74706332E12, 0.0], [1.74706494E12, 0.0], [1.7470629E12, 0.0], [1.74706512E12, 0.0], [1.7470623E12, 0.0], [1.74706452E12, 0.0]], "isOverall": false, "label": "Get paths to songs from each album", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74706578E12, "title": "Connect Time Over Time"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getConnectTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Average Connect Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendConnectTimeOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Average connect time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesConnectTimeOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotConnectTimeOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewConnectTimeOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Connect Time Over Time
function refreshConnectTimeOverTime(fixTimestamps) {
    var infos = connectTimeOverTimeInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyConnectTimeOverTime");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotConnectTimeOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesConnectTimeOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotConnectTimeOverTime", "#overviewConnectTimeOverTime");
        $('#footerConnectTimeOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var responseTimePercentilesOverTimeInfos = {
        data: {"result": {"minY": 2.0, "minX": 1.74706182E12, "maxY": 180.0, "series": [{"data": [[1.74706344E12, 78.0], [1.74706506E12, 53.0], [1.74706284E12, 82.0], [1.74706446E12, 55.0], [1.74706242E12, 60.0], [1.74706464E12, 54.0], [1.74706566E12, 42.0], [1.74706404E12, 56.0], [1.74706182E12, 53.0], [1.74706266E12, 84.0], [1.74706488E12, 62.0], [1.74706206E12, 68.0], [1.74706428E12, 73.0], [1.74706224E12, 66.0], [1.74706386E12, 48.0], [1.74706326E12, 67.0], [1.74706548E12, 67.0], [1.74706218E12, 55.0], [1.7470644E12, 75.0], [1.7470638E12, 78.0], [1.74706542E12, 180.0], [1.74706338E12, 53.0], [1.7470656E12, 50.0], [1.747065E12, 50.0], [1.74706278E12, 54.0], [1.747062E12, 57.0], [1.74706362E12, 57.0], [1.74706302E12, 63.0], [1.74706524E12, 63.0], [1.7470632E12, 53.0], [1.74706482E12, 68.0], [1.7470626E12, 69.0], [1.74706422E12, 86.0], [1.74706314E12, 52.0], [1.74706536E12, 78.0], [1.74706254E12, 65.0], [1.74706476E12, 66.0], [1.74706272E12, 72.0], [1.74706434E12, 63.0], [1.74706212E12, 81.0], [1.74706374E12, 58.0], [1.74706296E12, 59.0], [1.74706458E12, 55.0], [1.74706236E12, 76.0], [1.74706398E12, 67.0], [1.74706578E12, 54.0], [1.74706194E12, 77.0], [1.74706416E12, 61.0], [1.74706356E12, 84.0], [1.74706518E12, 51.0], [1.74706248E12, 53.0], [1.7470641E12, 71.0], [1.74706188E12, 68.0], [1.7470635E12, 82.0], [1.74706572E12, 40.0], [1.74706368E12, 54.0], [1.7470653E12, 60.0], [1.74706308E12, 70.0], [1.7470647E12, 56.0], [1.74706392E12, 65.0], [1.74706554E12, 46.0], [1.74706332E12, 58.0], [1.74706494E12, 53.0], [1.7470629E12, 60.0], [1.74706512E12, 55.0], [1.7470623E12, 62.0], [1.74706452E12, 77.0]], "isOverall": false, "label": "Max", "isController": false}, {"data": [[1.74706344E12, 2.0], [1.74706506E12, 2.0], [1.74706284E12, 2.0], [1.74706446E12, 2.0], [1.74706242E12, 2.0], [1.74706464E12, 2.0], [1.74706566E12, 2.0], [1.74706404E12, 2.0], [1.74706182E12, 3.0], [1.74706266E12, 2.0], [1.74706488E12, 2.0], [1.74706206E12, 2.0], [1.74706428E12, 2.0], [1.74706224E12, 3.0], [1.74706386E12, 2.0], [1.74706326E12, 2.0], [1.74706548E12, 2.0], [1.74706218E12, 2.0], [1.7470644E12, 2.0], [1.7470638E12, 2.0], [1.74706542E12, 3.0], [1.74706338E12, 2.0], [1.7470656E12, 2.0], [1.747065E12, 2.0], [1.74706278E12, 2.0], [1.747062E12, 2.0], [1.74706362E12, 2.0], [1.74706302E12, 2.0], [1.74706524E12, 2.0], [1.7470632E12, 2.0], [1.74706482E12, 2.0], [1.7470626E12, 2.0], [1.74706422E12, 2.0], [1.74706314E12, 2.0], [1.74706536E12, 2.0], [1.74706254E12, 2.0], [1.74706476E12, 2.0], [1.74706272E12, 2.0], [1.74706434E12, 2.0], [1.74706212E12, 2.0], [1.74706374E12, 2.0], [1.74706296E12, 2.0], [1.74706458E12, 2.0], [1.74706236E12, 2.0], [1.74706398E12, 2.0], [1.74706578E12, 39.0], [1.74706194E12, 2.0], [1.74706416E12, 2.0], [1.74706356E12, 2.0], [1.74706518E12, 2.0], [1.74706248E12, 2.0], [1.7470641E12, 2.0], [1.74706188E12, 2.0], [1.7470635E12, 2.0], [1.74706572E12, 3.0], [1.74706368E12, 2.0], [1.7470653E12, 2.0], [1.74706308E12, 2.0], [1.7470647E12, 2.0], [1.74706392E12, 2.0], [1.74706554E12, 2.0], [1.74706332E12, 2.0], [1.74706494E12, 2.0], [1.7470629E12, 2.0], [1.74706512E12, 2.0], [1.7470623E12, 2.0], [1.74706452E12, 2.0]], "isOverall": false, "label": "Min", "isController": false}, {"data": [[1.74706344E12, 41.0], [1.74706506E12, 42.900000000000006], [1.74706284E12, 43.30000000000001], [1.74706446E12, 42.0], [1.74706242E12, 43.400000000000034], [1.74706464E12, 42.20000000000002], [1.74706566E12, 41.0], [1.74706404E12, 40.5], [1.74706182E12, 42.0], [1.74706266E12, 42.400000000000006], [1.74706488E12, 43.0], [1.74706206E12, 43.0], [1.74706428E12, 41.0], [1.74706224E12, 43.0], [1.74706386E12, 40.0], [1.74706326E12, 42.5], [1.74706548E12, 46.10000000000001], [1.74706218E12, 45.0], [1.7470644E12, 42.0], [1.7470638E12, 46.30000000000001], [1.74706542E12, 58.10000000000001], [1.74706338E12, 41.0], [1.7470656E12, 40.0], [1.747065E12, 40.0], [1.74706278E12, 41.80000000000001], [1.747062E12, 45.0], [1.74706362E12, 41.0], [1.74706302E12, 43.80000000000001], [1.74706524E12, 43.0], [1.7470632E12, 43.0], [1.74706482E12, 40.0], [1.7470626E12, 43.0], [1.74706422E12, 43.0], [1.74706314E12, 41.20000000000002], [1.74706536E12, 41.0], [1.74706254E12, 43.0], [1.74706476E12, 42.099999999999994], [1.74706272E12, 42.30000000000001], [1.74706434E12, 41.0], [1.74706212E12, 41.099999999999994], [1.74706374E12, 42.0], [1.74706296E12, 41.0], [1.74706458E12, 40.099999999999994], [1.74706236E12, 42.0], [1.74706398E12, 44.0], [1.74706578E12, 54.0], [1.74706194E12, 50.0], [1.74706416E12, 41.0], [1.74706356E12, 40.0], [1.74706518E12, 40.70000000000002], [1.74706248E12, 44.0], [1.7470641E12, 42.599999999999994], [1.74706188E12, 45.0], [1.7470635E12, 44.099999999999994], [1.74706572E12, 38.5], [1.74706368E12, 41.0], [1.7470653E12, 43.0], [1.74706308E12, 45.0], [1.7470647E12, 43.5], [1.74706392E12, 43.0], [1.74706554E12, 41.2], [1.74706332E12, 45.0], [1.74706494E12, 41.0], [1.7470629E12, 45.0], [1.74706512E12, 40.0], [1.7470623E12, 44.0], [1.74706452E12, 41.0]], "isOverall": false, "label": "90th percentile", "isController": false}, {"data": [[1.74706344E12, 64.20000000000005], [1.74706506E12, 52.09], [1.74706284E12, 79.38999999999999], [1.74706446E12, 55.0], [1.74706242E12, 60.0], [1.74706464E12, 53.22], [1.74706566E12, 42.0], [1.74706404E12, 55.25], [1.74706182E12, 53.0], [1.74706266E12, 69.59999999999988], [1.74706488E12, 56.41999999999996], [1.74706206E12, 65.69999999999996], [1.74706428E12, 71.75999999999999], [1.74706224E12, 63.99000000000004], [1.74706386E12, 46.04000000000002], [1.74706326E12, 67.0], [1.74706548E12, 67.0], [1.74706218E12, 55.0], [1.7470644E12, 73.60000000000002], [1.7470638E12, 71.83999999999992], [1.74706542E12, 180.0], [1.74706338E12, 51.16999999999996], [1.7470656E12, 50.0], [1.747065E12, 48.68000000000001], [1.74706278E12, 53.47999999999999], [1.747062E12, 56.730000000000004], [1.74706362E12, 55.39999999999998], [1.74706302E12, 59.04000000000002], [1.74706524E12, 62.28], [1.7470632E12, 52.16], [1.74706482E12, 63.79999999999998], [1.7470626E12, 68.03], [1.74706422E12, 79.60999999999993], [1.74706314E12, 52.0], [1.74706536E12, 70.00000000000006], [1.74706254E12, 63.04999999999998], [1.74706476E12, 64.52999999999997], [1.74706272E12, 68.51999999999998], [1.74706434E12, 62.129999999999995], [1.74706212E12, 74.68000000000006], [1.74706374E12, 55.30000000000004], [1.74706296E12, 57.69999999999999], [1.74706458E12, 52.24000000000001], [1.74706236E12, 71.75000000000003], [1.74706398E12, 63.64000000000007], [1.74706578E12, 54.0], [1.74706194E12, 76.54999999999998], [1.74706416E12, 55.900000000000034], [1.74706356E12, 63.000000000000114], [1.74706518E12, 50.27000000000001], [1.74706248E12, 52.349999999999994], [1.7470641E12, 64.29999999999993], [1.74706188E12, 68.0], [1.7470635E12, 76.09999999999997], [1.74706572E12, 40.0], [1.74706368E12, 51.559999999999945], [1.7470653E12, 57.99000000000004], [1.74706308E12, 67.38999999999999], [1.7470647E12, 55.150000000000006], [1.74706392E12, 62.75], [1.74706554E12, 46.0], [1.74706332E12, 56.04999999999998], [1.74706494E12, 53.0], [1.7470629E12, 58.30000000000001], [1.74706512E12, 51.360000000000014], [1.7470623E12, 61.34], [1.74706452E12, 66.20999999999984]], "isOverall": false, "label": "99th percentile", "isController": false}, {"data": [[1.74706344E12, 4.0], [1.74706506E12, 4.0], [1.74706284E12, 4.0], [1.74706446E12, 4.0], [1.74706242E12, 5.0], [1.74706464E12, 4.0], [1.74706566E12, 4.0], [1.74706404E12, 4.0], [1.74706182E12, 5.0], [1.74706266E12, 4.0], [1.74706488E12, 5.0], [1.74706206E12, 5.0], [1.74706428E12, 5.0], [1.74706224E12, 5.0], [1.74706386E12, 4.0], [1.74706326E12, 4.0], [1.74706548E12, 4.0], [1.74706218E12, 5.0], [1.7470644E12, 5.0], [1.7470638E12, 5.0], [1.74706542E12, 6.0], [1.74706338E12, 4.0], [1.7470656E12, 3.0], [1.747065E12, 4.0], [1.74706278E12, 5.0], [1.747062E12, 5.0], [1.74706362E12, 5.0], [1.74706302E12, 5.0], [1.74706524E12, 5.0], [1.7470632E12, 4.0], [1.74706482E12, 4.0], [1.7470626E12, 4.0], [1.74706422E12, 4.0], [1.74706314E12, 5.0], [1.74706536E12, 4.0], [1.74706254E12, 5.0], [1.74706476E12, 5.0], [1.74706272E12, 4.0], [1.74706434E12, 4.0], [1.74706212E12, 4.0], [1.74706374E12, 5.0], [1.74706296E12, 4.0], [1.74706458E12, 5.0], [1.74706236E12, 4.0], [1.74706398E12, 5.0], [1.74706578E12, 41.0], [1.74706194E12, 5.0], [1.74706416E12, 5.0], [1.74706356E12, 4.0], [1.74706518E12, 4.0], [1.74706248E12, 5.0], [1.7470641E12, 4.0], [1.74706188E12, 5.0], [1.7470635E12, 5.0], [1.74706572E12, 3.0], [1.74706368E12, 5.0], [1.7470653E12, 4.0], [1.74706308E12, 4.0], [1.7470647E12, 4.0], [1.74706392E12, 4.0], [1.74706554E12, 3.0], [1.74706332E12, 5.0], [1.74706494E12, 4.0], [1.7470629E12, 4.0], [1.74706512E12, 4.0], [1.7470623E12, 5.0], [1.74706452E12, 4.0]], "isOverall": false, "label": "Median", "isController": false}, {"data": [[1.74706344E12, 49.099999999999966], [1.74706506E12, 45.0], [1.74706284E12, 47.0], [1.74706446E12, 48.29999999999998], [1.74706242E12, 49.349999999999994], [1.74706464E12, 46.0], [1.74706566E12, 41.8], [1.74706404E12, 46.25], [1.74706182E12, 44.0], [1.74706266E12, 49.19999999999999], [1.74706488E12, 48.900000000000006], [1.74706206E12, 50.69999999999999], [1.74706428E12, 45.900000000000006], [1.74706224E12, 46.0], [1.74706386E12, 42.099999999999994], [1.74706326E12, 48.75], [1.74706548E12, 58.099999999999994], [1.74706218E12, 49.75], [1.7470644E12, 45.5], [1.7470638E12, 49.0], [1.74706542E12, 72.0], [1.74706338E12, 45.94999999999999], [1.7470656E12, 41.0], [1.747065E12, 44.0], [1.74706278E12, 44.400000000000006], [1.747062E12, 50.29999999999998], [1.74706362E12, 45.0], [1.74706302E12, 48.0], [1.74706524E12, 47.400000000000006], [1.7470632E12, 46.0], [1.74706482E12, 44.0], [1.7470626E12, 47.299999999999955], [1.74706422E12, 53.89999999999998], [1.74706314E12, 44.099999999999994], [1.74706536E12, 48.5], [1.74706254E12, 49.75], [1.74706476E12, 47.54999999999998], [1.74706272E12, 48.0], [1.74706434E12, 46.650000000000006], [1.74706212E12, 48.0], [1.74706374E12, 47.0], [1.74706296E12, 44.75], [1.74706458E12, 47.54999999999998], [1.74706236E12, 49.75], [1.74706398E12, 47.0], [1.74706578E12, 54.0], [1.74706194E12, 57.54999999999998], [1.74706416E12, 45.0], [1.74706356E12, 45.0], [1.74706518E12, 45.0], [1.74706248E12, 48.0], [1.7470641E12, 47.29999999999998], [1.74706188E12, 52.0], [1.7470635E12, 50.04999999999998], [1.74706572E12, 40.0], [1.74706368E12, 43.94999999999999], [1.7470653E12, 49.0], [1.74706308E12, 51.60000000000002], [1.7470647E12, 46.75], [1.74706392E12, 47.0], [1.74706554E12, 43.19999999999999], [1.74706332E12, 48.75], [1.74706494E12, 44.0], [1.7470629E12, 49.0], [1.74706512E12, 42.44999999999999], [1.7470623E12, 51.0], [1.74706452E12, 45.849999999999994]], "isOverall": false, "label": "95th percentile", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74706578E12, "title": "Response Time Percentiles Over Time (successful requests only)"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true,
                        fill: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Response Time in ms",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: '#legendResponseTimePercentilesOverTime'
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s : at %x Response time was %y ms"
                }
            };
        },
        createGraph: function () {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesResponseTimePercentilesOverTime"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotResponseTimePercentilesOverTime"), dataset, options);
            // setup overview
            $.plot($("#overviewResponseTimePercentilesOverTime"), dataset, prepareOverviewOptions(options));
        }
};

// Response Time Percentiles Over Time
function refreshResponseTimePercentilesOverTime(fixTimestamps) {
    var infos = responseTimePercentilesOverTimeInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotResponseTimePercentilesOverTime"))) {
        infos.createGraph();
    }else {
        var choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimePercentilesOverTime", "#overviewResponseTimePercentilesOverTime");
        $('#footerResponseTimePercentilesOverTime .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var responseTimeVsRequestInfos = {
    data: {"result": {"minY": 3.0, "minX": 1.0, "maxY": 38.0, "series": [{"data": [[2.0, 38.0], [8.0, 4.0], [9.0, 4.0], [10.0, 5.0], [11.0, 4.0], [3.0, 37.0], [12.0, 3.0], [13.0, 3.0], [14.0, 3.5], [15.0, 3.0], [1.0, 37.0], [4.0, 36.5], [16.0, 3.0], [18.0, 3.0], [19.0, 4.0], [5.0, 5.0], [20.0, 3.0], [6.0, 3.0], [25.0, 3.0], [7.0, 3.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 25.0, "title": "Response Time Vs Request"}},
    getOptions: function() {
        return {
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Response Time in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: {
                noColumns: 2,
                show: true,
                container: '#legendResponseTimeVsRequest'
            },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median response time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesResponseTimeVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotResponseTimeVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewResponseTimeVsRequest"), dataset, prepareOverviewOptions(options));

    }
};

// Response Time vs Request
function refreshResponseTimeVsRequest() {
    var infos = responseTimeVsRequestInfos;
    prepareSeries(infos.data);
    if (isGraph($("#flotResponseTimeVsRequest"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesResponseTimeVsRequest");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotResponseTimeVsRequest", "#overviewResponseTimeVsRequest");
        $('#footerResponseRimeVsRequest .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};


var latenciesVsRequestInfos = {
    data: {"result": {"minY": 2.5, "minX": 1.0, "maxY": 4.0, "series": [{"data": [[2.0, 4.0], [8.0, 3.0], [9.0, 3.0], [10.0, 3.0], [11.0, 3.0], [3.0, 3.0], [12.0, 3.0], [13.0, 3.0], [14.0, 3.0], [15.0, 3.0], [1.0, 4.0], [4.0, 3.0], [16.0, 3.0], [18.0, 2.5], [19.0, 3.0], [5.0, 3.0], [20.0, 3.0], [6.0, 3.0], [25.0, 3.0], [7.0, 3.0]], "isOverall": false, "label": "Successes", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 1000, "maxX": 25.0, "title": "Latencies Vs Request"}},
    getOptions: function() {
        return{
            series: {
                lines: {
                    show: false
                },
                points: {
                    show: true
                }
            },
            xaxis: {
                axisLabel: "Global number of requests per second",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            yaxis: {
                axisLabel: "Median Latency in ms",
                axisLabelUseCanvas: true,
                axisLabelFontSizePixels: 12,
                axisLabelFontFamily: 'Verdana, Arial',
                axisLabelPadding: 20,
            },
            legend: { noColumns: 2,show: true, container: '#legendLatencyVsRequest' },
            selection: {
                mode: 'xy'
            },
            grid: {
                hoverable: true // IMPORTANT! this is needed for tooltip to work
            },
            tooltip: true,
            tooltipOpts: {
                content: "%s : Median Latency time at %x req/s was %y ms"
            },
            colors: ["#9ACD32", "#FF6347"]
        };
    },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesLatencyVsRequest"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotLatenciesVsRequest"), dataset, options);
        // setup overview
        $.plot($("#overviewLatenciesVsRequest"), dataset, prepareOverviewOptions(options));
    }
};

// Latencies vs Request
function refreshLatenciesVsRequest() {
        var infos = latenciesVsRequestInfos;
        prepareSeries(infos.data);
        if(isGraph($("#flotLatenciesVsRequest"))){
            infos.createGraph();
        }else{
            var choiceContainer = $("#choicesLatencyVsRequest");
            createLegend(choiceContainer, infos);
            infos.createGraph();
            setGraphZoomable("#flotLatenciesVsRequest", "#overviewLatenciesVsRequest");
            $('#footerLatenciesVsRequest .legendColorBox > div').each(function(i){
                $(this).clone().prependTo(choiceContainer.find("li").eq(i));
            });
        }
};

var hitsPerSecondInfos = {
        data: {"result": {"minY": 0.05, "minX": 1.74706182E12, "maxY": 3.65, "series": [{"data": [[1.74706344E12, 2.8], [1.74706506E12, 3.1666666666666665], [1.74706284E12, 3.1], [1.74706446E12, 2.8833333333333333], [1.74706242E12, 2.8666666666666667], [1.74706464E12, 2.95], [1.74706566E12, 0.38333333333333336], [1.74706404E12, 2.9], [1.74706182E12, 1.2], [1.74706266E12, 3.25], [1.74706488E12, 2.683333333333333], [1.74706206E12, 2.4166666666666665], [1.74706428E12, 2.683333333333333], [1.74706224E12, 2.7666666666666666], [1.74706386E12, 3.283333333333333], [1.74706326E12, 3.066666666666667], [1.74706548E12, 1.3], [1.74706218E12, 3.066666666666667], [1.7470644E12, 2.816666666666667], [1.7470638E12, 2.933333333333333], [1.74706542E12, 1.6333333333333333], [1.74706338E12, 2.6666666666666665], [1.7470656E12, 0.65], [1.747065E12, 2.75], [1.74706278E12, 2.5166666666666666], [1.747062E12, 2.1], [1.74706362E12, 2.9833333333333334], [1.74706302E12, 2.75], [1.74706524E12, 2.85], [1.7470632E12, 3.05], [1.74706482E12, 3.05], [1.7470626E12, 3.2666666666666666], [1.74706422E12, 2.8333333333333335], [1.74706314E12, 2.95], [1.74706536E12, 3.4833333333333334], [1.74706254E12, 2.7333333333333334], [1.74706476E12, 2.466666666666667], [1.74706272E12, 3.1], [1.74706434E12, 3.1], [1.74706212E12, 2.966666666666667], [1.74706374E12, 2.55], [1.74706296E12, 2.7333333333333334], [1.74706458E12, 2.8], [1.74706236E12, 3.066666666666667], [1.74706398E12, 2.45], [1.74706578E12, 0.05], [1.74706194E12, 1.7], [1.74706416E12, 3.066666666666667], [1.74706356E12, 3.65], [1.74706518E12, 2.8666666666666667], [1.74706248E12, 2.7333333333333334], [1.7470641E12, 3.55], [1.74706188E12, 1.4833333333333334], [1.7470635E12, 2.6333333333333333], [1.74706572E12, 0.23333333333333334], [1.74706368E12, 2.6666666666666665], [1.7470653E12, 2.7666666666666666], [1.74706308E12, 3.1], [1.7470647E12, 3.066666666666667], [1.74706392E12, 2.9], [1.74706554E12, 0.7833333333333333], [1.74706332E12, 2.7333333333333334], [1.74706494E12, 2.9833333333333334], [1.7470629E12, 3.066666666666667], [1.74706512E12, 3.1666666666666665], [1.7470623E12, 2.75], [1.74706452E12, 3.033333333333333]], "isOverall": false, "label": "hitsPerSecond", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74706578E12, "title": "Hits Per Second"}},
        getOptions: function() {
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of hits / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendHitsPerSecond"
                },
                selection: {
                    mode : 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y.2 hits/sec"
                }
            };
        },
        createGraph: function createGraph() {
            var data = this.data;
            var dataset = prepareData(data.result.series, $("#choicesHitsPerSecond"));
            var options = this.getOptions();
            prepareOptions(options, data);
            $.plot($("#flotHitsPerSecond"), dataset, options);
            // setup overview
            $.plot($("#overviewHitsPerSecond"), dataset, prepareOverviewOptions(options));
        }
};

// Hits per second
function refreshHitsPerSecond(fixTimestamps) {
    var infos = hitsPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if (isGraph($("#flotHitsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesHitsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotHitsPerSecond", "#overviewHitsPerSecond");
        $('#footerHitsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
}

var codesPerSecondInfos = {
        data: {"result": {"minY": 0.05, "minX": 1.74706182E12, "maxY": 3.65, "series": [{"data": [[1.74706344E12, 2.8], [1.74706506E12, 3.1666666666666665], [1.74706284E12, 3.1], [1.74706446E12, 2.8833333333333333], [1.74706242E12, 2.8666666666666667], [1.74706464E12, 2.95], [1.74706566E12, 0.38333333333333336], [1.74706404E12, 2.9], [1.74706182E12, 1.2], [1.74706266E12, 3.25], [1.74706488E12, 2.683333333333333], [1.74706206E12, 2.4166666666666665], [1.74706428E12, 2.683333333333333], [1.74706224E12, 2.7666666666666666], [1.74706386E12, 3.283333333333333], [1.74706326E12, 3.066666666666667], [1.74706548E12, 1.3], [1.74706218E12, 3.066666666666667], [1.7470644E12, 2.816666666666667], [1.7470638E12, 2.933333333333333], [1.74706542E12, 1.6333333333333333], [1.74706338E12, 2.6666666666666665], [1.7470656E12, 0.65], [1.747065E12, 2.75], [1.74706278E12, 2.5166666666666666], [1.747062E12, 2.1], [1.74706362E12, 2.9833333333333334], [1.74706302E12, 2.75], [1.74706524E12, 2.85], [1.7470632E12, 3.05], [1.74706482E12, 3.05], [1.7470626E12, 3.2666666666666666], [1.74706422E12, 2.8333333333333335], [1.74706314E12, 2.95], [1.74706536E12, 3.4833333333333334], [1.74706254E12, 2.7333333333333334], [1.74706476E12, 2.466666666666667], [1.74706272E12, 3.1], [1.74706434E12, 3.1], [1.74706212E12, 2.966666666666667], [1.74706374E12, 2.55], [1.74706296E12, 2.7333333333333334], [1.74706458E12, 2.8], [1.74706236E12, 3.066666666666667], [1.74706398E12, 2.45], [1.74706578E12, 0.05], [1.74706194E12, 1.7], [1.74706416E12, 3.066666666666667], [1.74706356E12, 3.65], [1.74706518E12, 2.8666666666666667], [1.74706248E12, 2.7333333333333334], [1.7470641E12, 3.55], [1.74706188E12, 1.4833333333333334], [1.7470635E12, 2.6333333333333333], [1.74706572E12, 0.23333333333333334], [1.74706368E12, 2.6666666666666665], [1.7470653E12, 2.7666666666666666], [1.74706308E12, 3.1], [1.7470647E12, 3.066666666666667], [1.74706392E12, 2.9], [1.74706554E12, 0.7833333333333333], [1.74706332E12, 2.7333333333333334], [1.74706494E12, 2.9833333333333334], [1.7470629E12, 3.066666666666667], [1.74706512E12, 3.1666666666666665], [1.7470623E12, 2.75], [1.74706452E12, 3.033333333333333]], "isOverall": false, "label": "200", "isController": false}], "supportsControllersDiscrimination": false, "granularity": 60000, "maxX": 1.74706578E12, "title": "Codes Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of responses / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendCodesPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "Number of Response Codes %s at %x was %y.2 responses / sec"
                }
            };
        },
    createGraph: function() {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesCodesPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotCodesPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewCodesPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Codes per second
function refreshCodesPerSecond(fixTimestamps) {
    var infos = codesPerSecondInfos;
    prepareSeries(infos.data);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotCodesPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesCodesPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotCodesPerSecond", "#overviewCodesPerSecond");
        $('#footerCodesPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var transactionsPerSecondInfos = {
        data: {"result": {"minY": 0.016666666666666666, "minX": 1.74706182E12, "maxY": 1.25, "series": [{"data": [[1.74706344E12, 0.45], [1.74706506E12, 0.43333333333333335], [1.74706284E12, 0.4666666666666667], [1.74706446E12, 0.43333333333333335], [1.74706242E12, 0.4], [1.74706464E12, 0.38333333333333336], [1.74706404E12, 0.4166666666666667], [1.74706182E12, 0.21666666666666667], [1.74706266E12, 0.48333333333333334], [1.74706488E12, 0.4166666666666667], [1.74706206E12, 0.4166666666666667], [1.74706428E12, 0.4166666666666667], [1.74706224E12, 0.4], [1.74706386E12, 0.45], [1.74706326E12, 0.4666666666666667], [1.74706218E12, 0.45], [1.7470644E12, 0.4], [1.7470638E12, 0.43333333333333335], [1.74706542E12, 0.21666666666666667], [1.74706338E12, 0.4166666666666667], [1.747065E12, 0.4166666666666667], [1.74706278E12, 0.43333333333333335], [1.747062E12, 0.38333333333333336], [1.74706362E12, 0.4166666666666667], [1.74706302E12, 0.4166666666666667], [1.74706524E12, 0.43333333333333335], [1.7470632E12, 0.36666666666666664], [1.74706482E12, 0.4666666666666667], [1.7470626E12, 0.38333333333333336], [1.74706422E12, 0.4166666666666667], [1.74706314E12, 0.43333333333333335], [1.74706536E12, 0.43333333333333335], [1.74706254E12, 0.4], [1.74706476E12, 0.4], [1.74706272E12, 0.3333333333333333], [1.74706434E12, 0.4], [1.74706212E12, 0.4], [1.74706374E12, 0.36666666666666664], [1.74706296E12, 0.38333333333333336], [1.74706458E12, 0.45], [1.74706236E12, 0.43333333333333335], [1.74706398E12, 0.4], [1.74706194E12, 0.48333333333333334], [1.74706416E12, 0.43333333333333335], [1.74706356E12, 0.43333333333333335], [1.74706518E12, 0.43333333333333335], [1.74706248E12, 0.45], [1.7470641E12, 0.43333333333333335], [1.74706188E12, 0.35], [1.7470635E12, 0.38333333333333336], [1.74706368E12, 0.45], [1.7470653E12, 0.4], [1.74706308E12, 0.43333333333333335], [1.7470647E12, 0.38333333333333336], [1.74706392E12, 0.38333333333333336], [1.74706332E12, 0.4], [1.74706494E12, 0.4], [1.7470629E12, 0.4], [1.74706512E12, 0.38333333333333336], [1.7470623E12, 0.4166666666666667], [1.74706452E12, 0.4166666666666667]], "isOverall": false, "label": "Search-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Cover 3-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Cover 1-success", "isController": false}, {"data": [[1.74706344E12, 0.11666666666666667], [1.74706506E12, 0.15], [1.74706284E12, 0.16666666666666666], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.16666666666666666], [1.74706566E12, 0.03333333333333333], [1.74706404E12, 0.15], [1.74706266E12, 0.18333333333333332], [1.74706488E12, 0.08333333333333333], [1.74706206E12, 0.08333333333333333], [1.74706428E12, 0.11666666666666667], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.21666666666666667], [1.74706326E12, 0.15], [1.74706548E12, 0.13333333333333333], [1.74706218E12, 0.11666666666666667], [1.7470644E12, 0.11666666666666667], [1.7470638E12, 0.11666666666666667], [1.74706542E12, 0.11666666666666667], [1.74706338E12, 0.11666666666666667], [1.7470656E12, 0.06666666666666667], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.06666666666666667], [1.747062E12, 0.05], [1.74706362E12, 0.13333333333333333], [1.74706302E12, 0.11666666666666667], [1.74706524E12, 0.1], [1.7470632E12, 0.18333333333333332], [1.74706482E12, 0.16666666666666666], [1.7470626E12, 0.2], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.15], [1.74706536E12, 0.21666666666666667], [1.74706254E12, 0.11666666666666667], [1.74706476E12, 0.08333333333333333], [1.74706272E12, 0.16666666666666666], [1.74706434E12, 0.16666666666666666], [1.74706212E12, 0.18333333333333332], [1.74706374E12, 0.1], [1.74706296E12, 0.15], [1.74706458E12, 0.11666666666666667], [1.74706236E12, 0.16666666666666666], [1.74706398E12, 0.05], [1.74706416E12, 0.13333333333333333], [1.74706356E12, 0.25], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.1], [1.7470641E12, 0.23333333333333334], [1.7470635E12, 0.1], [1.74706572E12, 0.03333333333333333], [1.74706368E12, 0.1], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.13333333333333333], [1.7470647E12, 0.15], [1.74706392E12, 0.15], [1.74706554E12, 0.08333333333333333], [1.74706332E12, 0.1], [1.74706494E12, 0.16666666666666666], [1.7470629E12, 0.13333333333333333], [1.74706512E12, 0.18333333333333332], [1.7470623E12, 0.11666666666666667], [1.74706452E12, 0.15]], "isOverall": false, "label": "Albums page-success", "isController": true}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Cover 4-success", "isController": false}, {"data": [[1.74706344E12, 0.5833333333333334], [1.74706506E12, 0.75], [1.74706284E12, 0.8333333333333334], [1.74706446E12, 0.6666666666666666], [1.74706242E12, 0.6666666666666666], [1.74706464E12, 0.8333333333333334], [1.74706566E12, 0.16666666666666666], [1.74706404E12, 0.75], [1.74706266E12, 0.9166666666666666], [1.74706488E12, 0.4166666666666667], [1.74706206E12, 0.4166666666666667], [1.74706428E12, 0.5833333333333334], [1.74706224E12, 0.6666666666666666], [1.74706386E12, 1.0833333333333333], [1.74706326E12, 0.75], [1.74706548E12, 0.6666666666666666], [1.74706218E12, 0.5833333333333334], [1.7470644E12, 0.5833333333333334], [1.7470638E12, 0.5833333333333334], [1.74706542E12, 0.5833333333333334], [1.74706338E12, 0.5833333333333334], [1.7470656E12, 0.3333333333333333], [1.747065E12, 0.6666666666666666], [1.74706278E12, 0.3333333333333333], [1.747062E12, 0.25], [1.74706362E12, 0.6666666666666666], [1.74706302E12, 0.5833333333333334], [1.74706524E12, 0.5], [1.7470632E12, 0.9166666666666666], [1.74706482E12, 0.8333333333333334], [1.7470626E12, 1.0], [1.74706422E12, 0.6666666666666666], [1.74706314E12, 0.75], [1.74706536E12, 1.0833333333333333], [1.74706254E12, 0.5833333333333334], [1.74706476E12, 0.4166666666666667], [1.74706272E12, 0.8333333333333334], [1.74706434E12, 0.8333333333333334], [1.74706212E12, 0.9166666666666666], [1.74706374E12, 0.5], [1.74706296E12, 0.75], [1.74706458E12, 0.5833333333333334], [1.74706236E12, 0.8333333333333334], [1.74706398E12, 0.25], [1.74706416E12, 0.6666666666666666], [1.74706356E12, 1.25], [1.74706518E12, 0.6666666666666666], [1.74706248E12, 0.5], [1.7470641E12, 1.1666666666666667], [1.7470635E12, 0.5], [1.74706572E12, 0.16666666666666666], [1.74706368E12, 0.5], [1.7470653E12, 0.6666666666666666], [1.74706308E12, 0.6666666666666666], [1.7470647E12, 0.75], [1.74706392E12, 0.75], [1.74706554E12, 0.4166666666666667], [1.74706332E12, 0.5], [1.74706494E12, 0.8333333333333334], [1.7470629E12, 0.6666666666666666], [1.74706512E12, 0.9166666666666666], [1.7470623E12, 0.5833333333333334], [1.74706452E12, 0.75]], "isOverall": false, "label": "Get paths to songs from each album-success", "isController": false}, {"data": [[1.74706344E12, 0.3333333333333333], [1.74706506E12, 0.48333333333333334], [1.74706284E12, 0.38333333333333336], [1.74706446E12, 0.4166666666666667], [1.74706242E12, 0.43333333333333335], [1.74706464E12, 0.36666666666666664], [1.74706404E12, 0.4], [1.74706182E12, 0.05], [1.74706266E12, 0.45], [1.74706488E12, 0.5], [1.74706206E12, 0.45], [1.74706428E12, 0.38333333333333336], [1.74706224E12, 0.3333333333333333], [1.74706386E12, 0.31666666666666665], [1.74706326E12, 0.36666666666666664], [1.74706548E12, 0.2], [1.74706218E12, 0.55], [1.7470644E12, 0.45], [1.7470638E12, 0.5333333333333333], [1.74706542E12, 0.3333333333333333], [1.74706338E12, 0.35], [1.7470656E12, 0.05], [1.747065E12, 0.38333333333333336], [1.74706278E12, 0.38333333333333336], [1.747062E12, 0.35], [1.74706362E12, 0.35], [1.74706302E12, 0.45], [1.74706524E12, 0.5], [1.7470632E12, 0.4166666666666667], [1.74706482E12, 0.36666666666666664], [1.7470626E12, 0.48333333333333334], [1.74706422E12, 0.43333333333333335], [1.74706314E12, 0.35], [1.74706536E12, 0.4], [1.74706254E12, 0.31666666666666665], [1.74706476E12, 0.4], [1.74706272E12, 0.4166666666666667], [1.74706434E12, 0.4166666666666667], [1.74706212E12, 0.36666666666666664], [1.74706374E12, 0.4166666666666667], [1.74706296E12, 0.2833333333333333], [1.74706458E12, 0.4666666666666667], [1.74706236E12, 0.38333333333333336], [1.74706398E12, 0.43333333333333335], [1.74706194E12, 0.2833333333333333], [1.74706416E12, 0.4166666666666667], [1.74706356E12, 0.4166666666666667], [1.74706518E12, 0.38333333333333336], [1.74706248E12, 0.5], [1.7470641E12, 0.4666666666666667], [1.74706188E12, 0.2], [1.7470635E12, 0.5666666666666667], [1.74706368E12, 0.4166666666666667], [1.7470653E12, 0.4666666666666667], [1.74706308E12, 0.5], [1.7470647E12, 0.4], [1.74706392E12, 0.36666666666666664], [1.74706554E12, 0.08333333333333333], [1.74706332E12, 0.5], [1.74706494E12, 0.4], [1.7470629E12, 0.5666666666666667], [1.74706512E12, 0.38333333333333336], [1.7470623E12, 0.4], [1.74706452E12, 0.38333333333333336]], "isOverall": false, "label": "Play songs from home page-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Initial song-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Cover 5-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Home page-success", "isController": true}, {"data": [[1.74706344E12, 0.26666666666666666], [1.74706506E12, 0.3], [1.74706284E12, 0.31666666666666665], [1.74706446E12, 0.3], [1.74706242E12, 0.3], [1.74706464E12, 0.26666666666666666], [1.74706566E12, 0.18333333333333332], [1.74706404E12, 0.25], [1.74706266E12, 0.2833333333333333], [1.74706488E12, 0.21666666666666667], [1.74706206E12, 0.11666666666666667], [1.74706428E12, 0.25], [1.74706224E12, 0.3], [1.74706386E12, 0.2833333333333333], [1.74706326E12, 0.2833333333333333], [1.74706548E12, 0.3], [1.74706218E12, 0.31666666666666665], [1.7470644E12, 0.3333333333333333], [1.7470638E12, 0.21666666666666667], [1.74706542E12, 0.26666666666666666], [1.74706338E12, 0.26666666666666666], [1.7470656E12, 0.2], [1.747065E12, 0.21666666666666667], [1.74706278E12, 0.36666666666666664], [1.747062E12, 0.016666666666666666], [1.74706362E12, 0.36666666666666664], [1.74706302E12, 0.25], [1.74706524E12, 0.26666666666666666], [1.7470632E12, 0.23333333333333334], [1.74706482E12, 0.2833333333333333], [1.7470626E12, 0.26666666666666666], [1.74706422E12, 0.25], [1.74706314E12, 0.3333333333333333], [1.74706536E12, 0.4166666666666667], [1.74706254E12, 0.26666666666666666], [1.74706476E12, 0.23333333333333334], [1.74706272E12, 0.3], [1.74706434E12, 0.23333333333333334], [1.74706212E12, 0.16666666666666666], [1.74706374E12, 0.23333333333333334], [1.74706296E12, 0.23333333333333334], [1.74706458E12, 0.25], [1.74706236E12, 0.2], [1.74706398E12, 0.26666666666666666], [1.74706578E12, 0.05], [1.74706416E12, 0.36666666666666664], [1.74706356E12, 0.36666666666666664], [1.74706518E12, 0.31666666666666665], [1.74706248E12, 0.25], [1.7470641E12, 0.31666666666666665], [1.7470635E12, 0.15], [1.74706572E12, 0.03333333333333333], [1.74706368E12, 0.26666666666666666], [1.7470653E12, 0.16666666666666666], [1.74706308E12, 0.31666666666666665], [1.7470647E12, 0.3333333333333333], [1.74706392E12, 0.31666666666666665], [1.74706554E12, 0.2], [1.74706332E12, 0.3], [1.74706494E12, 0.25], [1.7470629E12, 0.25], [1.74706512E12, 0.36666666666666664], [1.7470623E12, 0.3], [1.74706452E12, 0.2833333333333333]], "isOverall": false, "label": "Play songs from albums-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Trending playlist-success", "isController": false}, {"data": [[1.74706344E12, 0.15], [1.74706506E12, 0.15], [1.74706284E12, 0.13333333333333333], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.13333333333333333], [1.74706404E12, 0.13333333333333333], [1.74706182E12, 0.13333333333333333], [1.74706266E12, 0.13333333333333333], [1.74706488E12, 0.15], [1.74706206E12, 0.13333333333333333], [1.74706428E12, 0.13333333333333333], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.13333333333333333], [1.74706326E12, 0.15], [1.74706218E12, 0.15], [1.7470644E12, 0.13333333333333333], [1.7470638E12, 0.15], [1.74706542E12, 0.016666666666666666], [1.74706338E12, 0.13333333333333333], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.13333333333333333], [1.747062E12, 0.15], [1.74706362E12, 0.15], [1.74706302E12, 0.13333333333333333], [1.74706524E12, 0.15], [1.7470632E12, 0.13333333333333333], [1.74706482E12, 0.13333333333333333], [1.7470626E12, 0.13333333333333333], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.13333333333333333], [1.74706536E12, 0.13333333333333333], [1.74706254E12, 0.15], [1.74706476E12, 0.13333333333333333], [1.74706272E12, 0.15], [1.74706434E12, 0.15], [1.74706212E12, 0.13333333333333333], [1.74706374E12, 0.13333333333333333], [1.74706296E12, 0.13333333333333333], [1.74706458E12, 0.13333333333333333], [1.74706236E12, 0.15], [1.74706398E12, 0.15], [1.74706194E12, 0.13333333333333333], [1.74706416E12, 0.15], [1.74706356E12, 0.13333333333333333], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.13333333333333333], [1.7470641E12, 0.13333333333333333], [1.74706188E12, 0.13333333333333333], [1.7470635E12, 0.13333333333333333], [1.74706368E12, 0.13333333333333333], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.15], [1.7470647E12, 0.15], [1.74706392E12, 0.13333333333333333], [1.74706332E12, 0.13333333333333333], [1.74706494E12, 0.13333333333333333], [1.7470629E12, 0.15], [1.74706512E12, 0.13333333333333333], [1.7470623E12, 0.13333333333333333], [1.74706452E12, 0.15]], "isOverall": false, "label": "Cover 2-success", "isController": false}, {"data": [[1.74706344E12, 0.11666666666666667], [1.74706506E12, 0.15], [1.74706284E12, 0.16666666666666666], [1.74706446E12, 0.13333333333333333], [1.74706242E12, 0.13333333333333333], [1.74706464E12, 0.16666666666666666], [1.74706566E12, 0.03333333333333333], [1.74706404E12, 0.15], [1.74706266E12, 0.18333333333333332], [1.74706488E12, 0.08333333333333333], [1.74706206E12, 0.08333333333333333], [1.74706428E12, 0.11666666666666667], [1.74706224E12, 0.13333333333333333], [1.74706386E12, 0.21666666666666667], [1.74706326E12, 0.15], [1.74706548E12, 0.13333333333333333], [1.74706218E12, 0.11666666666666667], [1.7470644E12, 0.11666666666666667], [1.7470638E12, 0.11666666666666667], [1.74706542E12, 0.11666666666666667], [1.74706338E12, 0.11666666666666667], [1.7470656E12, 0.06666666666666667], [1.747065E12, 0.13333333333333333], [1.74706278E12, 0.06666666666666667], [1.747062E12, 0.05], [1.74706362E12, 0.13333333333333333], [1.74706302E12, 0.11666666666666667], [1.74706524E12, 0.1], [1.7470632E12, 0.18333333333333332], [1.74706482E12, 0.16666666666666666], [1.7470626E12, 0.2], [1.74706422E12, 0.13333333333333333], [1.74706314E12, 0.15], [1.74706536E12, 0.21666666666666667], [1.74706254E12, 0.11666666666666667], [1.74706476E12, 0.08333333333333333], [1.74706272E12, 0.16666666666666666], [1.74706434E12, 0.16666666666666666], [1.74706212E12, 0.18333333333333332], [1.74706374E12, 0.1], [1.74706296E12, 0.15], [1.74706458E12, 0.11666666666666667], [1.74706236E12, 0.16666666666666666], [1.74706398E12, 0.05], [1.74706416E12, 0.13333333333333333], [1.74706356E12, 0.25], [1.74706518E12, 0.13333333333333333], [1.74706248E12, 0.1], [1.7470641E12, 0.23333333333333334], [1.7470635E12, 0.1], [1.74706572E12, 0.03333333333333333], [1.74706368E12, 0.1], [1.7470653E12, 0.13333333333333333], [1.74706308E12, 0.13333333333333333], [1.7470647E12, 0.15], [1.74706392E12, 0.15], [1.74706554E12, 0.08333333333333333], [1.74706332E12, 0.1], [1.74706494E12, 0.16666666666666666], [1.7470629E12, 0.13333333333333333], [1.74706512E12, 0.18333333333333332], [1.7470623E12, 0.11666666666666667], [1.74706452E12, 0.15]], "isOverall": false, "label": "Get all albums-success", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74706578E12, "title": "Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTransactionsPerSecond"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                }
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTransactionsPerSecond"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTransactionsPerSecond"), dataset, options);
        // setup overview
        $.plot($("#overviewTransactionsPerSecond"), dataset, prepareOverviewOptions(options));
    }
};

// Transactions per second
function refreshTransactionsPerSecond(fixTimestamps) {
    var infos = transactionsPerSecondInfos;
    prepareSeries(infos.data);
    if(infos.data.result.series.length == 0) {
        setEmptyGraph("#bodyTransactionsPerSecond");
        return;
    }
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTransactionsPerSecond"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTransactionsPerSecond");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTransactionsPerSecond", "#overviewTransactionsPerSecond");
        $('#footerTransactionsPerSecond .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

var totalTPSInfos = {
        data: {"result": {"minY": 0.05, "minX": 1.74706182E12, "maxY": 4.033333333333333, "series": [{"data": [[1.74706344E12, 3.066666666666667], [1.74706506E12, 3.466666666666667], [1.74706284E12, 3.4], [1.74706446E12, 3.15], [1.74706242E12, 3.1333333333333333], [1.74706464E12, 3.25], [1.74706566E12, 0.4166666666666667], [1.74706404E12, 3.183333333333333], [1.74706182E12, 1.3333333333333333], [1.74706266E12, 3.566666666666667], [1.74706488E12, 2.9166666666666665], [1.74706206E12, 2.6333333333333333], [1.74706428E12, 2.933333333333333], [1.74706224E12, 3.033333333333333], [1.74706386E12, 3.6333333333333333], [1.74706326E12, 3.3666666666666667], [1.74706548E12, 1.4333333333333333], [1.74706218E12, 3.3333333333333335], [1.7470644E12, 3.066666666666667], [1.7470638E12, 3.2], [1.74706542E12, 1.7666666666666666], [1.74706338E12, 2.9166666666666665], [1.7470656E12, 0.7166666666666667], [1.747065E12, 3.0166666666666666], [1.74706278E12, 2.716666666666667], [1.747062E12, 2.3], [1.74706362E12, 3.2666666666666666], [1.74706302E12, 3.0], [1.74706524E12, 3.1], [1.7470632E12, 3.3666666666666667], [1.74706482E12, 3.35], [1.7470626E12, 3.6], [1.74706422E12, 3.1], [1.74706314E12, 3.2333333333333334], [1.74706536E12, 3.8333333333333335], [1.74706254E12, 3.0], [1.74706476E12, 2.683333333333333], [1.74706272E12, 3.4166666666666665], [1.74706434E12, 3.4166666666666665], [1.74706212E12, 3.283333333333333], [1.74706374E12, 2.783333333333333], [1.74706296E12, 3.0166666666666666], [1.74706458E12, 3.05], [1.74706236E12, 3.3833333333333333], [1.74706398E12, 2.65], [1.74706578E12, 0.05], [1.74706194E12, 1.8333333333333333], [1.74706416E12, 3.35], [1.74706356E12, 4.033333333333333], [1.74706518E12, 3.1333333333333333], [1.74706248E12, 2.966666666666667], [1.7470641E12, 3.9166666666666665], [1.74706188E12, 1.6166666666666667], [1.7470635E12, 2.8666666666666667], [1.74706572E12, 0.26666666666666666], [1.74706368E12, 2.9], [1.7470653E12, 3.033333333333333], [1.74706308E12, 3.3833333333333333], [1.7470647E12, 3.3666666666666667], [1.74706392E12, 3.183333333333333], [1.74706554E12, 0.8666666666666667], [1.74706332E12, 2.966666666666667], [1.74706494E12, 3.283333333333333], [1.7470629E12, 3.35], [1.74706512E12, 3.4833333333333334], [1.7470623E12, 3.0], [1.74706452E12, 3.3333333333333335]], "isOverall": false, "label": "Transaction-success", "isController": false}, {"data": [], "isOverall": false, "label": "Transaction-failure", "isController": false}], "supportsControllersDiscrimination": true, "granularity": 60000, "maxX": 1.74706578E12, "title": "Total Transactions Per Second"}},
        getOptions: function(){
            return {
                series: {
                    lines: {
                        show: true
                    },
                    points: {
                        show: true
                    }
                },
                xaxis: {
                    mode: "time",
                    timeformat: getTimeFormat(this.data.result.granularity),
                    axisLabel: getElapsedTimeLabel(this.data.result.granularity),
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20,
                },
                yaxis: {
                    axisLabel: "Number of transactions / sec",
                    axisLabelUseCanvas: true,
                    axisLabelFontSizePixels: 12,
                    axisLabelFontFamily: 'Verdana, Arial',
                    axisLabelPadding: 20
                },
                legend: {
                    noColumns: 2,
                    show: true,
                    container: "#legendTotalTPS"
                },
                selection: {
                    mode: 'xy'
                },
                grid: {
                    hoverable: true // IMPORTANT! this is needed for tooltip to
                                    // work
                },
                tooltip: true,
                tooltipOpts: {
                    content: "%s at %x was %y transactions / sec"
                },
                colors: ["#9ACD32", "#FF6347"]
            };
        },
    createGraph: function () {
        var data = this.data;
        var dataset = prepareData(data.result.series, $("#choicesTotalTPS"));
        var options = this.getOptions();
        prepareOptions(options, data);
        $.plot($("#flotTotalTPS"), dataset, options);
        // setup overview
        $.plot($("#overviewTotalTPS"), dataset, prepareOverviewOptions(options));
    }
};

// Total Transactions per second
function refreshTotalTPS(fixTimestamps) {
    var infos = totalTPSInfos;
    // We want to ignore seriesFilter
    prepareSeries(infos.data, false, true);
    if(fixTimestamps) {
        fixTimeStamps(infos.data.result.series, 7200000);
    }
    if(isGraph($("#flotTotalTPS"))){
        infos.createGraph();
    }else{
        var choiceContainer = $("#choicesTotalTPS");
        createLegend(choiceContainer, infos);
        infos.createGraph();
        setGraphZoomable("#flotTotalTPS", "#overviewTotalTPS");
        $('#footerTotalTPS .legendColorBox > div').each(function(i){
            $(this).clone().prependTo(choiceContainer.find("li").eq(i));
        });
    }
};

// Collapse the graph matching the specified DOM element depending the collapsed
// status
function collapse(elem, collapsed){
    if(collapsed){
        $(elem).parent().find(".fa-chevron-up").removeClass("fa-chevron-up").addClass("fa-chevron-down");
    } else {
        $(elem).parent().find(".fa-chevron-down").removeClass("fa-chevron-down").addClass("fa-chevron-up");
        if (elem.id == "bodyBytesThroughputOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshBytesThroughputOverTime(true);
            }
            document.location.href="#bytesThroughputOverTime";
        } else if (elem.id == "bodyLatenciesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesOverTime(true);
            }
            document.location.href="#latenciesOverTime";
        } else if (elem.id == "bodyCustomGraph") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCustomGraph(true);
            }
            document.location.href="#responseCustomGraph";
        } else if (elem.id == "bodyConnectTimeOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshConnectTimeOverTime(true);
            }
            document.location.href="#connectTimeOverTime";
        } else if (elem.id == "bodyResponseTimePercentilesOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimePercentilesOverTime(true);
            }
            document.location.href="#responseTimePercentilesOverTime";
        } else if (elem.id == "bodyResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeDistribution();
            }
            document.location.href="#responseTimeDistribution" ;
        } else if (elem.id == "bodySyntheticResponseTimeDistribution") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshSyntheticResponseTimeDistribution();
            }
            document.location.href="#syntheticResponseTimeDistribution" ;
        } else if (elem.id == "bodyActiveThreadsOverTime") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshActiveThreadsOverTime(true);
            }
            document.location.href="#activeThreadsOverTime";
        } else if (elem.id == "bodyTimeVsThreads") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTimeVsThreads();
            }
            document.location.href="#timeVsThreads" ;
        } else if (elem.id == "bodyCodesPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshCodesPerSecond(true);
            }
            document.location.href="#codesPerSecond";
        } else if (elem.id == "bodyTransactionsPerSecond") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTransactionsPerSecond(true);
            }
            document.location.href="#transactionsPerSecond";
        } else if (elem.id == "bodyTotalTPS") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshTotalTPS(true);
            }
            document.location.href="#totalTPS";
        } else if (elem.id == "bodyResponseTimeVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshResponseTimeVsRequest();
            }
            document.location.href="#responseTimeVsRequest";
        } else if (elem.id == "bodyLatenciesVsRequest") {
            if (isGraph($(elem).find('.flot-chart-content')) == false) {
                refreshLatenciesVsRequest();
            }
            document.location.href="#latencyVsRequest";
        }
    }
}

/*
 * Activates or deactivates all series of the specified graph (represented by id parameter)
 * depending on checked argument.
 */
function toggleAll(id, checked){
    var placeholder = document.getElementById(id);

    var cases = $(placeholder).find(':checkbox');
    cases.prop('checked', checked);
    $(cases).parent().children().children().toggleClass("legend-disabled", !checked);

    var choiceContainer;
    if ( id == "choicesBytesThroughputOverTime"){
        choiceContainer = $("#choicesBytesThroughputOverTime");
        refreshBytesThroughputOverTime(false);
    } else if(id == "choicesResponseTimesOverTime"){
        choiceContainer = $("#choicesResponseTimesOverTime");
        refreshResponseTimeOverTime(false);
    }else if(id == "choicesResponseCustomGraph"){
        choiceContainer = $("#choicesResponseCustomGraph");
        refreshCustomGraph(false);
    } else if ( id == "choicesLatenciesOverTime"){
        choiceContainer = $("#choicesLatenciesOverTime");
        refreshLatenciesOverTime(false);
    } else if ( id == "choicesConnectTimeOverTime"){
        choiceContainer = $("#choicesConnectTimeOverTime");
        refreshConnectTimeOverTime(false);
    } else if ( id == "choicesResponseTimePercentilesOverTime"){
        choiceContainer = $("#choicesResponseTimePercentilesOverTime");
        refreshResponseTimePercentilesOverTime(false);
    } else if ( id == "choicesResponseTimePercentiles"){
        choiceContainer = $("#choicesResponseTimePercentiles");
        refreshResponseTimePercentiles();
    } else if(id == "choicesActiveThreadsOverTime"){
        choiceContainer = $("#choicesActiveThreadsOverTime");
        refreshActiveThreadsOverTime(false);
    } else if ( id == "choicesTimeVsThreads"){
        choiceContainer = $("#choicesTimeVsThreads");
        refreshTimeVsThreads();
    } else if ( id == "choicesSyntheticResponseTimeDistribution"){
        choiceContainer = $("#choicesSyntheticResponseTimeDistribution");
        refreshSyntheticResponseTimeDistribution();
    } else if ( id == "choicesResponseTimeDistribution"){
        choiceContainer = $("#choicesResponseTimeDistribution");
        refreshResponseTimeDistribution();
    } else if ( id == "choicesHitsPerSecond"){
        choiceContainer = $("#choicesHitsPerSecond");
        refreshHitsPerSecond(false);
    } else if(id == "choicesCodesPerSecond"){
        choiceContainer = $("#choicesCodesPerSecond");
        refreshCodesPerSecond(false);
    } else if ( id == "choicesTransactionsPerSecond"){
        choiceContainer = $("#choicesTransactionsPerSecond");
        refreshTransactionsPerSecond(false);
    } else if ( id == "choicesTotalTPS"){
        choiceContainer = $("#choicesTotalTPS");
        refreshTotalTPS(false);
    } else if ( id == "choicesResponseTimeVsRequest"){
        choiceContainer = $("#choicesResponseTimeVsRequest");
        refreshResponseTimeVsRequest();
    } else if ( id == "choicesLatencyVsRequest"){
        choiceContainer = $("#choicesLatencyVsRequest");
        refreshLatenciesVsRequest();
    }
    var color = checked ? "black" : "#818181";
    if(choiceContainer != null) {
        choiceContainer.find("label").each(function(){
            this.style.color = color;
        });
    }
}

